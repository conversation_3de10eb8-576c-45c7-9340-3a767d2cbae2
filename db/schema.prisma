generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = "*******************************************/flow_online"
}

model User {
  id                  Int                   @id @default(autoincrement())
  createdAt           DateTime              @default(now())
  updatedAt           DateTime              @updatedAt
  name                String?
  email               String                @unique
  hashedPassword      String?
  nickName            String?
  avatar              String?
  role                String                @default("USER")
  asset               Asset[]
  sessions            Session[]
  tokens              Token[]
  member              Member[]
  TechnicalArticle    TechnicalArticle[]
  FENews              FENews[]
  FEShare             FEShare[]
  H5                  H5[]
  Kunlun              Kunlun[]
  KunlunGroup         KunlunGroup[]
  KunlunMock          KunlunMock[]
  VscMaterial         VscMaterial[]
  appCredentials      AppCredential[]
  gptStores           GptStore[] // 添加反向关联
  emailStore          EmailStore[]
  TeamWeeklyReport    TeamWeeklyReport[]
  userProjectSettings UserProjectSettings[]
}

model Session {
  id                 Int       @id @default(autoincrement())
  createdAt          DateTime  @default(now())
  updatedAt          DateTime  @updatedAt
  expiresAt          DateTime?
  handle             String    @unique
  hashedSessionToken String?
  antiCSRFToken      String?
  publicData         String?
  privateData        String?
  userId             Int?
  user               User?     @relation(fields: [userId], references: [id])
}

model Token {
  id          Int      @id @default(autoincrement())
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
  hashedToken String
  type        String
  expiresAt   DateTime
  sentTo      String
  userId      Int
  user        User     @relation(fields: [userId], references: [id])

  @@unique([hashedToken, type])
}

model Asset {
  id        Int      @id @default(autoincrement())
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  url       String
  authorId  Int?
  author    User?    @relation(fields: [authorId], references: [id])
}

model Task {
  id        Int      @id @default(autoincrement())
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  data      Json?
  isDeleted Boolean? @default(false)
}

model Member {
  id       Int    @id @default(autoincrement())
  name     String
  authorId Int?
  author   User?  @relation(fields: [authorId], references: [id])
}

model ArticleSocial {
  id          Int      @id @default(autoincrement())
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
  thumb       String
  time        DateTime
  timeStr     String?
  content     String
  group       String?
  sortNum     String?
  content_en  String?
  isIgnoreDay Boolean? @default(false)
  isDeleted   Boolean? @default(false)
}

model ArticleNews {
  id          Int      @id @default(autoincrement())
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
  thumb       String
  time        DateTime
  info        String
  info_en     String?
  timeStr     String?
  content     String
  group       String?
  sortNum     String?
  from        String
  from_en     String?
  title       String
  title_en    String?
  link        String
  content_en  String?
  coverImage  String?
  isHomeNews  Boolean? @default(false)
  isfocusNews Boolean? @default(false)
  hide        Boolean? @default(false)
  hide_en     Boolean? @default(false)
  isDeleted   Boolean? @default(false)

}

model Kol {
  id              Int      @id @default(autoincrement())
  createdAt       DateTime @default(now())
  updatedAt       DateTime @updatedAt
  imgUrl          String?
  name            String
  name_en         String
  info            String
  info_en         String
  intro           String?
  intro_en        String?
  idNum           String
  countNum        String
  category        String
  category_en     String
  list            Json?
  list_en         Json?
  recommendAvatar String?
  sortNum         String?
  sortNum2        String?
  sellRecommend   Boolean? @default(false)
  isDeleted       Boolean? @default(false)
}

model TechnicalArticle {
  id        Int      @id @default(autoincrement())
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  timeStr   String?
  title     String
  content   String
  isDeleted Boolean? @default(false)
  authorId  Int?
  author    User?    @relation(fields: [authorId], references: [id])
}

model FENews {
  id        Int      @id @default(autoincrement())
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  timeStr   String?
  content   String
  isDeleted Boolean? @default(false)
  authorId  Int?
  author    User?    @relation(fields: [authorId], references: [id])
}

model JsonConfigGroup {
  id          Int          @id @default(autoincrement())
  appName     String?
  description String?
  isDeleted   Boolean?     @default(false)
  info        Json?
  jsonConfigs JsonConfig[] // 添加这一行，表示一对多关系
}

model JsonConfig {
  id                Int              @id @default(autoincrement())
  content           Json?
  updateTime        String?
  publishTime       String?
  env               String
  isDeleted         Boolean?         @default(false)
  JsonConfigGroup   JsonConfigGroup? @relation(fields: [jsonConfigGroupId], references: [id])
  jsonConfigGroupId Int?
}

model JsonHistory {
  id        Int      @id @default(autoincrement())
  createdAt DateTime @default(now())
  jsonId    Int
  url       String
}

model Projects {
  id                  Int                   @id @default(autoincrement())
  title               String
  repoName            String?
  desc                String?
  content             String?
  info                Json?
  pic                 String?
  pid                 String?
  fe                  String?
  type                Int?
  createdAt           DateTime              @default(now())
  updatedAt           DateTime              @updatedAt
  devUrl              String?
  dailyUrl            String?
  grayUrl             String?
  prodUrl             String?
  panguAppId          String?
  gitUrl              String?
  userProjectSettings UserProjectSettings[]
}

model UserProjectSettings {
  id        Int      @id @default(autoincrement())
  userId    Int
  projectId Int
  localPath String?
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  user      User     @relation(fields: [userId], references: [id])
  project   Projects @relation(fields: [projectId], references: [id])

  @@unique([userId, projectId])
}

model FEShare {
  id           Int      @id @default(autoincrement())
  createdAt    DateTime @default(now())
  updatedAt    DateTime @updatedAt
  timeStr      String?
  title        String
  pic          String?
  content      String?
  type         Int?
  isDeleted    Boolean? @default(false)
  fe           String?
  progressType Int?
  teamPlanType Int?
  authorId     Int?
  author       User?    @relation(fields: [authorId], references: [id])
}

model KolMatrix {
  id        Int      @id @default(autoincrement())
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  avatar    String
  idNo      String
  name      String
  fans      String
  sortNum   Float?
  isDeleted Boolean? @default(false)
}

model KolRecord {
  id        Int      @id @default(autoincrement())
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  date      String
  imgUrl    String
  commerce  String
  amount    String
  unit      String
  sortNum   String?
  isDeleted Boolean? @default(false)
}

model Material {
  id            Int      @id @default(autoincrement())
  name          String
  category      Int
  version       String
  description   String?
  platforms     String[]
  githubUrl     String?
  npmUrl        String?
  screenshotUrl String?
  fe            String[]
  content       String?
  createdAt     DateTime @default(now())
  updatedAt     DateTime @updatedAt
  isDeleted     Boolean? @default(false)
}

model H5 {
  id          Int      @id @default(autoincrement())
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
  timeStr     String?
  title       String
  description String?
  cover       String?
  html        String?
  css         String?
  js          String?
  uuid        String?
  isDeleted   Boolean? @default(false)
  authorId    Int?
  author      User?    @relation(fields: [authorId], references: [id])
}

model H5History {
  id        Int      @id @default(autoincrement())
  createdAt DateTime @default(now())
  h5Id      Int
  url       String
}

model Kunlun {
  id              Int               @id @default(autoincrement())
  createdAt       DateTime          @default(now())
  updatedAt       DateTime          @updatedAt
  content         String
  appName         String?
  desc            String?
  isDeleted       Boolean?          @default(false)
  authorId        Int?
  author          User?             @relation(fields: [authorId], references: [id])
  KunlunGroup     KunlunGroup?      @relation(fields: [kunlunGroupId], references: [id])
  kunlunGroupId   Int?
  KunlunGroupMock KunlunGroupMock[]
}

model KunlunGroup {
  id        Int      @id @default(autoincrement())
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  groupName String?
  desc      String?
  isDeleted Boolean? @default(false)
  authorId  Int?
  author    User?    @relation(fields: [authorId], references: [id])
  Kunlun    Kunlun[]
}

model KunlunMock {
  id        Int      @id @default(autoincrement())
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  content   String
  appName   String?
  desc      String?
  condition Json?
  data      Json?
  isDeleted Boolean? @default(false)
  authorId  Int?
  author    User?    @relation(fields: [authorId], references: [id])
}

model KunlunGroupMock {
  id        Int      @id @default(autoincrement())
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  condition Json?
  data      Json?
  kunlunId  Int?
  kunlun    Kunlun?  @relation(fields: [kunlunId], references: [id])
}

model VscMaterial {
  id              Int      @id @default(autoincrement())
  name            String
  category        Int?
  description     String?
  cover           String?
  content         String?
  css             String?
  note            String?
  createdAt       DateTime @default(now())
  updatedAt       DateTime @updatedAt
  supportsPreview Boolean? @default(false)
  isXC            Boolean? @default(false)
  isDeleted       Boolean? @default(false)
  authorId        Int?
  author          User?    @relation(fields: [authorId], references: [id])
}

model ServiceUsage {
  id          Int    @id @default(autoincrement())
  serviceName String
  date        String
  count       Int    @default(0)

  @@unique([serviceName, date])
}

// prisma/schema.prisma
model ServiceCall {
  id    Int    @id @default(autoincrement())
  name  String
  count Int    @default(0)
  date  String

  @@unique([name, date])
}

model AppCredential {
  id       Int    @id @default(autoincrement())
  appName  String // 应用名称
  data     Json?
  authorId Int? // 可选的用户ID
  author   User?  @relation(fields: [authorId], references: [id])
}

model Changelog {
  id           Int      @id @default(autoincrement())
  projectName  String
  branch       String?
  sourceBranch String?
  targetBranch String?
  log          String?
  user         String?
  time         String?
  url          String?
  title        String?
  createdAt    DateTime @default(now())
}

model XDesignTheme {
  id          Int      @id @default(autoincrement())
  tokens      Json?
  components  Json?
  name        String
  description String?
  version     String?
  isDeleted   Boolean? @default(false)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
}

model XDesignComponents {
  id          Int       @id @default(autoincrement())
  tokens      Json?
  name        String
  description String?
  category    String?
  isDeleted   Boolean?  @default(false)
  createdAt   DateTime? @default(now())
  updatedAt   DateTime? @updatedAt
}

model GptStore {
  id           Int      @id @default(autoincrement())
  name         String
  description  String
  instructions String
  createdAt    DateTime @default(now())
  updatedAt    DateTime @updatedAt
  isOfficial   Boolean  @default(false)
  authorId     Int?
  author       User?    @relation(fields: [authorId], references: [id])
}

model EmailStore {
  id          Int      @id @default(autoincrement())
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
  timeStr     String?
  title       String
  type        Int?
  description String?
  content     String?
  Json        Json?
  authorId    Int?
  to          String?
  author      User?    @relation(fields: [authorId], references: [id])
}

model EmailStoreTmpl {
  id          Int     @id @default(autoincrement())
  sort        String?
  title       String
  description String?
  content     String?
  type        Int?
}

model GlobalConfig {
  id         Int     @id @default(autoincrement())
  jsessionId String? @unique
}

model GlobalCache {
  id        Int     @id @default(autoincrement())
  key       String? @unique
  value     String?
  updatedAt String?
}

model TrackingApplication {
  id            Int            @id @default(autoincrement())
  name          String         @unique
  description   String?
  trackingPages TrackingPage[]
}

model TrackingPage {
  id                    Int                 @id @default(autoincrement())
  trackingApplicationId Int
  name                  String
  description           String?
  trackingApplication   TrackingApplication @relation(fields: [trackingApplicationId], references: [id])
  trackingModules       TrackingModule[]
}

model TrackingModule {
  id             Int                  @id @default(autoincrement())
  trackingPageId Int
  name           String
  description    String?
  trackingPage   TrackingPage         @relation(fields: [trackingPageId], references: [id])
  actionTypes    TrackingActionType[]
}

model TrackingActionType {
  id               Int            @id @default(autoincrement())
  trackingModuleId Int
  type             String
  description      String?
  trackingModule   TrackingModule @relation(fields: [trackingModuleId], references: [id])
}

model TeamWeeklyReport {
  id                 Int       @id @default(autoincrement())
  title              String
  content            String?
  createdAt          DateTime? @default(now())
  updatedAt          DateTime? @updatedAt
  authorId           Int?
  author             User?     @relation(fields: [authorId], references: [id])
  weeklyTaskSnapshot Json?
  teamBuilding       String?
  isDeleted          Boolean?  @default(false)
}
