-- CreateTable
CREATE TABLE "TechnicalArticle" (
    "id" SERIAL NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "time" TIMESTAMP(3) NOT NULL,
    "timeStr" TEXT,
    "title" TEXT NOT NULL,
    "content" TEXT NOT NULL,
    "isDeleted" BOOLEAN DEFAULT false,
    "authorId" INTEGER,

    CONSTRAINT "TechnicalArticle_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "FENews" (
    "id" SERIAL NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "time" TIMESTAMP(3) NOT NULL,
    "timeStr" TEXT,
    "content" TEXT NOT NULL,
    "isDeleted" BOOLEAN DEFAULT false,
    "authorId" INTEGER,

    CONSTRAINT "FENews_pkey" PRIMARY KEY ("id")
);

-- Add<PERSON><PERSON><PERSON><PERSON><PERSON>
ALTER TABLE "TechnicalArticle" ADD CONSTRAINT "TechnicalArticle_authorId_fkey" FOREIGN KEY ("authorId") REFERENCES "User"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "FENews" ADD CONSTRAINT "FENews_authorId_fkey" FOREIGN KEY ("authorId") REFERENCES "User"("id") ON DELETE SET NULL ON UPDATE CASCADE;
