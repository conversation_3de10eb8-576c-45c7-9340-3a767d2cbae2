/*
  Warnings:

  - You are about to drop the `<PERSON><PERSON><PERSON><PERSON><PERSON>` table. If the table is not empty, all the data it contains will be lost.
  - You are about to drop the `_MemberToTask` table. If the table is not empty, all the data it contains will be lost.

*/
-- DropForeignKey
ALTER TABLE "TaskMemebers" DROP CONSTRAINT "TaskMemebers_memberId_fkey";

-- DropForeignKey
ALTER TABLE "TaskMemebers" DROP CONSTRAINT "TaskMemebers_taskId_fkey";

-- DropForeignKey
ALTER TABLE "_MemberToTask" DROP CONSTRAINT "_MemberToTask_A_fkey";

-- DropForeignKey
ALTER TABLE "_MemberToTask" DROP CONSTRAINT "_MemberToTask_B_fkey";

-- AlterTable
ALTER TABLE "Member" ADD COLUMN     "taskId" INTEGER;

-- DropTable
DROP TABLE "TaskMemebers";

-- DropTable
DROP TABLE "_MemberToTask";

-- AddF<PERSON><PERSON><PERSON><PERSON>
ALTER TABLE "Member" ADD CONSTRAINT "Member_taskId_fkey" FOREIGN KEY ("taskId") REFERENCES "Task"("id") ON DELETE SET NULL ON UPDATE CASCADE;
