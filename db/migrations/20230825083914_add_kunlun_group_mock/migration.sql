/*
  Warnings:

  - You are about to drop the column `kunlunId` on the `KunlunMock` table. All the data in the column will be lost.

*/
-- DropForeignKey
ALTER TABLE "KunlunMock" DROP CONSTRAINT "KunlunMock_kunlunId_fkey";

-- AlterTable
ALTER TABLE "KunlunMock" DROP COLUMN "kunlunId";

-- CreateTable
CREATE TABLE "KunlunGroupMock" (
    "id" SERIAL NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "condition" JSONB,
    "data" JSONB,
    "kunlunId" INTEGER,

    CONSTRAINT "KunlunGroupMock_pkey" PRIMARY KEY ("id")
);

-- AddForeignKey
ALTER TABLE "KunlunGroupMock" ADD CONSTRAINT "KunlunGroupMock_kunlunId_fkey" FOREIGN KEY ("kunlunId") REFERENCES "Kunlun"("id") ON DELETE SET NULL ON UPDATE CASCADE;
