-- CreateTable
CREATE TABLE "VscMaterial" (
    "id" SERIAL NOT NULL,
    "name" TEXT NOT NULL,
    "category" INTEGER,
    "description" TEXT,
    "cover" TEXT,
    "content" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "isDeleted" BOOLEAN DEFAULT false,
    "authorId" INTEGER,

    CONSTRAINT "VscMaterial_pkey" PRIMARY KEY ("id")
);

-- AddForeignKey
ALTER TABLE "VscMaterial" ADD CONSTRAINT "VscMaterial_authorId_fkey" FOREIGN KEY ("authorId") REFERENCES "User"("id") ON DELETE SET NULL ON UPDATE CASCADE;
