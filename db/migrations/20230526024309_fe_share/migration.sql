-- CreateTable
CREATE TABLE "FEShare" (
    "id" SERIAL NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "timeStr" TEXT,
    "title" TEXT NOT NULL,
    "content" TEXT,
    "isDeleted" BOOLEAN DEFAULT false,
    "fe" TEXT,
    "authorId" INTEGER,

    CONSTRAINT "FEShare_pkey" PRIMARY KEY ("id")
);

-- AddForeignKey
ALTER TABLE "FEShare" ADD CONSTRAINT "FEShare_authorId_fkey" FOREIGN KEY ("authorId") REFERENCES "User"("id") ON DELETE SET NULL ON UPDATE CASCADE;
