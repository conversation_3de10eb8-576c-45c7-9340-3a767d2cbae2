-- CreateTable
CREATE TABLE "EmailStore" (
    "id" SERIAL NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "timeStr" TEXT,
    "title" TEXT NOT NULL,
    "description" TEXT,
    "content" TEXT,
    "authorId" INTEGER,

    CONSTRAINT "EmailStore_pkey" PRIMARY KEY ("id")
);

-- AddForeignKey
ALTER TABLE "EmailStore" ADD CONSTRAINT "EmailStore_authorId_fkey" FOREIGN KEY ("authorId") REFERENCES "User"("id") ON DELETE SET NULL ON UPDATE CASCADE;
