-- CreateTable
CREATE TABLE "AppCredential" (
    "id" SERIAL NOT NULL,
    "appName" TEXT NOT NULL,
    "username" TEXT NOT NULL,
    "password" TEXT NOT NULL,
    "authorId" INTEGER,

    CONSTRAINT "AppCredential_pkey" PRIMARY KEY ("id")
);

-- AddForeignKey
ALTER TABLE "AppCredential" ADD CONSTRAINT "AppCredential_authorId_fkey" FOREIGN KEY ("authorId") REFERENCES "User"("id") ON DELETE SET NULL ON UPDATE CASCADE;
