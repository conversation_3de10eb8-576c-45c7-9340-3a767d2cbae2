-- CreateTable
CREATE TABLE "KunlunMock" (
    "id" SERIAL NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "content" TEXT NOT NULL,
    "appName" TEXT,
    "desc" TEXT,
    "data" JSONB,
    "isDeleted" BOOLEAN DEFAULT false,
    "authorId" INTEGER,

    CONSTRAINT "KunlunMock_pkey" PRIMARY KEY ("id")
);

-- AddForeignKey
ALTER TABLE "KunlunMock" ADD CONSTRAINT "KunlunMock_authorId_fkey" FOREIGN KEY ("authorId") REFERENCES "User"("id") ON DELETE SET NULL ON UPDATE CASCADE;
