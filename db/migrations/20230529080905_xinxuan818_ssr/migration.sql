-- CreateTable
CREATE TABLE "KolMatrix" (
    "id" SERIAL NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "avatar" TEXT NOT NULL,
    "idNo" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "fans" TEXT NOT NULL,
    "sortNum" TEXT,
    "isDeleted" BOOLEAN DEFAULT false,

    CONSTRAINT "KolMatrix_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "KolRecord" (
    "id" SERIAL NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "date" TEXT NOT NULL,
    "imgUrl" TEXT NOT NULL,
    "commerce" TEXT NOT NULL,
    "amount" TEXT NOT NULL,
    "unit" TEXT NOT NULL,
    "sortNum" TEXT,
    "isDeleted" BOOLEAN DEFAULT false,

    CONSTRAINT "KolRecord_pkey" PRIMARY KEY ("id")
);
