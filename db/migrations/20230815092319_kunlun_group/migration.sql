-- AlterTable
ALTER TABLE "Kunlun" ADD COLUMN     "kunlunGroupId" INTEGER;

-- CreateTable
CREATE TABLE "KunlunGroup" (
    "id" SERIAL NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "groupName" TEXT,
    "desc" TEXT,
    "isDeleted" BOOLEAN DEFAULT false,
    "authorId" INTEGER,

    CONSTRAINT "KunlunGroup_pkey" PRIMARY KEY ("id")
);

-- AddForeignKey
ALTER TABLE "Kunlun" ADD CONSTRAINT "Kunlun_kunlunGroupId_fkey" FOREIGN KEY ("kunlunGroupId") REFERENCES "KunlunGroup"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "KunlunGroup" ADD CONSTRAINT "KunlunGroup_authorId_fkey" FOREIGN KEY ("authorId") REFERENCES "User"("id") ON DELETE SET NULL ON UPDATE CASCADE;
