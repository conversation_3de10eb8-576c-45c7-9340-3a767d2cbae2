/*
  Warnings:

  - You are about to drop the column `taskId` on the `Member` table. All the data in the column will be lost.

*/
-- DropFore<PERSON>Key
ALTER TABLE "Member" DROP CONSTRAINT "Member_taskId_fkey";

-- AlterTable
ALTER TABLE "Member" DROP COLUMN "taskId";

-- CreateTable
CREATE TABLE "_MemberToTask" (
    "A" INTEGER NOT NULL,
    "B" INTEGER NOT NULL
);

-- CreateIndex
CREATE UNIQUE INDEX "_MemberToTask_AB_unique" ON "_MemberToTask"("A", "B");

-- CreateIndex
CREATE INDEX "_MemberToTask_B_index" ON "_MemberToTask"("B");

-- AddForeignKey
ALTER TABLE "_MemberToTask" ADD CONSTRAINT "_MemberToTask_A_fkey" FOREIGN KEY ("A") REFERENCES "Member"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- Add<PERSON><PERSON>ignKey
ALTER TABLE "_MemberToTask" ADD CONSTRAINT "_MemberToTask_B_fkey" FOREIGN KEY ("B") REFERENCES "Task"("id") ON DELETE CASCADE ON UPDATE CASCADE;
