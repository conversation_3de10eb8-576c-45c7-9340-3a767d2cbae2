-- AlterTable
ALTER TABLE "FEShare" ADD COLUMN     "progressType" INTEGER,
ADD COLUMN     "teamPlanType" INTEGER;

-- CreateTable
CREATE TABLE "H5" (
    "id" SERIAL NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "timeStr" TEXT,
    "title" TEXT NOT NULL,
    "description" TEXT,
    "cover" TEXT,
    "html" TEXT,
    "css" TEXT,
    "js" TEXT,
    "uuid" TEXT,
    "isDeleted" BOOLEAN DEFAULT false,
    "authorId" INTEGER,

    CONSTRAINT "H5_pkey" PRIMARY KEY ("id")
);

-- AddForeignKey
ALTER TABLE "H5" ADD CONSTRAINT "H5_authorId_fkey" FOREIGN KEY ("authorId") REFERENCES "User"("id") ON DELETE SET NULL ON UPDATE CASCADE;
