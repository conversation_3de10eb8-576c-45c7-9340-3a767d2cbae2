-- CreateTable
CREATE TABLE "JsonConfigGroup" (
    "id" SERIAL NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "appCode" TEXT,
    "isDeleted" BOOLEAN DEFAULT false,

    CONSTRAINT "JsonConfigGroup_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "JsonConfig" (
    "id" SERIAL NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "appCode" TEXT,
    "name" TEXT NOT NULL,
    "content" JSONB,
    "env" TEXT NOT NULL,
    "filePath" TEXT,
    "isDeleted" BOOLEAN DEFAULT false,
    "jsonConfigGroupId" INTEGER,

    CONSTRAINT "JsonConfig_pkey" PRIMARY KEY ("id")
);

-- AddForeignKey
ALTER TABLE "JsonConfig" ADD CONSTRAINT "JsonConfig_jsonConfigGroupId_fkey" FOREIGN KEY ("jsonConfigGroupId") REFERENCES "JsonConfigGroup"("id") ON DELETE SET NULL ON UPDATE CASCADE;
