/*
  Warnings:

  - You are about to drop the column `bizName` on the `Task` table. All the data in the column will be lost.
  - You are about to drop the column `bizTeam` on the `Task` table. All the data in the column will be lost.
  - You are about to drop the column `content` on the `Task` table. All the data in the column will be lost.
  - You are about to drop the column `name` on the `Task` table. All the data in the column will be lost.
  - You are about to drop the column `plan` on the `Task` table. All the data in the column will be lost.
  - You are about to drop the column `prd` on the `Task` table. All the data in the column will be lost.
  - You are about to drop the column `progressContent` on the `Task` table. All the data in the column will be lost.
  - You are about to drop the column `progressType` on the `Task` table. All the data in the column will be lost.
  - You are about to drop the column `quality` on the `Task` table. All the data in the column will be lost.
  - You are about to drop the column `risk` on the `Task` table. All the data in the column will be lost.
  - You are about to drop the column `type` on the `Task` table. All the data in the column will be lost.
  - You are about to drop the `_MemberToTask` table. If the table is not empty, all the data it contains will be lost.

*/
-- DropForeignKey
ALTER TABLE "_MemberToTask" DROP CONSTRAINT "_MemberToTask_A_fkey";

-- DropForeignKey
ALTER TABLE "_MemberToTask" DROP CONSTRAINT "_MemberToTask_B_fkey";

-- AlterTable
ALTER TABLE "Task" DROP COLUMN "bizName",
DROP COLUMN "bizTeam",
DROP COLUMN "content",
DROP COLUMN "name",
DROP COLUMN "plan",
DROP COLUMN "prd",
DROP COLUMN "progressContent",
DROP COLUMN "progressType",
DROP COLUMN "quality",
DROP COLUMN "risk",
DROP COLUMN "type",
ADD COLUMN     "data" JSONB,
ADD COLUMN     "isDeleted" BOOLEAN DEFAULT false;

-- DropTable
DROP TABLE "_MemberToTask";
