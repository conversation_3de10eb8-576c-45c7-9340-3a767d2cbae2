-- CreateTable
CREATE TABLE "Kunlun" (
    "id" SERIAL NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "content" TEXT NOT NULL,
    "isDeleted" BOOLEAN DEFAULT false,
    "authorId" INTEGER,

    CONSTRAINT "Kunlun_pkey" PRIMARY KEY ("id")
);

-- AddForeignKey
ALTER TABLE "Kunlun" ADD CONSTRAINT "Kunlun_authorId_fkey" FOREIGN KEY ("authorId") REFERENCES "User"("id") ON DELETE SET NULL ON UPDATE CASCADE;
