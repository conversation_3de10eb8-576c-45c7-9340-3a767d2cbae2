-- CreateTable
CREATE TABLE "TrackingApplication" (
    "id" SERIAL NOT NULL,
    "name" TEXT NOT NULL,

    CONSTRAINT "TrackingApplication_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "TrackingPage" (
    "id" SERIAL NOT NULL,
    "trackingApplicationId" INTEGER NOT NULL,
    "name" TEXT NOT NULL,

    CONSTRAINT "TrackingPage_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "TrackingModule" (
    "id" SERIAL NOT NULL,
    "trackingPageId" INTEGER NOT NULL,
    "name" TEXT NOT NULL,

    CONSTRAINT "TrackingModule_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "TrackingActionType" (
    "id" SERIAL NOT NULL,
    "trackingModuleId" INTEGER NOT NULL,
    "type" TEXT NOT NULL,

    CONSTRAINT "TrackingActionType_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "TrackingApplication_name_key" ON "TrackingApplication"("name");

-- AddForeignKey
ALTER TABLE "TrackingPage" ADD CONSTRAINT "TrackingPage_trackingApplicationId_fkey" FOREIGN KEY ("trackingApplicationId") REFERENCES "TrackingApplication"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "TrackingModule" ADD CONSTRAINT "TrackingModule_trackingPageId_fkey" FOREIGN KEY ("trackingPageId") REFERENCES "TrackingPage"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "TrackingActionType" ADD CONSTRAINT "TrackingActionType_trackingModuleId_fkey" FOREIGN KEY ("trackingModuleId") REFERENCES "TrackingModule"("id") ON DELETE RESTRICT ON UPDATE CASCADE;
