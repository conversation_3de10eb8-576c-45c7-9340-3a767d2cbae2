import { SimpleRolesIsAuthorized } from "@blitzjs/auth"
import { User } from "db"

export type Role = "ADMIN" | "USER"

declare module "@blitzjs/auth" {
  export interface Session {
    isAuthorized: SimpleRolesIsAuthorized<Role>
    PublicData: {
      userId: User["id"]
      role: Role
    }
  }
}

declare module "oocode" {
  export const FeatureGrid: any
  export const ShineImageSection: any
  export const FeatureSection: any
  export const MouseTrail: any
  export const VanishInput: any
  export const Circle: any
}
