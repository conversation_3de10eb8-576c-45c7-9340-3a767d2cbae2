import React, { useState, useEffect } from "react"
import CodeMirror from "@uiw/react-codemirror"
import { StreamLanguage } from "@codemirror/language"
import { xml } from "@codemirror/legacy-modes/mode/xml"
import { css } from "@codemirror/legacy-modes/mode/css"
import { javascript } from "@codemirror/legacy-modes/mode/javascript"
import { useMutation, useQuery } from "@blitzjs/rpc"

import { FontAwesomeIcon } from "@fortawesome/react-fontawesome"
import { faCompressAlt, faExpandAlt } from "@fortawesome/free-solid-svg-icons"


export default function Editor(props) {
  const { language, displayName, value, onChange } = props

  const [open, setOpen] = useState(true)

  const [isClient, setIsClient] = useState(false)

  // Wait until after client-side hydration to show
  useEffect(() => {
    setIsClient(true)
  }, [])

  function handleChange(editor, data, value) {
    onChange(value)
  }

  if (!isClient) {
    return null
  }


  let extensions = [StreamLanguage.define(javascript)]

  return (
    <div className={`editor-container ${open ? "" : "collapsed"}`}>
      <div className="editor-title">
        {displayName}
        <button
          type="button"
          className="expand-collapse-btn"
          onClick={() => setOpen((prevOpen) => !prevOpen)}
        >
          <FontAwesomeIcon icon={open ? faCompressAlt : faExpandAlt} />
        </button>
      </div>
      <CodeMirror
        // onChange={handleChange}
        extensions={extensions}
        onChange={(value) => {
          handleChange(null, null, value)
        }}
        aria-expanded={false}
        editable={true}
        className="mb-10 border"
        placeholder={""}
        value={value}
        height="calc(100vh - 160px)"
      // minHeight="600px"
      // maxHeight="600px"

      // extensions={[StreamLanguage.define(json)]}
      // onBlur={() => {
      //   verifyJson()
      // }}
      />

      {/* <ControlledEditor
        onBeforeChange={handleChange}
        value={value}
        className="code-mirror-wrapper"
        options={{
          lineWrapping: true,
          lint: true,
          mode: language,
          theme: "material",
          lineNumbers: true,
        }}
      /> */}
    </div>
  )
}
