/* eslint-disable react/no-unknown-property */
// @ts-nocheck

import Layout from "pages/layouts/Layout"
import { Image, Input, Select, Form, Button as AntButton } from "antd"
import { Badge, Text, Grid, Container, Paper, Title, Group, Box, ScrollArea } from "@mantine/core"
import { useEffect, useState, useRef } from "react"
import { BlitzPage } from "@blitzjs/next"
import { showNotification } from "@mantine/notifications"
import { getEmployeesByTeam } from "@/constants/employees"
import SectionHeadings from "app/components/SectionHeadings"
import { useMutation } from "@blitzjs/rpc"
import updateSharingsession from "app/material/mutations/updateMaterial"
import querySharingSession from "app/material/mutations/queryMaterial"
import "md-editor-rt/lib/style.css"
import { useRouter } from "next/router"
import React from "react"
import Editor from "md-editor-rt"
import { onUploadImg } from "utils/aliyun"
import { PencilIcon, EyeIcon, UserGroupIcon, TagIcon, GlobeAltIcon, CodeBracketIcon, CubeIcon } from "@heroicons/react/24/outline"

const HomePage: BlitzPage = (props) => {
  const router = useRouter()
  const [formData, setFormData] = useState({})
  const [detail, setDetail] = useState({})
  const [data, setData] = useState("# title")
  const [loaded, setLoaded] = useState(false)
  const [form] = Form.useForm()

  const [updateSharingsessionMutation] = useMutation(updateSharingsession)
  const [querySharingSessionMutation] = useMutation(querySharingSession)
  const { id, mode } = router.query

  const editorRef = useRef<Editor | null>(null)

  const getTableData = async () => {
    if (id) {
      const res = await querySharingSessionMutation({ id })
      if (res.entry) {
        setLoaded(true)
        setDetail(res.entry)
        form.setFieldsValue(res.entry)
        setData(res.entry.content || data)
      }
    } else {
      setLoaded(true)
    }
  }

  useEffect(() => {
    void getTableData()
  }, [])

  const saveData = async (val) => {
    const res = await updateSharingsessionMutation({ id, ...formData, content: val })
    if (res.status) {
      showNotification({
        title: "成功",
        message: "保存成功",
        color: "teal",
      })
    }
  }

  const onFinish = (values) => {
    setFormData(values)
    showNotification({
      title: "信息",
      message: "正在保存...",
      color: "blue",
    })
    setTimeout(() => editorRef.current?.triggerSave(), 1000)
  }

  const categoryEnum = {
    1: "组件",
    2: "SDK",
    3: "工具",
    4: "插件",
    99: "其他",
  }

  const platformsEnum = {
    "1": "PC",
    "2": "小程序",
    "3": "H5",
    "99": "其他",
  }

  return (
    <Layout title="项目" {...props}>
      <Container fluid py="xl">
        <Group position="apart" mb="xl">
          <Title order={1} sx={{ color: '#2c3e50' }}>{mode === "view" ? (detail?.name || "查看") : "编辑"}</Title>
          <AntButton
            icon={mode === "view" ? <PencilIcon width={16} /> : <EyeIcon width={16} />}
            onClick={() => {
              void router.push({
                pathname: "/material/materialEditor",
                query: { id, mode: mode === "view" ? "edit" : "view" },
              })
            }}
            style={{ minWidth: '80px', display: 'flex', alignItems: 'center', justifyContent: 'center' }}
          >
            <span style={{ marginLeft: '4px' }}>{mode === "view" ? "编辑" : "查看"}</span>
          </AntButton>
        </Group>

        <Grid gutter="xl" style={{ height: 'calc(100vh - 120px)' }}>
          <Grid.Col span={12} md={3}>
            <ScrollArea style={{ height: '100%' }}>
              <Paper p="md" radius="md" withBorder shadow="sm" style={{ height: '100%' }}>
                {mode === "edit" ? (
                  <Form
                    initialValues={{
                      fe: [],
                      name: "",
                      description: "",
                      version: "",
                      category: 1,
                      platforms: ["1"],
                      githubUrl: "",
                      npmUrl: "",
                      screenshotUrl: "",
                    }}
                    form={form}
                    name="control-hooks"
                    onFinish={onFinish}
                    layout="vertical"
                  >
                    <Form.Item name="fe" label="维护者" rules={[{ required: false }]}>
                      <Select
                        mode="multiple"
                        placeholder="选择"
                        options={getEmployeesByTeam("3")}
                        allowClear
                      />
                    </Form.Item>
                    <Form.Item name="name" label="组件名称" rules={[{ required: false }]}>
                      <Input placeholder="taro-lazy-swiper" />
                    </Form.Item>
                    <Form.Item name="description" label="描述" rules={[{ required: false }]}>
                      <Input.TextArea rows={3} />
                    </Form.Item>
                    <Form.Item name="version" label="版本" rules={[{ required: false }]}>
                      <Input />
                    </Form.Item>
                    <Form.Item name="category" label="类别" rules={[{ required: false }]}>
                      <Select>
                        {Object.entries(categoryEnum).map(([key, value]) => (
                          <Select.Option key={key} value={Number(key)}>{value}</Select.Option>
                        ))}
                      </Select>
                    </Form.Item>
                    <Form.Item name="platforms" label="支持平台" rules={[{ required: false }]}>
                      <Select mode="multiple" allowClear>
                        {Object.entries(platformsEnum).map(([key, value]) => (
                          <Select.Option key={key} value={key}>{value}</Select.Option>
                        ))}
                      </Select>
                    </Form.Item>
                    <Form.Item name="githubUrl" label="GitHub 链接" rules={[{ required: false }]}>
                      <Input />
                    </Form.Item>
                    <Form.Item name="npmUrl" label="NPM 链接" rules={[{ required: false }]}>
                      <Input />
                    </Form.Item>
                    <Form.Item name="screenshotUrl" label="截图链接" rules={[{ required: false }]}>
                      <Input />
                    </Form.Item>

                    <Form.Item>
                      <AntButton type="primary" htmlType="submit" block>
                        保存
                      </AntButton>
                    </Form.Item>
                  </Form>
                ) : (
                  <>
                    <Title order={2} mb="md" sx={{ color: '#34495e' }}>{detail.name}</Title>
                    <Text size="sm" color="dimmed" mb="md">{detail.description}</Text>
                    <Group spacing="xs" mb="xs">
                      <UserGroupIcon width={16} />
                      <Text size="sm" weight={600} sx={{ color: '#34495e' }}>维护者</Text>
                    </Group>
                    <Text size="sm" mb="md">{detail.fe?.join(", ")}</Text>
                    <Group spacing="xs" mb="xs">
                      <TagIcon width={16} />
                      <Text size="sm" weight={600} sx={{ color: '#34495e' }}>版本</Text>
                    </Group>
                    <Text size="sm" mb="md">{detail.version}</Text>
                    <Group spacing="xs" mb="xs">
                      <CubeIcon width={16} />
                      <Text size="sm" weight={600} sx={{ color: '#34495e' }}>类别</Text>
                    </Group>
                    <Badge size="sm" color="blue" mb="md">{categoryEnum[detail.category]}</Badge>
                    <Group spacing="xs" mb="xs">
                      <GlobeAltIcon width={16} />
                      <Text size="sm" weight={600} sx={{ color: '#34495e' }}>支持平台</Text>
                    </Group>
                    <Group mb="md">
                      {detail.platforms?.map((item, idx) => (
                        <Badge key={idx} size="sm" color="green">{platformsEnum[item]}</Badge>
                      ))}
                    </Group>
                    <Group spacing="xs" mb="xs">
                      <CodeBracketIcon width={16} />
                      <Text size="sm" weight={600} sx={{ color: '#34495e' }}>GitHub</Text>
                    </Group>
                    <Text size="sm" component="a" href={detail.githubUrl} target="_blank" sx={{ color: '#3498db', wordBreak: 'break-all' }} mb="md">{detail.githubUrl}</Text>
                    <Group spacing="xs" mb="xs">
                      <CodeBracketIcon width={16} />
                      <Text size="sm" weight={600} sx={{ color: '#34495e' }}>NPM</Text>
                    </Group>
                    <Text size="sm" component="a" href={detail.npmUrl} target="_blank" sx={{ color: '#3498db', wordBreak: 'break-all' }} mb="md">{detail.npmUrl}</Text>
                    {detail.screenshotUrl && (
                      <Image
                        src={detail.screenshotUrl}
                        alt="截图"
                        width="100%"
                        preview={false}
                        style={{
                          borderRadius: '8px',
                          marginTop: '16px',
                          transition: 'transform 0.3s ease-in-out',
                        }}
                      />
                    )}
                  </>
                )}
              </Paper>
            </ScrollArea>
          </Grid.Col>

          <Grid.Col span={12} md={9}>
            {loaded && (
              <Paper p="md" radius="md" withBorder shadow="sm" sx={{ height: '100%' }}>
                <Editor
                  editorId="article-content"
                  theme="light"
                  ref={editorRef}
                  onUploadImg={onUploadImg}
                  onSave={saveData}
                  onChange={setData}
                  modelValue={data}
                  preview={true}
                  key={`view-${mode}-${id}`}
                  previewOnly={mode === "view"}
                  style={{ height: '100%' }}
                />
              </Paper>
            )}
          </Grid.Col>
        </Grid>
      </Container>
    </Layout>
  )
}

export default HomePage
