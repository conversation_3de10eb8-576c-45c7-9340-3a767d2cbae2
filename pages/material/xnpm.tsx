import React, { useEffect, useState } from "react";
import Head from "next/head";
import { ErrorComponent } from "@blitzjs/next";
import Layout from "pages/layouts/Layout";
import SectionHeadings from "app/components/SectionHeadings";
import { useRouter } from "next/router";
import { FloatButton } from "antd";
import { FileTextOutlined } from "@ant-design/icons";

// Props Interface
interface NpmDeploymentPageProps {
  title?: string;
  full?: boolean;
  [key: string]: any;
}

// Client-side Hydration Hook
const useClientHydration = () => {
  const [isClient, setIsClient] = useState(false);
  useEffect(() => {
    setIsClient(true);
  }, []);
  return isClient;
};

// FloatingButton Component
const FloatingButton: React.FC<{ isClient: boolean }> = ({ isClient }) => (
  <FloatButton
    description="打开"
    icon={<FileTextOutlined />}
    onClick={() => {
      if (isClient) window.open("http://npm.xinc818.com/");
    }}
  />
);

// IframeContainer Component
const IframeContainer: React.FC = () => (
  <iframe
    src="http://npm.xinc818.com/"
    width="100%"
    height="100%"
    frameBorder="0"
  />
);

const NpmDeploymentPage: React.FC<NpmDeploymentPageProps> = (props) => {
  const isClient = useClientHydration();

  return (
    <Layout title="xnpm" full {...props}>
      <FloatingButton isClient={isClient} />
      <IframeContainer />
    </Layout>
  );
};

export default NpmDeploymentPage;
