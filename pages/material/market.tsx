/* eslint-disable react/no-unknown-property */
// @ts-nocheck
import Layout from "pages/layouts/Layout"
import { Image } from "antd"
import { Suspense, useEffect, useState, useRef } from "react"
import getToken from "app/users/queries/getToken"
import { useCurrentAssets } from "app/users/hooks/useCurrentAssets"
import { Routes, BlitzPage } from "@blitzjs/next"
import { CopyButton, Button } from "@mantine/core"
import SectionHeadings from "app/components/SectionHeadings"
import { useMutation, useQuery } from "@blitzjs/rpc"
import querySharingSession from "app/material/mutations/queryMaterial"
import { Router, useRouter } from "next/router"
import React from "react"
import VanillaTilt from "vanilla-tilt"

import { PencilSquareIcon } from "@heroicons/react/24/outline"

const Module = (props) => {
  const router = useRouter()

  return (
    <div
      className="group relative h-0 cursor-pointer overflow-hidden rounded-lg pb-[75%]"
      onClick={() => {
        void router.push({
          pathname: "/material/materialEditor",
          query: {
            id: props.data.id,
            mode: "view",
          },
        })
      }}
    >
      <div
        className="absolute inset-0 bg-cover bg-center transition-transform duration-300 ease-in-out group-hover:scale-105"
        style={{
          backgroundImage: `url(${props.data.screenshotUrl})`,
        }}
      />
      <div className="absolute bottom-0 left-0 right-0 w-full overflow-hidden bg-black/70 px-3 py-2 backdrop-blur-sm transition-all duration-300 ease-in-out group-hover:bg-black/80">
        <h2 className="text-sm font-semibold capitalize text-white truncate">
          {props.data.name}
        </h2>
        <p className="mt-0.5 text-xs text-gray-300 truncate">
          {props.data.description}
        </p>
      </div>
    </div>
  )
}

const Card = (props) => {
  const router = useRouter()
  const tiltRef = useRef()
  const [position, setPosition] = useState({ x: 0, y: 0 })

  const handleMouseMove = (event) => {
    const { left, top, width, height } = event.currentTarget.getBoundingClientRect()
    const x = event.clientX - left - width / 1
    const y = event.clientY - top - height / 1
    setPosition({ x, y })
  }

  useEffect(() => {
    VanillaTilt.init(tiltRef.current, {
      max: 10,
      speed: 300,
      glare: true,
      "max-glare": 0.3,
    })
  }, [])

  const transform = `scale(1.04) translateX(${position.x}px) translateY(${position.y}px) translateZ(0px)`

  return (
    <li
      style={{ opacity: 1, transform: "none" }}
      className="cursor-pointer"
      onClick={() => {
        void router.push({
          pathname: "/material/materialEditor",
          query: {
            id: props.data.id,
            mode: "view",
          },
        })
      }}
    >
      <div className="w-[70vw] max-w-[300px]" style={{ perspective: "800px" }}>
        <div
          tabIndex={0}
          style={{ transform: "none" }}
          ref={tiltRef}
          id="3element-id"
          onMouseMove={handleMouseMove}
        >
          <a className="block hover:no-underline">
            <div className="relative flex flex-col gap-3 rounded-2xl px-6 pb-8 pt-16 shadow-xl">
              <div className="absolute left-0 top-0 -z-50 h-full w-full overflow-hidden rounded-2xl bg-black">
                <div
                  className="absolute left-0 top-0 h-full w-full bg-center"
                  style={{
                    background:
                      "linear-gradient(transparent 19px, rgba(255, 255, 255, 0.2) 20px) 0% 0% / 20px 20px, linear-gradient(to right, transparent 19px, rgba(255, 255, 255, 0.2) 20px)",
                    backgroundSize: "20px 20px",
                  }}
                ></div>
                <img
                  src="https://getumbrel.github.io/umbrel-apps-gallery/tailscale/icon.svg"
                  style={{ transform }}
                  className="pointer-events-none absolute h-[200%] w-[200%] max-w-[200%] object-cover blur-[100px] saturate-150"
                  alt="12"
                />
              </div>

              <img
                src={props.data.screenshotUrl}
                className="pointer-events-none absolute -top-8 h-20 w-20 rounded-2xl object-cover shadow-xl md:h-24 md:w-24"
                alt="Icon of Tailscale on Umbrel App Store"
                style={{ transform: "none" }}
              />
              <div className="mt-2">
                <span className="mb-1 line-clamp-1 block text-xl font-medium text-white/90">
                  {props.data.name}
                </span>
                <span className="line-clamp-2 block h-10 text-sm text-white/70">
                  {props.data.description}
                </span>
              </div>
            </div>
          </a>
        </div>
      </div>
    </li>
  )
}

const HomePage: BlitzPage = (props) => {
  const [isClient, setIsClient] = useState(false)

  useEffect(() => {
    setIsClient(true)
  }, [])

  const [data, setData] = useState([])
  const [querySharingSessionMutation] = useMutation(querySharingSession, {})
  const router = useRouter()

  const getTableData = async () => {
    const res = await querySharingSessionMutation({
      type: "ea",
    })
    if (res.entry) {
      setData(res.entry || [])
    }
  }

  useEffect(() => {
    void getTableData()
  }, [])

  function getDataByCategory(data, category) {
    return data.filter((item) => item.category === category)
  }

  function filterPlatforms(data, type) {
    return data.filter((item) => item.platforms.includes(type))
  }

  return (
    <Layout title="projects" {...props}>
      <SectionHeadings
        name="物料市场"
        btnText="新增物料"
        onClick={() => {
          void router.push({
            pathname: "/material/materialEditor",
            query: {
              mode: 'edit'
            }
          })
        }}
      />

      <main className="container mx-auto px-4">
        <section className="mt-10">
          <div className="mb-6">
            <h2
              className="mb-1 text-xs font-bold uppercase tracking-wide text-black/40"
              style={{ opacity: 1, transform: "none" }}
            >
              Staff picks
            </h2>
            <span
              className="mb-8 block text-2xl font-bold text-black/80 md:text-3xl"
              style={{ opacity: 1, transform: "none" }}
            >
              SDK
            </span>
          </div>
          <ul className="!scrollbar-thin !scrollbar-h-0 !scrollbar-thumb-rounded-full !scrollbar-track-transparent !scrollbar-thumb-transparent flex flex-nowrap gap-4 overflow-x-auto pb-8 pt-[3rem] md:pt-[3rem]">
            {getDataByCategory(data, 2).map((item, key) => {
              return <Card key={key} data={item} />
            })}
          </ul>
        </section>

        <section className="bg-white mt-10">
          <div className="mb-6">
            <h2
              className="mb-1 text-xs font-bold uppercase tracking-wide text-black/40"
              style={{ opacity: 1, transform: "none" }}
            >
              Most installs
            </h2>
            <span
              className="mb-8 block text-2xl font-bold text-black/80 md:text-3xl"
              style={{ opacity: 1, transform: "none" }}
            >
              组件库
            </span>
          </div>
          <div className="mt-3 grid grid-cols-2 gap-4 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 xl:mt-3 xl:gap-5">
            {filterPlatforms(data, "1").concat(filterPlatforms(data, "2")).map((item, index) => {
              return <Module key={index} data={item} />
            })}
          </div>
        </section>

        <section className="bg-white mt-10">
          <div className="mb-6">
            <h2
              className="mb-1 text-xs font-bold uppercase tracking-wide text-black/40"
              style={{ opacity: 1, transform: "none" }}
            >
              Most installs
            </h2>
            <span
              className="mb-8 block text-2xl font-bold text-black/80 md:text-3xl"
              style={{ opacity: 1, transform: "none" }}
            >
              工具库
            </span>
          </div>
          <div className="mt-3 grid grid-cols-2 gap-4 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 xl:mt-3 xl:gap-5">
            {getDataByCategory(data, 3).map((item, index) => {
              return <Module key={index} data={item} />
            })}
          </div>
        </section>

        <section className="bg-white mt-10">
          <div className="mb-6">
            <h2
              className="mb-1 text-xs font-bold uppercase tracking-wide text-black/40"
              style={{ opacity: 1, transform: "none" }}
            >
              Most installs
            </h2>
            <span
              className="mb-8 block text-2xl font-bold text-black/80 md:text-3xl"
              style={{ opacity: 1, transform: "none" }}
            >
              插件、扩展
            </span>
          </div>
          <div className="mt-3 grid grid-cols-2 gap-4 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 xl:mt-3 xl:gap-5">
            {getDataByCategory(data, 4).map((item, index) => {
              return <Module key={index} data={item} />
            })}
          </div>
        </section>

        <div className="py-6"></div>
      </main>
    </Layout>
  )
}

export default HomePage
