import { Suspense, useEffect, useState, useRef } from "react"
import Layout from "pages/layouts/Layout"
import { Group, Text, useMantineTheme } from "@mantine/core"
import { IconUpload, IconPhoto, IconX } from "@tabler/icons"
import getToken from "app/users/queries/getDesignFileToken"
import uploadImage from "@/app/task/mutations/tinify"

import { invoke, useMutation } from "@blitzjs/rpc"
import { message } from "antd"
import upload from "app/auth/mutations/upload"
import { Dropzone, DropzoneProps, IMAGE_MIME_TYPE } from "@mantine/dropzone"
import Item from "antd/es/list/Item"
import { CopyButton, Button } from "@mantine/core"
import { Table, Modal, Input, Image } from "antd"
import SectionHeadings from "app/components/SectionHeadings"
import { Radio } from "antd"
import { showNotification, cleanNotifications } from "@mantine/notifications"
// const tinify = require("tinify")

// const tinify = require("tinify")
// 设置 API Key
// tinify.key = "vXb6B2skJsL0dc80vtysncHJ8dqx9F3P"

// 读取文件内容的辅助函数
function readFileAsync(fileObj) {
  return new Promise((resolve, reject) => {
    const reader = new FileReader()
    reader.readAsArrayBuffer(fileObj)
    reader.onload = () => resolve(reader.result)
    reader.onerror = (error) => reject(error)
  })
}

function getRandomName(isTin) {
  // if (isTin) {
  //   return "webci" + Date.now().toString(36) + Math.random().toString(36).substr(2, 6) + "tinty"
  // } else {
  //   return "webci" + Date.now().toString(36) + Math.random().toString(36).substr(2, 6)
  // }
  return "resources"
}

export default function BaseDemo(props: Partial<DropzoneProps>) {
  const theme = useMantineTheme()
  const [fileUrl, setFiletUrl] = useState([])
  const [token, setToken] = useState(null)
  const [value, setValue] = useState(1)
  const [uploadMutation] = useMutation(upload)
  const [uploadImageMutation] = useMutation(uploadImage)

  async function readFileAsArrayBuffer(file) {
    return new Promise((resolve, reject) => {
      const reader = new FileReader()
      reader.onload = () => resolve(reader.result)
      reader.onerror = reject
      reader.readAsArrayBuffer(file)
    })
  }

  async function arrayBufferToBase64(arrayBuffer) {
    const blob = new Blob([arrayBuffer])
    const reader = new FileReader()
    return new Promise((resolve, reject) => {
      reader.onload = () => resolve(reader.result)
      reader.onerror = reject
      reader.readAsDataURL(blob)
    })
  }

  // 定义压缩图片函数
  async function compressImage(fileObj) {
    try {
      // 读取文件内容
      // const buffer = (await readFileAsync(fileObj)) as any

      const arrayBuffer = await readFileAsArrayBuffer(fileObj)
      const base64 = await arrayBufferToBase64(arrayBuffer)

      // const encoder = new TextEncoder()

      // console.log("文件内容", buffer, encoder.encode(buffer).toString())

      // const array = new Int16Array(buffer)
      // const json = JSON.stringify({
      //   buffer: array,
      // });

      // 调用 tinify API 进行图片压缩
      const res = (await uploadImageMutation({
        image: base64,
        // data: encoder.encode(buffer).toString(),
      })) as any
      console.log("res", res)
      const base64Data = res.entry
      const base64ToUint8Array = (base64) => {
        const binaryString = atob(base64)
        const byteArray = new Uint8Array(binaryString.length)
        for (let i = 0; i < binaryString.length; i++) {
          byteArray[i] = binaryString.charCodeAt(i)
        }
        return byteArray
      }

      const byteArray = base64ToUint8Array(base64Data)
      // const byteArray = new Uint8Array(byteString.length)

      // for (let i = 0; i < byteString.length; i++) {
      //   byteArray[i] = byteString.charCodeAt(i)
      // }

      const blob = new Blob([byteArray], { type: fileObj.type })

      // const compressedBlob = new Blob([blob], { type: fileObj.type })

      // const compressedFile = new File([compressedBlob], fileObj.name, {
      //   type: fileObj.type,
      // })
      console.log("compressedFile", blob)
      // return null
      return blob
    } catch (err) {
      console.error(err)
      return null
    }
  }

  async function uploadImages(file, _file, uploadConfig) {
    console.log("vs", file, _file)
    let domain = "https://s.xinc818.com/"

    return new Promise((resolve, reject) => {
      var xhr = new XMLHttpRequest()
      var formData = new FormData()
      Object.keys(uploadConfig).forEach((key) => {
        formData.append(key, uploadConfig[key])
      })
      const fileName = file.name
      // let uploadName = `${getRandomName() + fileName.substr(fileName.lastIndexOf('.'))}`;
      let uploadName = `${getRandomName(_file ? true : false)}/${fileName}`
      // if (elChecked.checked) {
      //   uploadName = fileName;
      // }
      // const res = await uploadImageMutation({
      //   url: domain + key,
      // })
      const key = `${uploadConfig.dirPath}${uploadName}`
      formData.append("key", key)
      if (_file) {
        formData.append("file", _file, file.name)
      } else {
        formData.append("file", file)
      }
      xhr.open("POST", uploadConfig.host)
      xhr.send(formData)
      xhr.onreadystatechange = async function () {
        if (xhr.readyState == 4 && xhr.status >= 200 && xhr.status < 300) {
          // await uploadImages3(res, uploadConfig)
          resolve([domain + key, true])
        }
        if (xhr.status > 300) {
          resolve([false, false])
          xhr.abort()
        }
      }
      xhr.timeout = 50000
      xhr.ontimeout = (event) => {
        resolve([false, false])
      }
    })
  }
  const columns = [
    // {
    //   title: "图片",
    //   width: "80px",
    //   dataIndex: "url",
    //   render(value, record, index) {
    //     // eslint-disable-next-line jsx-a11y/alt-text
    //     return <Image src={value} />
    //   },
    // },
    {
      title: "URL地址",
      dataIndex: "url",
      render(value, record, index) {
        // eslint-disable-next-line jsx-a11y/alt-text
        return <span>{value}</span>
      },
    },
    {
      title: "默认/压缩",
      width: "160px",
      dataIndex: "url",
      render(value, record, index) {
        // eslint-disable-next-line jsx-a11y/alt-text
        return <span>{value.indexOf("tinty/") != -1 ? "已压缩(tinty)" : "默认"}</span>
      },
    },
    {
      title: "操作",
      width: "120px",
      dataIndex: "options",
      render(value, record, index) {
        // eslint-disable-next-line jsx-a11y/alt-text
        return (
          <CopyButton value={record.url}>
            {({ copied, copy }) => (
              <Button color={copied ? "teal" : "blue"} onClick={copy}>
                {copied ? "Copied url" : "Copy url"}
              </Button>
            )}
          </CopyButton>
        )
      },
    },
  ]

  useEffect(() => {
    const getTokenHandle = async () => {
      const token = (await invoke(getToken, {})) as any
      setToken(token)
    }
    void getTokenHandle()
    //
  }, [])

  if (!token) {
    return (
      <Layout title="upload" {...props}>
        <span></span>
      </Layout>
    )
  }

  const onChange = (e) => {
    setValue(e.target.value)
  }
  return (
    <Layout title="upload" {...props}>
      <SectionHeadings name="设计资源上传" />
      {/* <Radio.Group onChange={onChange} value={value} className="mb-5">
        <Radio value={1}>默认模式</Radio>
        <Radio value={2}>TinyPng压缩模式</Radio>
      </Radio.Group> */}
      <Dropzone
        onDrop={async (files) => {
          showNotification({
            title: "上传提示:",
            message: "开始上传任务...",
          })
          let arr = [] as any
          for (let i = 0; i < files.length; i++) {
            try {
              let _file = null
              if (value == 2) {
                showNotification({
                  title: "上传提示:",
                  autoClose: false,
                  message: `第${i + 1}个文件正在压缩中，请稍候。如果文件较大，可能需要稍等片刻  🤥`,
                })
                _file = (await compressImage(files[i])) as any
                if (_file == null) {
                  showNotification({
                    title: "上传提示:",
                    color: "red",
                    message: "今日压缩次数已达限制，改用默认模式ing 😭",
                  })
                }
              }
              const [result, status] = (await uploadImages(files[i], _file || null, token)) as any
              if (status) {
                await uploadMutation({
                  url: result,
                })
                arr.push({
                  url: result,
                })
              } else {
                void message.error("上传失败，请刷新页面重试")
              }
            } catch (err) { }
          }
          setFiletUrl(arr)
          showNotification({
            title: "上传提示:",
            message: "上传任务完成! 🎉",
          })
          setTimeout(() => {
            cleanNotifications()
          }, 1500)

          console.log("accepted files", files)
        }}
        onReject={(files) => console.log("rejected files222", files)}
        maxSize={100 * 1024 ** 2}
        accept={["*"]}
        {...props}
      >
        <Group position="center" spacing="xl" style={{ minHeight: 220, pointerEvents: "none" }}>
          <Dropzone.Accept>
            <IconUpload
              size={50}
              stroke={1.5}
            // color={theme.colors[theme.primaryColor][theme.colorScheme === "dark" ? 4 : 6]}
            />
          </Dropzone.Accept>
          <Dropzone.Reject>
            <IconX
              size={50}
              stroke={1.5}
              color={theme.colors.red[theme.colorScheme === "dark" ? 4 : 6]}
            />
          </Dropzone.Reject>
          <Dropzone.Idle>
            <IconPhoto size={50} stroke={1.5} />
          </Dropzone.Idle>

          <div>
            <Text size="xl" inline>
              Drag images here or click to select files
            </Text>
            <Text size="sm" color="dimmed" inline mt={7}>
              Attach as many files as you like, each file should not exceed 5mb
            </Text>
          </div>
        </Group>
      </Dropzone>
      <br />
      <div>
        <Table
          columns={columns}
          pagination={{ pageSize: 50 }}
          dataSource={fileUrl}
          scroll={{ x: "max-content" }}
        />
      </div>
    </Layout>
  )
}
