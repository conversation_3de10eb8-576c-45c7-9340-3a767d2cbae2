import React, { useEffect, useState } from "react";
import Layout from "pages/layouts/Layout";
import { FloatButton } from "antd";
import { FileTextOutlined } from "@ant-design/icons";

// Props Interface
interface CodeSignPageProps {
  title?: string;
  full?: boolean;
  [key: string]: any;
}

// Custom Hook for Client-side Hydration
const useClientHydration = () => {
  const [isClient, setIsClient] = useState(false);
  useEffect(() => {
    setIsClient(true);
  }, []);
  return isClient;
};

// FloatingButton Component
const FloatingButton: React.FC<{ isClient: boolean; url: string }> = ({ isClient, url }) => (
  <FloatButton
    description="打开"
    icon={<FileTextOutlined />}
    onClick={() => {
      if (isClient) {
        window.open(url);
      }
    }}
  />
);

// IframeContainer Component
const IframeContainer: React.FC<{ url: string }> = ({ url }) => (
  <iframe
    src={url}
    width="100%"
    height="100%"
    frameBorder="0"
  />
);

const CodeSignPage: React.FC<CodeSignPageProps> = (props) => {
  const pageUrl = 'https://codesign.qq.com/app/design/QGPEpZGla3jw3z7?team_id=eGyOl9ykrLZdxaW';
  const isClient = useClientHydration();

  return (
    <Layout title="webci" full {...props}>
      <FloatingButton isClient={isClient} url={pageUrl} />
      <IframeContainer url={pageUrl} />
    </Layout>
  );
};

export default CodeSignPage;
