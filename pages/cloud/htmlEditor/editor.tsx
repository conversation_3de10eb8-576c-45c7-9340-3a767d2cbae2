import React, { useState, useEffect } from "react";
import CodeMirror from "@uiw/react-codemirror";
import { StreamLanguage } from "@codemirror/language";
import { xml } from "@codemirror/legacy-modes/mode/xml";
import { css } from "@codemirror/legacy-modes/mode/css";
import { javascript } from "@codemirror/legacy-modes/mode/javascript";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { faCompressAlt, faExpandAlt } from "@fortawesome/free-solid-svg-icons";

interface EditorProps {
  language: string;
  displayName: string;
  value: string;
  onChange: (value: string) => void;
}

const CodeEditor: React.FC<EditorProps> = ({ language, displayName, value, onChange }) => {
  const [isExpanded, setIsExpanded] = useState(true);
  const [isClient, setIsClient] = useState(false);

  // Ensure the component is hydrated before rendering
  useEffect(() => {
    setIsClient(true);
  }, []);

  const handleEditorChange = (value: string) => {
    onChange(value);
  };

  if (!isClient) {
    return null;
  }

  let languageExtensions;
  switch (language) {
    case "xml":
      languageExtensions = [StreamLanguage.define(xml)];
      break;
    case "css":
      languageExtensions = [StreamLanguage.define(css)];
      break;
    case "javascript":
    default:
      languageExtensions = [StreamLanguage.define(javascript)];
      break;
  }

  return (
    <div className={`editor-container ${isExpanded ? "" : "collapsed"}`}>
      <div className="editor-title">
        {displayName}
        <button
          type="button"
          className="expand-collapse-btn"
          onClick={() => setIsExpanded((prev) => !prev)}
        >
          <FontAwesomeIcon icon={isExpanded ? faCompressAlt : faExpandAlt} />
        </button>
      </div>
      <CodeMirror
        extensions={languageExtensions}
        onChange={handleEditorChange}
        value={value}
        className="code-mirror-editor mb-10 border"
        editable={true}
        placeholder=""
        maxHeight="calc(100vh - 220px)"
        minHeight="600px"
      />
    </div>
  );
};

export default CodeEditor;
