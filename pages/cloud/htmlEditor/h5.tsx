import React, { useState, useEffect, useCallback } from "react";
import { useRouter } from "next/router";
import { Tabs, Modal, Table, Button, Input, Typography, QRCode, Space, Form } from "antd";
import type { ColumnsType } from "antd/es/table";
import dayjs from "dayjs";
import Layout from "pages/layouts/Layout";
import SectionHeadings from "app/components/SectionHeadings";
import updateMobileWebsites from "app/h5/mutations/updateMobileWebsites";
import queryMobileWebsites from "app/h5/mutations/queryMobileWebsites";
import queryHistory from "app/h5/mutations/queryHistory";
import { showNotification } from "@mantine/notifications";
import uploadHtml from "@/app/h5/mutations/uploadHtml";
import tmpl from "../../../constants/tmpl";
import { nanoid } from "nanoid";
import { useMutation } from "@blitzjs/rpc";
import Editor from "./editor";

const { Title } = Typography;
const { confirm } = Modal;

const MobileWebsiteEditorPage: React.FC = (props) => {
  const [htmlContent, setHtmlContent] = useState(tmpl.html);
  const [cssContent, setCssContent] = useState(tmpl.css);
  const [jsContent, setJsContent] = useState(tmpl.js);
  const [pageUuid, setPageUuid] = useState("");
  const [iframeSource, setIframeSource] = useState("");
  const [pageTitle, setPageTitle] = useState("");
  const [pageDescription, setPageDescription] = useState("");
  const [previewUrl, setPreviewUrl] = useState("");
  const [productionUrl, setProductionUrl] = useState("");
  const [isPageLoaded, setIsPageLoaded] = useState(false);
  const [historyRecords, setHistoryRecords] = useState([]);

  const router = useRouter();
  const { id } = router.query;

  const [updateWebsiteMutation] = useMutation(updateMobileWebsites);
  const [fetchWebsiteMutation] = useMutation(queryMobileWebsites);
  const [uploadHtmlMutation] = useMutation(uploadHtml);
  const [fetchHistoryMutation] = useMutation(queryHistory);

  const fetchPageData = useCallback(async () => {
    if (id) {
      const response = await fetchWebsiteMutation({ id });
      if (response.entry) {
        setIsPageLoaded(true);
        setHtmlContent(response.entry.html);
        setCssContent(response.entry.css);
        setJsContent(response.entry.js);
        setPageTitle(response.entry.title);
        setPageDescription(response.entry.description);
        setPageUuid(response.entry.uuid);
        setPreviewUrl(`https://s.xinc818.com/flow-h5/preview/${id}/${response.entry.uuid}.html`);
        setProductionUrl(`https://s.xinc818.com/flow-h5/prod/${id}/${response.entry.uuid}.html`);
      }
    } else {
      setIsPageLoaded(true);
    }
  }, [id, fetchWebsiteMutation]);

  useEffect(() => {
    fetchPageData();
  }, [fetchPageData]);

  useEffect(() => {
    const timeoutId = setTimeout(() => {
      setIframeSource(`
        <html>
          <body>${htmlContent}</body>
          <style>${cssContent}</style>
          <script>${jsContent}</script>
        </html>
      `);
    }, 250);
    return () => clearTimeout(timeoutId);
  }, [htmlContent, cssContent, jsContent]);

  const handleSavePage = async () => {
    if (pageTitle.trim() === "") {
      showNotification({
        title: "Error",
        message: "请输入页面标题",
        color: "red",
      });
      return;
    }

    const response = await updateWebsiteMutation({
      id,
      uuid: nanoid(),
      description: pageDescription,
      title: pageTitle,
      html: htmlContent,
      css: cssContent,
      js: jsContent,
    });

    if (response.status) {
      showNotification({
        title: "成功",
        message: id ? "保存成功" : "保存成功,自动跳转到编辑页..",
        color: "green",
      });

      if (!id && response.entry.id) {
        setTimeout(() => {
          void router.replace({
            pathname: "/cloud/htmlEditor/h5",
            query: { id: response.entry.id },
          });
        }, 500);
      }
    } else {
      showNotification({
        title: "Error",
        message: response.message || "保存失败",
        color: "red",
      });
    }
  };

  const handleUploadHtml = async (type: string) => {
    const response = await uploadHtmlMutation({ id, type }) as any;
    if (response.status) {
      showNotification({
        title: "Success",
        message: "同步成功",
        color: "green",
      });
    } else {
      showNotification({
        title: "Error",
        message: response.message || "同步CDN失败",
        color: "red",
      });
    }
  };

  const columns: ColumnsType<any> = [
    {
      title: "发布时间",
      dataIndex: "createdAt",
      render: (value) => (value ? dayjs(value).format("YYYY年MM月DD日 HH:mm:ss") : "-"),
    },
    {
      title: "预览地址",
      dataIndex: "url",
      render: (value) => (
        <a href={value} target="_blank" rel="noreferrer">
          {value}
        </a>
      ),
    },
  ];

  const tabItems = [
    {
      key: "1",
      label: `HTML`,
      children: <Editor language="xml" displayName="HTML" value={htmlContent} onChange={setHtmlContent} />,
    },
    {
      key: "2",
      label: `CSS`,
      children: <Editor language="css" displayName="CSS" value={cssContent} onChange={setCssContent} />,
    },
    {
      key: "3",
      label: `JavaScript`,
      children: <Editor language="javascript" displayName="JS" value={jsContent} onChange={setJsContent} />,
    },
    {
      key: "4",
      label: `配置/部署`,
      children: (
        <div>
          <Form.Item label="页面标题" className="mb-5">
            <Input
              value={pageTitle}
              placeholder="输入页面标题"
              onChange={(e) => setPageTitle(e.target.value)}
            />
          </Form.Item>
          <Form.Item label="页面描述" className="mb-3">
            <Input.TextArea
              value={pageDescription}
              placeholder="输入页面描述"
              rows={4}
              onChange={(e) => setPageDescription(e.target.value)}
            />
          </Form.Item>
          <Title level={3} className="py-5">
            操作
          </Title>
          <Space direction="vertical" className="mb-10">
            <Space wrap>
              <Button type="primary" size="large" onClick={handleSavePage}>
                保存
              </Button>
              {id && (
                <Button
                  type="dashed"
                  size="large"
                  onClick={() => handleUploadHtml("preview")}
                >
                  预发布
                </Button>
              )}
              {id && (
                <Button
                  type="dashed"
                  size="large"
                  onClick={() => {
                    confirm({
                      title: "温馨提示",
                      content: "请先点保存，再按发布，并确认您发布的内容",
                      onOk: () => handleUploadHtml("prod"),
                    });
                  }}
                >
                  上线
                </Button>
              )}
            </Space>
          </Space>
          <Tabs
            defaultActiveKey="1"
            items={[
              {
                label: "测试环境",
                key: "1",
                children: (
                  <Space direction="vertical">
                    <QRCode value={previewUrl || "-"} />
                    <span>{previewUrl}</span>
                  </Space>
                ),
              },
              {
                label: "线上环境",
                key: "2",
                children: (
                  <Space direction="vertical">
                    <QRCode value={productionUrl || "-"} />
                    <span>{productionUrl}</span>
                  </Space>
                ),
              },
            ]}
          />
        </div>
      ),
    },
    {
      key: "5",
      label: `历史记录`,
      children: <Table columns={columns} pagination={{ pageSize: 20 }} dataSource={historyRecords} />,
    },
  ];

  return (
    <div className="h-screen overflow-hidden">
      <Layout title="编辑移动网站" {...props}>
        <SectionHeadings name="编辑页面" />
        {isPageLoaded && (
          <div className="flex h-auto flex-row mt-10">
            <div className="h-auto w-1/2">
              <Tabs defaultActiveKey="1" items={tabItems} onChange={(key) => key === '5' && fetchHistoryMutation({ id }).then(res => setHistoryRecords(res.entry || []))} />
            </div>
            <div className="flex w-1/2 items-center justify-center">
              <div className="mobile-device items-center">
                <div className="mobile-device-status flex w-4/5 items-center justify-between ">
                  <span></span>
                  <span>10:24</span>
                </div>
                <iframe
                  srcDoc={iframeSource}
                  title="output"
                  sandbox="allow-scripts"
                  width="100%"
                  height="calc(100vh - 50px)"
                  style={{ flex: "1 1", border: 0 }}
                />
              </div>
            </div>
          </div>
        )}
      </Layout>
    </div>
  );
};

export default MobileWebsiteEditorPage;
