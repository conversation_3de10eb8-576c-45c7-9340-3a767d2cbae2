/* eslint-disable react/no-unknown-property */
// @ts-nocheck
import Layout from "pages/layouts/Layout";
import { Table, Input, Button, Form, Tooltip, Popover, message } from "antd";
import { EyeOutlined, EditOutlined, EyeInvisibleOutlined } from "@ant-design/icons";
import dayjs from "dayjs";
import { useEffect, useState } from "react";
import { BlitzPage } from "@blitzjs/next";
import SectionHeadings from "app/components/SectionHeadings";
import { useMutation } from "@blitzjs/rpc";
import queryMobileWebsites from "app/h5/mutations/queryMobileWebsites";
import { useRouter } from "next/router";
import React from "react";

const MobileWebsiteManagementPage: BlitzPage = (props) => {
  const [form] = Form.useForm();
  const [isClientSide, setIsClientSide] = useState(false);
  const [websitesData, setWebsitesData] = useState([]);
  const [queryWebsitesMutation] = useMutation(queryMobileWebsites, {});
  const router = useRouter();

  useEffect(() => {
    setIsClientSide(true);
    void fetchWebsitesData();
  }, []);

  const fetchWebsitesData = async () => {
    const response = await queryWebsitesMutation({});
    if (response.entry) {
      setWebsitesData(response.entry);
    }
  };

  const handleFormSubmit = async (values) => {
    const response = await queryWebsitesMutation({ title: values.title });
    setWebsitesData(response.entry);
  };

  const handleFormReset = async () => {
    form.resetFields();
    await fetchWebsitesData();
  };

  const handlePreview = (website) => {
    window.open(`https://s.xinc818.com/flow-h5/prod/${website.id}/${website.uuid}.html`, '_blank');
  };

  const columns = [
    {
      title: '页面名称',
      dataIndex: 'title',
      key: 'title',
      render: (text, record) => (
        <div className="flex items-center">
          <span className="mr-2">{text.length > 20 ? `${text.substring(0, 20)}...` : text}</span>
          <Popover
            content={
              <iframe
                src={`https://s.xinc818.com/flow-h5/prod/${record.id}/${record.uuid}.html`}
                title={text}
                width="300"
                height="200"
                onError={(e) => {
                  e.target.style.display = 'none';
                  e.target.nextSibling.style.display = 'flex';
                }}
              />
            }
            title="预览"
            trigger="hover"
            placement="topRight"
          >
            <Tooltip title="预览">
              <EyeOutlined className="cursor-pointer text-blue-500 hover:text-blue-700" />
            </Tooltip>
          </Popover>
        </div>
      ),
    },
    {
      title: '作者',
      dataIndex: ['author', 'name'],
      key: 'author',
    },
    {
      title: '描述',
      dataIndex: 'description',
      key: 'description',
      render: (text) => (
        <Tooltip title={text || "暂无描述"}>
          <span>{text ? (text.length > 30 ? `${text.substring(0, 30)}...` : text) : "暂无描述"}</span>
        </Tooltip>
      ),
    },
    {
      title: '创建时间',
      dataIndex: 'createdAt',
      key: 'createdAt',
      render: (text) => dayjs(text).format('YYYY-MM-DD HH:mm'),
    },
    {
      title: '更新时间',
      dataIndex: 'updatedAt',
      key: 'updatedAt',
      render: (text) => dayjs(text).format('YYYY-MM-DD HH:mm'),
    },
    {
      title: '操作',
      key: 'action',
      render: (_, record) => (
        <span>
          <Tooltip title="预览">
            <Button
              icon={<EyeOutlined />}
              onClick={() => handlePreview(record)}
              style={{ marginRight: 8 }}
            />
          </Tooltip>
          <Tooltip title="编辑">
            <Button
              icon={<EditOutlined />}
              onClick={() => router.push({ pathname: "/cloud/htmlEditor/h5", query: { id: record.id } })}
            />
          </Tooltip>
        </span>
      ),
    },
  ];

  return (
    <Layout title="Mobile Websites" {...props}>
      <SectionHeadings
        name="EasyH5"
        btnText="新增H5页面"
        onClick={() => router.push({ pathname: "/cloud/htmlEditor/h5" })}
      />
      <Form
        layout="inline"
        form={form}
        name="search-websites"
        onFinish={handleFormSubmit}
        style={{ marginBottom: 16 }}
      >
        <Form.Item name="title" label="页面名">
          <Input placeholder="请输入页面名" />
        </Form.Item>
        <Form.Item>
          <Button type="primary" htmlType="submit" style={{ marginRight: 8 }}>
            查询
          </Button>
          <Button htmlType="button" onClick={handleFormReset}>
            重置
          </Button>
        </Form.Item>
      </Form>
      <Table
        columns={columns}
        dataSource={websitesData}
        rowKey="id"
        pagination={{ pageSize: 10 }}
      />
    </Layout>
  );
};

export default MobileWebsiteManagementPage;
