/* eslint-disable react/no-unknown-property */
// @ts-nocheck
import React, { useEffect, useState } from "react"
import Layout from "pages/layouts/Layout"
import { Text, Flex, CopyButton } from "@mantine/core"
import { Table, Modal, Input, Tag, Tabs, Select, Form, Switch, Radio, Empty, Button, Image } from "antd"
import { useMutation, useQuery } from "@blitzjs/rpc"
import { Routes, BlitzPage } from "@blitzjs/next"
import SectionHeadings from "app/components/SectionHeadings"
import queryMobileWebsites from "app/h5/mutations/queryMobileWebsites"
import queryGitlabCode from "app/cloud/mutations/searchBackEndCode"
import { useRouter } from "next/router"
import CodeMirror from "@uiw/react-codemirror"
import { StreamLanguage } from "@codemirror/language"
import { javascript } from "@codemirror/legacy-modes/mode/javascript"
import { showNotification, cleanNotifications } from "@mantine/notifications"
import { PencilSquareIcon } from "@heroicons/react/24/outline"
import { SEARCH_CONFIG, ERROR_MESSAGES, SUCCESS_MESSAGES } from "app/cloud/config/searchConfig"
import SearchMonitor from "app/cloud/components/SearchMonitor"

const { TabPane } = Tabs

/**
 * Function to sort projects by content length.
 * @param a - First project.
 * @param b - Second project.
 * @returns Sorted projects.
 */
function sortProjects(a, b) {
  return b.content.length - a.content.length
}

const BackendCodeSearchPage: BlitzPage = (props) => {
  const [form] = Form.useForm()
  const extensions = [StreamLanguage.define(javascript)]
  const [isClient, setIsClient] = useState(false)
  const [activeKey, setActiveKey] = useState("0")
  const [data, setData] = useState([])
  const [loading, setLoading] = useState(false)
  const [lastSearchTime, setLastSearchTime] = useState(0)
  const [selectedGroup, setSelectedGroup] = useState("basic_framework")
  const router = useRouter()

  // Ensure the component is rendered on the client-side
  useEffect(() => {
    setIsClient(true)
  }, [])

  // Handle tab click event
  const onTabClick = (key) => {
    setActiveKey(key)
  }

  // Handle form submission
  const onFinish = async (values) => {
    // 防止频繁搜索
    const now = Date.now()
    if (now - lastSearchTime < SEARCH_CONFIG.MIN_SEARCH_INTERVAL) {
      showNotification({
        title: "提示",
        message: ERROR_MESSAGES.FREQUENT_SEARCH,
        color: "orange",
        autoClose: SEARCH_CONFIG.NOTIFICATION_DURATION.WARNING,
      })
      return
    }

    // 验证搜索内容
    if (!values.search || values.search.trim().length < SEARCH_CONFIG.MIN_SEARCH_LENGTH) {
      showNotification({
        title: "提示",
        message: ERROR_MESSAGES.SHORT_QUERY,
        color: "orange",
        autoClose: SEARCH_CONFIG.NOTIFICATION_DURATION.WARNING,
      })
      return
    }

    console.log(values)
    setLoading(true)
    setLastSearchTime(now)
    setSelectedGroup(values.group)
    setActiveKey("0")

    showNotification({
      title: "温馨提示",
      message: SUCCESS_MESSAGES.SEARCH_STARTED,
      autoClose: SEARCH_CONFIG.NOTIFICATION_DURATION.SEARCHING,
    })

    try {
      const result = await queryGitlabCode({
        ...values
      })

      cleanNotifications()

      if (result && result.status) {
        const sortedEntries = result.entry.sort(sortProjects)
        setData(sortedEntries)

        // 统计搜索结果
        const totalResults = sortedEntries.reduce((sum, item) => sum + item.content.length, 0)
        showNotification({
          title: "搜索完成",
          message: SUCCESS_MESSAGES.SEARCH_COMPLETE(totalResults),
          color: "green",
          autoClose: SEARCH_CONFIG.NOTIFICATION_DURATION.SUCCESS,
        })
      } else {
        showNotification({
          title: "搜索失败",
          message: ERROR_MESSAGES.NO_RESULTS,
          color: "red",
          autoClose: SEARCH_CONFIG.NOTIFICATION_DURATION.ERROR,
        })
      }
    } catch (error) {
      console.error('搜索出错:', error)
      cleanNotifications()
      showNotification({
        title: "搜索出错",
        message: ERROR_MESSAGES.NETWORK_ERROR,
        color: "red",
        autoClose: SEARCH_CONFIG.NOTIFICATION_DURATION.ERROR,
      })
    } finally {
      setLoading(false)
    }
  }

  // Handle form reset
  const onReset = async () => {
    form.resetFields()
  }

  // Render search results
  const renderResult = () => (
    <Tabs activeKey={activeKey} onChange={onTabClick}>
      {data.map((item, index) => (
        <TabPane tab={`${item.projectName}(${item.content.length >= 30 ? "30+" : item.content.length})`} key={index}>
          {item.content?.length > 0 ? (
            <ul>
              {item.content.map((content, idx) => (
                <li key={idx}>
                  <p>
                    <a href={`${item.projectLink}${content.path}`} className="flow-link" target="_blank" rel="noreferrer">
                      {content.path}:
                    </a>
                  </p>
                  <CodeMirror
                    extensions={extensions}
                    editable={true}
                    className="mb-10 border"
                    placeholder=""
                    indentWithTab={true}
                    lineWrapping={true}
                    value={content.data}
                  />
                </li>
              ))}
            </ul>
          ) : (
            <p className="mt-20">
              <Empty />
            </p>
          )}
        </TabPane>
      ))}
    </Tabs>
  )

  return (
    <Layout title="projects" {...props}>
      <SectionHeadings name="后端代码搜索" />

      <SearchMonitor
        loading={loading}
        selectedGroup={selectedGroup}
        searchResults={data}
        lastSearchTime={lastSearchTime}
      />

      <Form
        layout="inline"
        className="mb-5"
        initialValues={{
          search: "",
          group: "basic_framework",
        }}
        form={form}
        name="control-hooks"
        onFinish={onFinish}
      >
        <Form.Item name="search" style={{ width: "300px" }} label="搜索内容">
          <Input />
        </Form.Item>
        <Form.Item name="group" label="项目组" style={{ width: "450px" }}>
          <Select
            options={[
              { value: "basic_framework", label: "group: basic_framework / supplierChain / base_service" },
              { value: "item", label: "group: item / trade / antifraud" },
              { value: "other", label: "group:customer / marketing / purchase/ outway/ 等等.." },
              { value: "bigdata", label: "group: bigdata" },
            ]}
          />
        </Form.Item>
        <Form.Item>
          <Button
            type="primary"
            htmlType="submit"
            loading={loading}
            disabled={loading}
            style={{ marginRight: "10px" }}
          >
            {loading ? "搜索中..." : "查询"}
          </Button>
          <Button
            htmlType="button"
            onClick={onReset}
            disabled={loading}
            style={{ marginRight: "10px" }}
          >
            重置
          </Button>
        </Form.Item>
      </Form>
      {data && data.length > 0 ? renderResult() : null}
    </Layout>
  )
}

export default BackendCodeSearchPage
