/* eslint-disable react/no-unknown-property */
// @ts-nocheck
import React, { useEffect, useState } from "react"
import Layout from "pages/layouts/Layout"
import { Text, Flex, CopyButton } from "@mantine/core"
import { Table, Modal, Input, Tag, Tabs, Select, Form, Switch, Radio, Empty, Button, Image } from "antd"
import { useMutation, useQuery } from "@blitzjs/rpc"
import { Routes, BlitzPage } from "@blitzjs/next"
import SectionHeadings from "app/components/SectionHeadings"
import queryMobileWebsites from "app/h5/mutations/queryMobileWebsites"
import queryGitlabCode from "app/cloud/mutations/searchGitlabCode"
import { useRouter } from "next/router"
import CodeMirror from "@uiw/react-codemirror"
import { StreamLanguage } from "@codemirror/language"
import { javascript } from "@codemirror/legacy-modes/mode/javascript"
import { showNotification, cleanNotifications } from "@mantine/notifications"
import { PencilSquareIcon } from "@heroicons/react/24/outline"

const { TabPane } = Tabs

/**
 * Function to sort projects by content length.
 * @param a - First project.
 * @param b - Second project.
 * @returns Sorted projects.
 */
function sortProjects(a, b) {
  return b.content.length - a.content.length
}

const CodeSearchPage: BlitzPage = (props) => {
  const [form] = Form.useForm()
  const extensions = [StreamLanguage.define(javascript)]
  const [isClient, setIsClient] = useState(false)
  const [activeKey, setActiveKey] = useState("0")
  const [data, setData] = useState([])
  const router = useRouter()

  // Ensure the component is rendered on the client-side
  useEffect(() => {
    setIsClient(true)
  }, [])

  // Handle tab click event
  const onTabClick = (key) => {
    setActiveKey(key)
  }

  // Handle form submission
  const onFinish = async (values) => {
    console.log(values)
    setActiveKey("0")
    showNotification({
      title: "温馨提示",
      message: "系统搜索中.. 🐟",
      autoClose: 6000,
    })
    const result = await queryGitlabCode({
      ...values
    })
    cleanNotifications()
    if (result && result.status) {
      const sortedEntries = result.entry.sort(sortProjects)
      setData(sortedEntries)
    }
  }

  // Handle form reset
  const onReset = async () => {
    form.resetFields()
  }

  // Render search results
  const renderResult = () => (
    <Tabs activeKey={activeKey} onChange={onTabClick}>
      {data.map((item, index) => (
        <TabPane tab={`${item.projectName}(${item.content.length >= 30 ? "30+" : item.content.length})`} key={index}>
          {item.content?.length > 0 ? (
            <ul>
              {item.content.map((content, idx) => (
                <li key={idx}>
                  <p>
                    <a href={`${item.projectLink}${content.path}`} className="flow-link" target="_blank" rel="noreferrer">
                      {content.path}:
                    </a>
                  </p>
                  <CodeMirror
                    extensions={extensions}
                    editable={true}
                    className="mb-10 border"
                    placeholder=""
                    indentWithTab={true}
                    lineWrapping={true}
                    value={content.data}
                  />
                </li>
              ))}
            </ul>
          ) : (
            <p className="mt-20">
              <Empty />
            </p>
          )}
        </TabPane>
      ))}
    </Tabs>
  )

  return (
    <Layout title="projects" {...props}>
      <SectionHeadings name="代码搜索" />
      <Form
        layout="inline"
        className="mb-5"
        initialValues={{
          search: "",
          group: "supply",
        }}
        form={form}
        name="control-hooks"
        onFinish={onFinish}
      >
        <Form.Item name="search" style={{ width: "300px" }} label="搜索内容">
          <Input />
        </Form.Item>
        <Form.Item name="group" label="项目组">
          <Select
            options={[
              { value: "supply", label: "PC端" },
              { value: "jf", label: "小程序" },
            ]}
          />
        </Form.Item>
        <Form.Item>
          <Button type="primary" htmlType="submit" style={{ marginRight: "10px" }}>
            查询
          </Button>
          <Button htmlType="button" onClick={onReset} style={{ marginRight: "10px" }}>
            重置
          </Button>
        </Form.Item>
      </Form>
      {data && data.length > 0 ? renderResult() : null}
    </Layout>
  )
}

export default CodeSearchPage
