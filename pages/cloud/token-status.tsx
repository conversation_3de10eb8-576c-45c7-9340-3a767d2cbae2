import React, { useState, useEffect } from "react"
import Layout from "pages/layouts/Layout"
import { Card, Button, Space, Tag, Descriptions, Alert, Spin, Typography } from "antd"
import { 
  CheckCircleOutlined, 
  CloseCircleOutlined, 
  ReloadOutlined,
  WarningOutlined,
  KeyOutlined,
  SearchOutlined,
  DatabaseOutlined
} from "@ant-design/icons"
import { useMutation } from "@blitzjs/rpc"
import { BlitzPage } from "@blitzjs/next"
import SectionHeadings from "app/components/SectionHeadings"
import checkGitlabToken from "app/cloud/mutations/checkGitlabToken"

const { Title, Text, Paragraph } = Typography

const TokenStatusPage: BlitzPage = (props) => {
  const [tokenData, setTokenData] = useState<any>(null)
  const [loading, setLoading] = useState(false)
  const [checkGitlabTokenMutation] = useMutation(checkGitlabToken)

  const checkToken = async (action: string = 'validate') => {
    setLoading(true)
    try {
      const result = await checkGitlabTokenMutation({ action })
      setTokenData(result)
    } catch (error) {
      console.error('检查Token失败:', error)
      setTokenData({
        status: false,
        message: '检查失败',
        data: { error: error.message }
      })
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    checkToken()
  }, [])

  const getStatusIcon = (status: boolean) => {
    return status ? (
      <CheckCircleOutlined style={{ color: '#52c41a' }} />
    ) : (
      <CloseCircleOutlined style={{ color: '#ff4d4f' }} />
    )
  }

  const getStatusTag = (status: boolean, text: string) => {
    return (
      <Tag color={status ? 'success' : 'error'} icon={getStatusIcon(status)}>
        {text}
      </Tag>
    )
  }

  const renderTokenInfo = () => {
    if (!tokenData?.data?.tokenInfo) return null

    const { tokenInfo } = tokenData.data
    
    return (
      <Card title="Token详细信息" style={{ marginTop: 16 }}>
        <Descriptions column={2} bordered>
          <Descriptions.Item label="Token ID">
            {tokenInfo.tokenId || 'N/A'}
          </Descriptions.Item>
          <Descriptions.Item label="Token名称">
            {tokenInfo.name || 'N/A'}
          </Descriptions.Item>
          <Descriptions.Item label="权限范围">
            {tokenInfo.scopes?.join(', ') || 'N/A'}
          </Descriptions.Item>
          <Descriptions.Item label="状态">
            {getStatusTag(tokenInfo.active, tokenInfo.active ? '活跃' : '非活跃')}
          </Descriptions.Item>
          <Descriptions.Item label="创建时间">
            {tokenInfo.createdAt ? new Date(tokenInfo.createdAt).toLocaleString() : 'N/A'}
          </Descriptions.Item>
          <Descriptions.Item label="最后使用">
            {tokenInfo.lastUsedAt ? new Date(tokenInfo.lastUsedAt).toLocaleString() : '从未使用'}
          </Descriptions.Item>
          <Descriptions.Item label="过期时间" span={2}>
            {tokenInfo.expiresAt ? (
              <Text type={new Date(tokenInfo.expiresAt) < new Date() ? 'danger' : 'success'}>
                {new Date(tokenInfo.expiresAt).toLocaleString()}
                {new Date(tokenInfo.expiresAt) < new Date() && ' (已过期)'}
              </Text>
            ) : (
              <Text type="success">永不过期</Text>
            )}
          </Descriptions.Item>
        </Descriptions>
      </Card>
    )
  }

  const renderEnvironmentInfo = () => {
    if (!tokenData?.data?.environment) return null

    const { environment } = tokenData.data
    
    return (
      <Card title="环境配置" style={{ marginTop: 16 }}>
        <Descriptions column={1} bordered>
          <Descriptions.Item label="配置源">
            {environment.source}
          </Descriptions.Item>
          <Descriptions.Item label="Token状态">
            {getStatusTag(environment.hasToken, environment.hasToken ? '已配置' : '未配置')}
          </Descriptions.Item>
          {environment.tokenPreview && (
            <Descriptions.Item label="Token预览">
              <Text code>{environment.tokenPreview}</Text>
            </Descriptions.Item>
          )}
        </Descriptions>
      </Card>
    )
  }

  return (
    <Layout title="Token状态检查" {...props}>
      <SectionHeadings name="GitLab Token 状态检查" />
      
      <Card>
        <Space direction="vertical" style={{ width: '100%' }}>
          <Space>
            <Button 
              type="primary" 
              icon={<KeyOutlined />}
              loading={loading}
              onClick={() => checkToken('validate')}
            >
              验证Token
            </Button>
            <Button 
              icon={<SearchOutlined />}
              loading={loading}
              onClick={() => checkToken('test-search')}
            >
              测试搜索权限
            </Button>
            <Button 
              icon={<DatabaseOutlined />}
              loading={loading}
              onClick={() => checkToken('list-tokens')}
            >
              获取Token列表
            </Button>
            <Button 
              icon={<ReloadOutlined />}
              loading={loading}
              onClick={() => checkToken('check-env')}
            >
              检查环境变量
            </Button>
          </Space>

          {loading && (
            <div style={{ textAlign: 'center', padding: '20px' }}>
              <Spin size="large" />
              <div style={{ marginTop: 8 }}>检查中...</div>
            </div>
          )}

          {tokenData && !loading && (
            <>
              <Alert
                message={tokenData.message}
                type={tokenData.status ? 'success' : 'error'}
                icon={getStatusIcon(tokenData.status)}
                showIcon
              />

              {renderEnvironmentInfo()}
              {renderTokenInfo()}

              {tokenData.data?.tokens && (
                <Card title="用户Token列表" style={{ marginTop: 16 }}>
                  {tokenData.data.tokens.length > 0 ? (
                    tokenData.data.tokens.map((token: any, index: number) => (
                      <Card key={index} size="small" style={{ marginBottom: 8 }}>
                        <Descriptions column={3} size="small">
                          <Descriptions.Item label="名称">{token.name}</Descriptions.Item>
                          <Descriptions.Item label="状态">
                            {getStatusTag(token.active, token.active ? '活跃' : '非活跃')}
                          </Descriptions.Item>
                          <Descriptions.Item label="过期时间">
                            {token.expires_at || '永不过期'}
                          </Descriptions.Item>
                        </Descriptions>
                      </Card>
                    ))
                  ) : (
                    <Text type="secondary">没有找到Token</Text>
                  )}
                </Card>
              )}
            </>
          )}
        </Space>
      </Card>

      <Card title="常见问题解答" style={{ marginTop: 16 }}>
        <Space direction="vertical" style={{ width: '100%' }}>
          <div>
            <Title level={5}>
              <WarningOutlined style={{ color: '#faad14' }} /> 为什么删除Token后还能使用？
            </Title>
            <Paragraph>
              <ul>
                <li><strong>缓存机制</strong>：应用或GitLab服务器可能有缓存，需要时间生效</li>
                <li><strong>异步删除</strong>：GitLab的删除操作可能是异步的，需要几分钟生效</li>
                <li><strong>多个Token</strong>：可能存在多个相同名称的Token</li>
                <li><strong>权限继承</strong>：可能通过组织或项目权限继承了访问权限</li>
              </ul>
            </Paragraph>
          </div>
          
          <div>
            <Title level={5}>解决方案</Title>
            <Paragraph>
              <ol>
                <li>等待5-10分钟让删除操作完全生效</li>
                <li>检查是否有多个同名Token</li>
                <li>重启应用清除缓存</li>
                <li>检查组织级别的Access Token</li>
                <li>联系GitLab管理员确认删除状态</li>
              </ol>
            </Paragraph>
          </div>
        </Space>
      </Card>
    </Layout>
  )
}

export default TokenStatusPage
