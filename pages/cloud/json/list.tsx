import { EllipsisOutlined, EnvironmentOutlined, WechatOutlined } from "@ant-design/icons"
import Layout from "pages/layouts/Layout"
import { Card, Col, Row, Typography, Tooltip, Tag, Space, Avatar } from "antd"
import React, { useState, useEffect, useCallback } from "react"
import { useMutation } from "@blitzjs/rpc"
import queryAppList from "app/cloud/mutations/queryAppList"
import { useRouter } from "next/router"
import SectionHeadings from "app/components/SectionHeadings"

const { Title, Text } = Typography;

interface Project {
  id: string
  name: string
  description: string
  info?: {
    bgColor?: string
  }
  environments: string[]
  isWechatMiniProgram: boolean
}

export default function ProjectList(props: any) {
  const [projectData, setProjectData] = useState<Project[]>([])
  const [queryAppListMutation] = useMutation(queryAppList)
  const router = useRouter()

  const fetchProjectData = useCallback(async () => {
    const response = await queryAppListMutation({})
    if (response.entry) {
      const formattedData = response.entry.map((item: any) => ({
        id: item.id,
        name: item.appName,
        description: item.description || '-',
        info: { bgColor: item?.info?.bgColor },
        environments: Array.from(new Set(item.jsonConfigs?.map((config: any) => config.env) || [])) as string[],
        isWechatMiniProgram: item.appName.toLowerCase().includes('mini') ||
          item.appName.toLowerCase().includes('weapp') ||
          item.appName.includes('小程序') ||
          (item.description && (
            item.description.toLowerCase().includes('mini') ||
            item.description.toLowerCase().includes('weapp') ||
            item.description.includes('小程序')
          ))
      }))
      setProjectData(formattedData as Project[])
    }
  }, [queryAppListMutation])

  useEffect(() => {
    void fetchProjectData()
  }, [fetchProjectData])

  const handleProjectClick = (projectId: string) => {
    void router.push({
      pathname: "/cloud/json/config",
      query: { id: projectId },
    })
  }

  return (
    <Layout title="JSON配置平台" {...props}>
      <SectionHeadings name="项目列表" />
      <Row gutter={[24, 24]}>
        {projectData.map((project) => (
          <Col xs={24} sm={12} md={8} lg={6} key={project.id}>
            <Card
              hoverable
              onClick={() => handleProjectClick(project.id)}
              actions={[
                <Tooltip title="查看详情" key="ellipsis">
                  <EllipsisOutlined key="ellipsis" />
                </Tooltip>,
              ]}
              style={{ height: '100%' }}
            >
              <Space direction="vertical" size="small" style={{ width: '100%' }}>
                <Space align="center">
                  <Avatar
                    style={{
                      backgroundColor: project.info?.bgColor || '#1890ff',
                      verticalAlign: 'middle',
                    }}
                    size="large"
                  >
                    {project.name.slice(0, 1).toUpperCase()}
                  </Avatar>
                  <Title level={4} style={{ margin: 0 }}>
                    {project.name}
                    {project.isWechatMiniProgram && (
                      <Tooltip title="微信小程序">
                        <WechatOutlined style={{ marginLeft: 8, color: '#52c41a' }} />
                      </Tooltip>
                    )}
                  </Title>
                </Space>
                <Text type="secondary" ellipsis={{ tooltip: true }} title={project.description}>
                  {project.description}
                </Text>
                <Space wrap>
                  <EnvironmentOutlined />
                  {project.environments.map((env, index) => (
                    <Tag key={index} color="blue">{env}</Tag>
                  ))}
                </Space>
              </Space>
            </Card>
          </Col>
        ))}
      </Row>
    </Layout>
  )
}
