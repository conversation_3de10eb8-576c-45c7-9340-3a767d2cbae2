import { EllipsisVerticalIcon } from "@heroicons/react/20/solid"
import Layout from "pages/layouts/Layout"
import { Table, Modal, Input, Button, Space } from "antd"
import { Text, Flex } from "@mantine/core"
import type { ColumnsType } from "antd/es/table"
import { Tooltip } from "@mantine/core"
import { StreamLanguage } from "@codemirror/language"
import { json } from "@codemirror/legacy-modes/mode/javascript"
import type { TableRowSelection } from "antd/es/table/interface"
import React, { useState, useEffect } from "react"
import { javascript } from '@codemirror/lang-javascript';
import { useMutation, useQuery } from "@blitzjs/rpc"
import { useClipboard } from "@mantine/hooks"
import { Tag, Tabs, Select, Form, Switch, Radio, Image } from "antd"
import { showNotification } from "@mantine/notifications"
import { IconX } from "@tabler/icons"
import dayjs from "dayjs"
import getJsonDetail from "@/app/cloud/mutations/getJsonDetail"
import getJsonHistory from "@/app/cloud/mutations/getJsonHistory"
import submitDetail from "@/app/cloud/mutations/submitJson"
import uploadJson from "@/app/cloud/mutations/uploadJson"
import { Router, useRouter } from "next/router"
import { getEmployeesByTeam } from "@/constants/employees"
import { invoke } from "@blitzjs/rpc"
import getToken from "app/users/queries/getToken"
import { CopyOutlined } from "@ant-design/icons"
import { IconCopy } from "@tabler/icons"
import CodeMirror from "@uiw/react-codemirror"
// import locale from "react-json-editor-ajrm/locale/en"
import SectionHeadings from "app/components/SectionHeadings"
import queryJsonHistory from "@/app/cloud/mutations/getJsonHistory"

// import { buffer } from "stream/consumers"
const { confirm } = Modal
const { Option } = Select

const projects = [
  { name: "Graph API", initials: "GA", href: "#", members: 16, bgColor: "bg-pink-600" },
  { name: "Component Design", initials: "CD", href: "#", members: 12, bgColor: "bg-purple-600" },
  { name: "Templates", initials: "T", href: "#", members: 16, bgColor: "bg-yellow-500" },
  { name: "React Components", initials: "RC", href: "#", members: 8, bgColor: "bg-green-500" },
]

const tabs = [
  { name: "DEV", href: "#", current: true },
  { name: "DAILY", href: "#", current: false },
  { name: "GRAY", href: "#", current: false },
  { name: "RPOD", href: "#", current: false },
]

function classNames(...classes) {
  return classes.filter(Boolean).join(" ")
}

const formatJson = (json) => {
  const result = JSON.stringify(json, null, "\t")
  return result
}

export default function List(props) {
  const router = useRouter()
  const id = router.query.id
  const [code, setCode] = useState("{}")
  const [data, setData] = useState([])
  const [historyData, setHistoryData] = useState([]);
  const [detail, setDetail] = useState({}) as any
  const [errorTips, setErrorTips] = useState("")
  const [getDetailMutation] = useMutation(getJsonDetail, {})
  const [submitDetailMutation] = useMutation(submitDetail, {})
  const [uploadJsonMutation] = useMutation(uploadJson, {})
  const [queryJsonHistoryMutation] = useMutation(queryJsonHistory, {})

  const url = `https://s.xinc818.com/flow-app-config/${router.query.name}/${router.query.env}.json`

  const getDetail = async () => {
    const res = (await getDetailMutation({
      id: id,
    })) as any

    if (res.status) {
      setDetail(res.entry)
      setCode(formatJson(res.entry?.content) || "{}")
    } else {
      showNotification({
        title: "Error",
        message: "error",
        color: "red",
      })
    }
  }

  useEffect(() => {
    void getDetail()
  }, [])

  const handlerSave = async () => {
    try {
      const json = JSON.parse(code)
      // 字符串为合法的 JSON 数据，可以在这里对 JSON 数据进行操作
    } catch (e) {
      showNotification({
        title: "Error",
        message: "json格式化失败",
        color: "red",
      })
      return false
      // 捕获到异常，说明字符串不是合法的 JSON 数据
    }
    setCode(formatJson(JSON.parse(code)))
    const res = await submitDetailMutation({
      id: id,
      content: JSON.parse(code),
      // content: {
      //   json_str: code,
      //   json_obj: JSON.parse(code),
      // },
    })
    if (res.status) {
      showNotification({
        title: "Success",
        message: "保存成功",
        color: "green",
      })
      return true
    } else {
      showNotification({
        title: "Error",
        message: "保存失败",
        color: "red",
      })
      return false
    }
  }

  const handlerSaveCdn = async () => {
    const flag = await handlerSave()
    if (flag) {
      const res = (await uploadJsonMutation({
        id: id,
        content: JSON.parse(code),
      })) as any
      if (res.status) {
        showNotification({
          title: "Success",
          message: "同步成功",
          color: "green",
        })
      } else {
        showNotification({
          title: "Error",
          message: "同步CDN失败",
          color: "red",
        })
      }
    } else {
      showNotification({
        title: "Error",
        message: "保存失败",
        color: "red",
      })
    }
  }

  const onChange = async (key) => {
    if (key == '2') {
      const result = await queryJsonHistoryMutation({
        id: id
      })
      setHistoryData(result.entry || [])
    }
  }

  const columns: ColumnsType<any> = [
    {
      title: "发布时间",
      width: "60px",
      dataIndex: "createdAt",
      render(value, record, index) {
        return value ? dayjs(value).format("YYYY年MM月DD日 HH:mm:ss") : "-"
      },
    },

    {
      title: "预览地址",
      width: "120px",
      dataIndex: "url",
      render(value, record, index) {
        return <a href={value} target="_blank" rel="noreferrer">{value}</a>
      },
    }
  ]

  let items = [
    {
      key: "1",
      label: `JSON编辑`,
      children: (
        <CodeMirror
          editable={true}
          className="mb-10 border"
          placeholder={""}
          value={code}
          minHeight="600px"
          extensions={[
            //StreamLanguage.define(json)
            javascript({ jsx: true })
          ]}
          // onBlur={() => {
          //   verifyJson()
          // }}
          onChange={(value) => {
            setCode(value)
          }}
        />
      ),
    }
  ]

  if (!['dev', 'daily', 'gray'].includes(router.query.env as string)) {
    items.push({
      key: "2",
      label: `历史记录`,
      children: (
        <Table
          columns={columns}
          style={{ 'minHeight': '600px' }}
          pagination={{ pageSize: 20 }}
          dataSource={historyData || []}
          scroll={{ x: "max-content", }}
        />
      )
    })
  }


  return (
    <Layout title="json-detail" {...props}>
      <SectionHeadings name="JSON修改" />
      <p className="mt-2 max-w-4xl text-sm text-gray-500">
        <div>当前环境 : <span className="text-lg text-red-500">{router.query.env}</span></div>
        <div>最近发布时间 : {detail?.publishTime ? detail?.publishTime : "暂未发布"}</div>
        <div>最近修改信息 : {detail?.updateTime ? detail?.updateTime : "暂未修改"}</div>
      </p>
      <Tabs defaultActiveKey="1" items={items} onChange={onChange} />


      <Button
        onClick={async () => {
          try {
            const result = JSON.stringify(JSON.parse(code), null, "\t")
            setCode(result)
          } catch (e) {
            showNotification({
              title: "Error",
              message: "格式化失败",
              color: "red",
            })
          }
        }}
      >
        格式化
      </Button>
      <Button type="primary" className="ml-2" onClick={handlerSave}>
        保存JSON
      </Button>
      <Button
        type="primary"
        className="ml-2"
        onClick={() => {
          confirm({
            title: "温馨提示!",
            content: "确定部署到线上??",
            onOk: async () => {
              await handlerSaveCdn()
            },
            onCancel() {
              console.log("Cancel")
            },
          })
        }}
      >
        保存JSON并同步CDN
      </Button>
      <a
        className="ml-4 mr-2 text-blue-600 underline hover:text-blue-700"
        href={url}
        target={"_blank"}
        rel="noreferrer"
      >
        访问链接
      </a>
      {/* <JSONInput
        id="a_unique_id"
        placeholder={""}
        // colors={darktheme}
        // locale={locale}
        height="550px"
      /> */}
    </Layout>
  )
}
