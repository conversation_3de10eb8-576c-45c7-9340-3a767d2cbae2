/* eslint-disable react/no-unescaped-entities */
import { EllipsisVerticalIcon } from "@heroicons/react/20/solid"
import Layout from "pages/layouts/Layout"
import { Table, Modal, Input, Button } from "antd"
import { Text, Flex } from "@mantine/core"
import type { ColumnsType } from "antd/es/table"
import { Tooltip } from "@mantine/core"
import type { TableRowSelection } from "antd/es/table/interface"
import React, { useState, Fragment, useEffect, useRef } from "react"
import { useMutation, useQuery } from "@blitzjs/rpc"
import { useClipboard } from "@mantine/hooks"
import { Tag, Tabs, Select, Form, Switch, Radio, Image } from "antd"
import { showNotification } from "@mantine/notifications"
import { IconX } from "@tabler/icons"
import dayjs from "dayjs"
import queryAppConfigList from "app/cloud/mutations/queryAppConfigList"
import addConfig from "app/cloud/mutations/addConfig"
import SectionHeadings from "app/components/SectionHeadings"
import deleteTask from "app/task/mutations/delete"
import { Router, useRouter } from "next/router"
import { Dialog, Transition } from "@headlessui/react"
import { CheckIcon, ArrowPathIcon, PlusCircleIcon } from "@heroicons/react/24/outline"
import getJsonDetail from "@/app/cloud/mutations/getJsonDetail"
import { getEmployeesByTeam } from "@/constants/employees"
import { invoke } from "@blitzjs/rpc"
import getToken from "app/users/queries/getToken"
import { CopyOutlined } from "@ant-design/icons"
import { IconCopy } from "@tabler/icons"
// import { buffer } from "stream/consumers"
const { confirm } = Modal
const { Option } = Select

const projects = [
  { name: "Graph API", initials: "GA", href: "#", members: 16, bgColor: "bg-pink-600" },
  { name: "Component Design", initials: "CD", href: "#", members: 12, bgColor: "bg-purple-600" },
  { name: "Templates", initials: "T", href: "#", members: 16, bgColor: "bg-yellow-500" },
  { name: "React Components", initials: "RC", href: "#", members: 8, bgColor: "bg-green-500" },
]

const envColor = {
  dev: "bg-gray-400",
  daily: "bg-orange-400",
  gray: "bg-gray-600",
  prod: "bg-green-600",
  default: "bg-blue-400",
}

function classNames(...classes) {
  return classes.filter(Boolean).join(" ")
}

export default function List(props) {
  const [loaded, setLoaded] = useState(false)
  const [data, setData] = useState([])
  const [name, setName] = useState("dev")
  const [queryAppConfigListMutation] = useMutation(queryAppConfigList, {})
  const router = useRouter()
  const cancelButtonRef = useRef(null)
  const [open, setOpen] = useState(false)
  const [addConfigMutation] = useMutation(addConfig, {})

  const getTableData = async () => {
    const res = await queryAppConfigListMutation({
      id: router.query.id,
    })
    setLoaded(true)
    let arr = [] as any
    if (res.entry) {
      arr = res.entry.map((item: any) => {
        return {
          id: item.id,
          env: item.env,
          name: item?.JsonConfigGroup?.appName,
          // info: item?.info,
          // members: item.jsonConfigs.length,
          bgColor: "bg-pink-600",
        }
      })
    }
    setData(arr || [])
  }

  useEffect(() => {
    void getTableData()
  }, [])

  return (
    <Layout title="upload" {...props}>
      <SectionHeadings
        name="环境配置"
        btnText="新建配置"
        onClick={() => {
          setOpen(true)
        }}
      />
      <Transition.Root show={open} as={Fragment}>
        <Dialog as="div" className="relative z-10" initialFocus={cancelButtonRef} onClose={setOpen}>
          <Transition.Child
            as={Fragment}
            enter="ease-out duration-300"
            enterFrom="opacity-0"
            enterTo="opacity-100"
            leave="ease-in duration-200"
            leaveFrom="opacity-100"
            leaveTo="opacity-0"
          >
            <div className="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" />
          </Transition.Child>

          <div className="fixed inset-0 z-10 overflow-y-auto">
            <div className="flex min-h-full items-end justify-center p-4 text-center sm:items-center sm:p-0">
              <Transition.Child
                as={Fragment}
                enter="ease-out duration-300"
                enterFrom="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
                enterTo="opacity-100 translate-y-0 sm:scale-100"
                leave="ease-in duration-200"
                leaveFrom="opacity-100 translate-y-0 sm:scale-100"
                leaveTo="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
              >
                <Dialog.Panel className="relative transform overflow-hidden rounded-lg bg-white px-4 pb-4 pt-5 text-left shadow-xl transition-all sm:my-8 sm:w-full sm:max-w-lg sm:p-6">
                  <div>
                    <div className="mx-auto flex h-12 w-12 items-center justify-center rounded-full bg-green-100">
                      <PlusCircleIcon className="h-6 w-6 text-green-600" aria-hidden="true" />
                    </div>
                    <div>
                      <label
                        htmlFor="name"
                        className="mt-3px mt-3 block pl-0 text-sm font-medium leading-6 text-gray-900"
                      >
                        选择环境:
                      </label>
                      <div className="mt-2">
                        <select
                          id="location"
                          name="location"
                          onChange={(e) => {
                            // alert(e.target.value)
                            setName(e.target.value)
                          }}
                          className="mt-2 block w-full rounded-md border-0 py-1.5 pl-3 pr-10 text-gray-900 ring-1 ring-inset ring-gray-300 focus:ring-2 focus:ring-indigo-600 sm:text-sm sm:leading-6"
                          defaultValue="dev"
                        >
                          <option>dev</option>
                          <option>daily</option>
                          <option>gray</option>
                          <option>prod</option>
                        </select>
                        {/* <input
                          type="text"
                          name="name"
                          id="name"

                          className="block w-full rounded-full border-0 px-4 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"
                          placeholder="配置名, 如:dev, daily, gray, prod"
                        /> */}
                      </div>
                    </div>
                    <div className="mt-3 text-center sm:mt-5">
                      <Dialog.Title
                        as="h3"
                        className="text-base font-semibold leading-6 text-gray-900"
                      >
                        新增你的配置环境
                      </Dialog.Title>
                      <div className="mt-2">
                        <p className="text-left text-sm text-gray-500">
                          常用环境配置四种选项：dev、daily、gray、prod。
                          <br />
                          如果你的项目非常复杂，也可以支持用户自定义环境配置，联系ziya。
                        </p>
                      </div>
                    </div>
                  </div>
                  <div className="mt-5 sm:mt-6 sm:grid sm:grid-flow-row-dense sm:grid-cols-2 sm:gap-3">
                    <button
                      type="button"
                      className="inline-flex w-full justify-center rounded-md bg-indigo-600 px-3 py-2 text-sm font-semibold text-white shadow-sm hover:bg-indigo-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-indigo-600 sm:col-start-2"
                      onClick={async () => {
                        const res = await addConfigMutation({
                          id: router.query.id,
                          name: name,
                        })
                        if (res && res.status) {
                          showNotification({
                            title: "notification",
                            message: "创建成功啦! 🤥",
                            color: "green",
                          })
                          setOpen(false)
                          setTimeout(() => {
                            void getTableData()
                          }, 1000)
                        } else {
                          showNotification({
                            title: "notification",
                            message: "已存在配置,创建失败啦! 🤥",
                            color: "red",
                          })
                        }
                      }}
                    >
                      添加
                    </button>
                    <button
                      type="button"
                      className="mt-3 inline-flex w-full justify-center rounded-md bg-white px-3 py-2 text-sm font-semibold text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 hover:bg-gray-50 sm:col-start-1 sm:mt-0"
                      onClick={() => setOpen(false)}
                      ref={cancelButtonRef}
                    >
                      取消
                    </button>
                  </div>
                </Dialog.Panel>
              </Transition.Child>
            </div>
          </div>
        </Dialog>
      </Transition.Root>
      <div>
        {/* <h2 className="text-md mb-5 font-medium text-gray-500"></h2> */}
        <ul
          role="list"
          className="mt-3 grid grid-cols-1 gap-5 sm:grid-cols-2 sm:gap-6 lg:grid-cols-4"
        >
          {data.map((project: any) => (
            <li
              key={project.name}
              onClick={() => {
                void router.push({
                  pathname: "/cloud/json/detail",
                  query: { id: project.id, name: project.name, env: project.env },
                })
              }}
              className="col-span-1 flex cursor-pointer rounded-md shadow-sm hover:shadow-md transition-shadow"
            >
              <div
                className={classNames(
                  envColor[project.env] || envColor.default,
                  "flex w-32 flex-shrink-0 items-center justify-center rounded-l-md text-sm font-medium text-white cursor-pointer"
                )}
              >
                {project.env}
              </div>
              <div className="flex flex-1 items-center justify-between truncate rounded-r-md border-b border-r border-t border-gray-200 bg-white cursor-pointer">
                <div className="flex-1 truncate px-4 py-4 text-sm">
                  <p className="mb-1 truncate font-medium text-gray-900 hover:text-gray-600">
                    {project.name}
                  </p>
                </div>
                <div className="flex-shrink-0 pr-2">
                  <button
                    type="button"
                    className="inline-flex h-8 w-8 items-center justify-center rounded-full bg-transparent bg-white text-gray-400 hover:text-gray-500 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2"
                  >
                    <span className="sr-only">Open options</span>
                    <EllipsisVerticalIcon className="h-5 w-5" aria-hidden="true" />
                  </button>
                </div>
              </div>
            </li>
          ))}
        </ul>
        {data.length == 0 && loaded && (
          <main className="grid min-h-full place-items-center bg-white px-6 py-24 sm:py-32 lg:px-8">
            <div className="text-center">
              <p className="text-base font-semibold text-indigo-600">暂无配置</p>
              <h1 className="mt-4 text-2xl font-bold tracking-tight text-gray-700 sm:text-2xl">
                现在就为你的应用创建第一个配置文件吧！赶紧开始配置吧！
              </h1>
              {/* <p className="mt-6 text-base leading-7 text-gray-600">
                Sorry, we couldn’t find the page you’re looking for.
              </p> */}
              <div className="mt-10 flex items-center justify-center gap-x-6">
                <a
                  href="javascript:void(0)"
                  onClick={() => {
                    setOpen(true)
                  }}
                  className="rounded-md bg-indigo-600 px-3.5 py-2.5 text-sm font-semibold text-white shadow-sm hover:bg-indigo-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-indigo-600"
                >
                  立即创建
                </a>
                {/* <a href="#" className="text-sm font-semibold text-gray-900">
                  Contact support <span aria-hidden="true">&rarr;</span>
                </a> */}
              </div>
            </div>
          </main>
        )}
      </div>
    </Layout>
  )
}
