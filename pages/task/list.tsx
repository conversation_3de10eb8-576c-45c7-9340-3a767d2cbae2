import { Suspense, useEffect } from "react"
// import Image from "next/image"
// import Link from "next/link"
import Layout from "pages/layouts/Layout"
// import { useCurrentUser } from "app/users/hooks/useCurrentUser"
// import logout from "app/auth/mutations/logout"
// import logo from "public/logo.png"
// import { useMutation } from "@blitzjs/rpc"
// import { Grid, Stack, Container, Skeleton, SimpleGrid, Space } from "@mantine/core"
import { Routes, BlitzPage } from "@blitzjs/next"
import SectionHeadings from "app/components/SectionHeadings"
import TaskTable from "./components/task/table"
import { Router, useRouter } from "next/router"

const Task: BlitzPage = (props) => {
  const router = useRouter()
  const isEditPage = router.query.id as any

  useEffect(() => {
    const handleScroll = () => {
      console.log('scrollPosition');
    };

    // 添加滚动事件监听器
    window.addEventListener('scroll', handleScroll);

    // 清理函数：组件卸载时移除事件监听器
    return () => {
      window.removeEventListener('scroll', handleScroll);
    };
  }, []); // 空依赖数组表示这个effect只在组件挂载时运行一次

  return (
    <Layout title="task" {...props}>
      <SectionHeadings
        name="任务列表"
        btnText="创建任务"
        onClick={() => {
          void router.push({
            pathname: "/task/task_editor",
          })
        }}
      />
      {TaskTable && <TaskTable />}
    </Layout>
  )
}

export default Task
