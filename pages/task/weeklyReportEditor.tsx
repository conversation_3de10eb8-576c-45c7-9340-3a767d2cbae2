/**
 * @file weeklyReportEditor.tsx
 * @description 周报编辑页
 * @lastModified 2024-07-23
**/

import React, { useState, useEffect, useCallback, useMemo } from "react";
import { useRouter } from "next/router";
import { Text, Badge } from "@mantine/core";
import { Button, Input, Modal, Table, Form } from "antd";
import { showNotification } from "@mantine/notifications";
import { useMutation } from "@blitzjs/rpc";
import dayjs from "dayjs";
import { ReloadOutlined } from '@ant-design/icons';

import Layout from "pages/layouts/Layout";
import SectionHeadings from "app/components/SectionHeadings";
import updateReport from "app/task/mutations/updateReport";
import queryReportDetail from "app/task/mutations/queryReportDetail";
import getTasks from "app/task/mutations/query";
import { taskTypes, getGradient } from "../../constants/task_constants";

const { TextArea } = Input;

interface TaskItem {
  id: string;
  title: string;
  progressType: number;
  startTime?: string;
  releaseTime?: string;
  remark?: string;
  author?: {
    name: string;
  };
  fe?: string[];
  prd?: string;
}

interface FormValues {
  name: string;
  content?: string;
  weeklyTaskSnapshot?: TaskItem[];
}

const WeeklyReportEditor: React.FC = (props) => {
  const router = useRouter();
  const { id } = router.query;
  const [form] = Form.useForm<FormValues>();
  const [loaded, setLoaded] = useState(false);
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [tableData, setTableData] = useState<TaskItem[]>([]);
  const [selectedTableData, setSelectedTableData] = useState<TaskItem[]>([]);
  const [selectedRowKeys, setSelectedRowKeys] = useState<string[]>([]);
  const [isDisabled, setIsDisabled] = useState(false);
  const [initialFetchParams, setInitialFetchParams] = useState<any>(null);
  const [authorName, setAuthorName] = useState<string>("");

  const [getTasksMutation] = useMutation(getTasks);
  const [updateReportMutation] = useMutation(updateReport);
  const [queryReportDetailMutation] = useMutation(queryReportDetail);

  const fetchTableData = useCallback(async (params: any) => {
    const res = await getTasksMutation(params);
    if (res.entry) {
      const data = res.entry.map((item: any) => ({ ...item.data, id: item.id }));
      setTableData(data);
    }
  }, [getTasksMutation]);

  const fetchDetailData = useCallback(async () => {
    if (typeof id === 'string') {
      const res = await queryReportDetailMutation({ id }) as any;
      if (res.entry) {
        setLoaded(true);
        form.setFieldsValue({
          name: res.entry.title,
          content: res.entry.content || '',
          weeklyTaskSnapshot: res.entry.weeklyTaskSnapshot || [],
        });
        const selectedKeys = (res.entry.weeklyTaskSnapshot || []).map(item => item.id);
        setSelectedRowKeys(selectedKeys);
        setSelectedTableData(res.entry.weeklyTaskSnapshot || []);

        const endDate = dayjs(res.entry.title.split(' ~ ')[1]);
        const currentDate = dayjs();
        setIsDisabled(currentDate.isAfter(endDate.add(4, 'day')));

        const date = dayjs(res.entry.title.split(' ~ ')[0]).format('YYYY-MM-DD HH:mm:ss');
        const params = { fe: res.entry.author?.name, date, progressType: 99 };
        setInitialFetchParams(params);
        fetchTableData(params);

        // Set author name
        setAuthorName(res.entry.author?.name || "");
      }
    } else {
      setLoaded(true);
    }
  }, [id, queryReportDetailMutation, form, fetchTableData]);

  useEffect(() => {
    fetchDetailData();
  }, [fetchDetailData]);

  const handleFormSubmit = async (values: FormValues) => {
    if (!values.name.trim()) {
      showNotification({ title: "Error", message: "请输入模板标题", color: "red" });
      return;
    }

    const params = {
      ...values,
      id,
      weeklyTaskSnapshot: selectedTableData.map(item => ({
        id: item.id,
        title: item.title,
        startTime: item.startTime,
        releaseTime: item.releaseTime,
        progressType: item.progressType,
        remark: item.remark,
      })),
    };

    const res = await updateReportMutation(params);
    showNotification({
      title: res.status ? "成功" : "Error",
      message: res.status ? "保存成功" : "保存失败",
      color: res.status ? "green" : "red",
    });
  };

  const handleTableChange = useCallback((keys: string[]) => setSelectedRowKeys(keys), []);

  const confirmSelectedData = useCallback(() => {
    const newData = selectedRowKeys.map(key => tableData.find(item => item.id === key)).filter((item): item is TaskItem => item !== undefined);
    setSelectedTableData(newData);
    setIsModalVisible(false);
  }, [selectedRowKeys, tableData]);

  const removeTableRow = useCallback((record: TaskItem) => {
    Modal.confirm({
      title: '确认删除',
      content: '确定要删除这条记录吗？',
      onOk: () => {
        setSelectedTableData(prev => {
          const newData = prev.filter(item => item.id !== record.id);
          setSelectedRowKeys(newData.map(item => item.id));
          return newData;
        });
      },
    });
  }, []);

  const openTaskDetail = useCallback((id: string) => {
    window.open(`https://flow.xinc818.com/task/task_editor?id=${id}`, '_blank');
  }, []);

  const refreshTableData = useCallback(() => {
    if (initialFetchParams) {
      fetchTableData(initialFetchParams);
    }
  }, [fetchTableData, initialFetchParams]);

  const columns = useMemo(() => [
    {
      title: "需求名",
      width: "160px",
      dataIndex: "title",
      render: (_: any, record: TaskItem) => (
        <Text lineClamp={3} style={{ cursor: 'pointer', color: 'blue' }} onClick={() => openTaskDetail(record.id)}>
          {record.title}
        </Text>
      ),
    },
    {
      title: "进度",
      width: "80px",
      dataIndex: "progressType",
      render: (value: number) => (
        <Badge variant="gradient" gradient={getGradient(value)}>
          {taskTypes.progressType[value]}
        </Badge>
      ),
    },
    {
      title: "评审时间",
      width: "80px",
      dataIndex: "reviewTime",
      render: value => (value ? dayjs(value).format("MM月DD日") : "-"),
    },
    {
      title: "发布时间",
      dataIndex: "releaseTime",
      width: "80px",
      render: value => (value ? dayjs(value).format("MM月DD日") : "-"),
    },
    {
      title: "开始时间",
      dataIndex: "startTime",
      width: "80px",
      render: value => (value ? dayjs(value).format("MM月DD日") : "-"),
    },
    {
      title: "联调结束时间",
      dataIndex: "endTime",
      width: "80px",
      render: value => (value ? dayjs(value).format("MM月DD日") : "-"),
    },
    {
      title: "项目人员",
      width: "130px",
      dataIndex: "pd",
      render: (value, record) => (
        <>
          {record.fe && record.fe.length > 0 ? (
            <Text lineClamp={1}>前端: {record.fe.join("、")}</Text>
          ) : (
            ""
          )}
        </>
      ),
    },
    {
      title: "文档地址",
      width: "90px",
      dataIndex: "prd",
      render: (value, record) => (
        <Text lineClamp={3}>
          {value ? (
            value.indexOf("http") !== -1 ? (
              <a href={value} className="flow-link flow-shine-link" target={"_blank"} rel="noreferrer">
                {record.title}
              </a>
            ) : (
              value
            )
          ) : (
            "-"
          )}
        </Text>
      ),
    },
    // ... 其他列定义
  ], [openTaskDetail]);

  const columns2 = useMemo(() => [
    {
      title: "需求名",
      width: '240px',
      dataIndex: "title",
    },
    {
      title: "进度",
      width: "120px",
      dataIndex: "progressType",
      render: (value: number) => (
        <Badge variant="gradient" gradient={getGradient(value)}>
          {taskTypes.progressType[value]}
        </Badge>
      ),
    },
    {
      title: "开始时间",
      dataIndex: "startTime",
      width: "100px",
      render: (value: string) => (value ? dayjs(value).format("MM月DD日") : "-"),
    },
    {
      title: "发布时间",
      dataIndex: "releaseTime",
      width: "100px",
      render: (value: string) => (value ? dayjs(value).format("MM月DD日") : "-"),
    },
    {
      title: "工作内容",
      dataIndex: "remark",
      render: (text: string, record: TaskItem) => (
        <Input.TextArea
          defaultValue={text}
          onChange={(e) => {
            const newData = selectedTableData.map(item =>
              item.id === record.id ? { ...item, remark: e.target.value } : item
            );
            setSelectedTableData(newData);
          }}
        />
      ),
    },
    {
      title: "操作",
      dataIndex: "operation",
      render: (_: any, record: TaskItem) =>
        selectedTableData.length >= 1 ? (
          <Button disabled={isDisabled} onClick={() => removeTableRow(record)}>删除</Button>
        ) : null,
    },
  ], [selectedTableData, isDisabled, removeTableRow]);

  return (
    <div className="h-screen">
      <Layout title="编辑周报" {...props}>
        <SectionHeadings name={`编辑${authorName ? `-${authorName}` : ''}的周报`} />
        {loaded && (
          <div className="flex h-auto flex-row mt-10">
            <div className="w-full flex">
              <div className="w-full">
                <Form<FormValues>
                  form={form}
                  layout="vertical"
                  initialValues={{
                    name: "",
                    content: "",
                    weeklyTaskSnapshot: [],
                  }}
                  autoComplete="off"
                  onFinish={handleFormSubmit}
                >
                  <Form.Item label="周报标题" name="name" required rules={[{ required: false, message: "请填写完整!" }]}>
                    <Input placeholder="填入标题,如：2024.07.16 ~ 2024.07.20" disabled={true} />
                  </Form.Item>
                  <Form.Item label="业务支撑(自动匹配, 请仔细确认需求进度)" required name="weeklyTaskSnapshot" rules={[{ required: false, message: "请填写完整!" }]}>
                    <Button onClick={() => setIsModalVisible(true)} className="mb-4" disabled={isDisabled}>选择</Button>
                    <Table
                      columns={columns2}
                      dataSource={selectedTableData}
                      pagination={false}
                      rowKey="id"
                    />
                  </Form.Item>
                  <Form.Item label="个人思考和团队建设等" name="content" rules={[{ required: true, message: "请填写完整!" }]}>
                    <TextArea
                      autoSize={{ minRows: 8, maxRows: 30 }}
                      placeholder="这周有哪些新发现或进步？遇到了什么困难？有什么对团队建设的新想法吗？随便写，你的每一点分享都能帮助我们一起成长和进步。"
                    />
                  </Form.Item>
                  <Form.Item style={{ marginTop: '30px' }}>
                    <Button type="primary" htmlType="submit" disabled={isDisabled}>保存</Button>
                    <Button style={{ marginLeft: "20px" }} onClick={() => router.back()}>返回</Button>
                  </Form.Item>
                </Form>
              </div>
            </div>
          </div>
        )}
        <Modal
          title="选择项目"
          visible={isModalVisible}
          onCancel={() => setIsModalVisible(false)}
          width={'1200px'}
          footer={[
            <Button key="refresh" icon={<ReloadOutlined />} onClick={refreshTableData}>更新数据</Button>,
            <Button key="submit" type="primary" onClick={confirmSelectedData}>确定</Button>,
          ]}
          bodyStyle={{ maxHeight: '600px', overflowY: 'auto' }}
        >
          <Table
            rowSelection={{
              type: "checkbox",
              onChange: handleTableChange,
              selectedRowKeys: selectedRowKeys,
            }}
            columns={columns}
            dataSource={tableData}
            rowKey="id"
            pagination={{ pageSize: 20 }}
            scroll={{ y: 400 }}
          />
        </Modal>
      </Layout>
    </div>
  );
};

export default WeeklyReportEditor;
