import React, { useState, useEffect } from 'react';
import { Form, Input, Select, Switch, Space, Button, Checkbox, Modal, Typography, message, Table, Affix } from 'antd';
import { MinusCircleOutlined, PlusOutlined, ExclamationCircleOutlined, CloseCircleOutlined, CopyOutlined, UserSwitchOutlined } from '@ant-design/icons';
import Search from 'antd/lib/input/Search';

const { Option } = Select;
const { confirm } = Modal;

const EnhancedApiConfig = ({ form, renderOptionsBasedOnFE, renderOptionsBasedOnGroup, getAPIsDataMutation }) => {
  const [selectedApis, setSelectedApis] = useState<any>([]);
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [apiUrl, setApiUrl] = useState("");
  const [apiData, setApiData] = useState(null);
  const [selectAll, setSelectAll] = useState(false);
  const [isAffixed, setIsAffixed] = useState(false);

  // 使用 Form.useWatch 监听 hiddenAPIUrl 的变化
  const hiddenAPIUrl = Form.useWatch('hiddenAPIUrl', form);

  useEffect(() => {
    if (hiddenAPIUrl) {
      setApiUrl(hiddenAPIUrl);
    }
  }, [hiddenAPIUrl]);

  const showModal = () => {
    setIsModalVisible(true);
  };

  const handleOk = () => {
    const aoneList = form.getFieldValue('aoneList');
    const updatedList = aoneList.map((item, index) => {
      if (selectedApis.includes(index)) {
        return { ...item, user: form.getFieldValue('batchUser') };
      }
      return item;
    });
    form.setFieldsValue({ aoneList: updatedList });
    setIsModalVisible(false);
    setSelectedApis([]);
    setSelectAll(false);
  };

  const handleCancel = () => {
    setIsModalVisible(false);
    setSelectedApis([]);
    setSelectAll(false);
  };

  function getPageIdFromUrl(url) {
    try {
      const urlObj = new URL(url);
      const params = urlObj.searchParams;
      return params.get('pageId') || null;
    } catch (error) {
      console.error("Invalid URL:", error);
      return null;
    }
  }

  function displayMessagesWithFixedDelay(messagesList, delay) {
    const messages = messagesList.split(',');
    messages.forEach((msg, index) => {
      setTimeout(() => {
        message.success(msg.trim());
      }, delay * index);
    });
  }

  const onSearch = async (val) => {
    const pageId = getPageIdFromUrl(val)
    if (pageId) {
      form.setFieldsValue({ hiddenAPIUrl: val });
      setApiUrl(val);
      displayMessagesWithFixedDelay("🤖 智能分析+特征提取..., 🤖 NLP自然语言处理...,🚀 解析完成。", 500)
      const apiData = await getAPIsDataMutation({
        id: pageId
      });
      if (apiData.status) {
        setApiData(apiData.entry);
        showTableModal(apiData.entry);
      } else {
        message.error('未提取到数据')
      }
    } else {
      message.info('请输入正确的wiki地址')
    }
  }

  const showTableModal = (data) => {
    const columns = [
      {
        title: '接口名称',
        dataIndex: 'apiName',
        key: 'apiName',
      },
      {
        title: '接口路径',
        dataIndex: 'apiPath',
        key: 'apiPath',
      },
      {
        title: '分组',
        dataIndex: 'moduleName',
        key: 'moduleName',
      },
      {
        title: '对接人',
        dataIndex: 'contactPerson',
        key: 'contactPerson',
      }
    ];

    Modal.info({
      title: '接口信息',
      width: 900,
      closable: true,
      content: (
        <div style={{ maxHeight: 600, overflow: 'auto' }}>
          <Table
            columns={columns}
            dataSource={data}
            pagination={false}
            //@ts-ignore
            rowKey={(record, index) => index}
          />
        </div>
      ),
      onOk() {
        addMultipleFields(data);
      },
      okText: '插入数据',
    });
  };

  const addMultipleFields = (newFields) => {
    const fields = form.getFieldValue('aoneList') || [];
    const existingPaths = new Set(fields.map(field => field.apiPath));
    const uniqueNewFields = newFields.filter(field => !existingPaths.has(field.apiPath));
    const updatedFields = uniqueNewFields.map(field => ({
      ...field,
      apiGroup: field.moduleName
    }));
    const combinedFields = [...fields, ...updatedFields];
    const deduplicatedFields = combinedFields.reduce((acc, current) => {
      const x = acc.find(item => item.apiPath === current.apiPath);
      if (!x) {
        return acc.concat([current]);
      } else {
        return acc;
      }
    }, []);
    form.setFieldsValue({
      aoneList: deduplicatedFields
    });

    // 追加分组信息到 aoneSubGroup
    const existingGroups = form.getFieldValue('aoneSubGroup') || [];
    //@ts-ignore
    const newGroups = [...new Set(newFields.map(item => item.moduleName))];
    //@ts-ignore
    const uniqueGroups = [...new Set([...existingGroups, ...newGroups])];
    form.setFieldsValue({ aoneSubGroup: uniqueGroups });

    message.success('已去重后插入数据!');
  };

  const clearAPIData = () => {
    confirm({
      title: "温馨提示!",
      icon: <ExclamationCircleOutlined />,
      content: "多想想，多想想。 是否删除数据",
      onOk() {
        form.setFieldsValue({ aoneList: [], hiddenAPIUrl: '' });
        setApiUrl('');
        setApiData(null);
        setSelectedApis([]);
        setSelectAll(false);
        message.info('数据已清除');
      },
    });
  };

  const handleApiGroupChange = (value, option, name) => {
    const aoneList = form.getFieldValue('aoneList');
    aoneList[name].apiGroup = value;
    form.setFieldsValue({ aoneList });
  };

  const copyToClipboard = (text) => {
    navigator.clipboard.writeText(text).then(() => {
      message.success('链接已复制到剪贴板');
    }).catch(err => {
      console.error('Failed to copy: ', err);
      message.error('复制失败');
    });
  };

  const handleSelectAll = (e) => {
    setSelectAll(e.target.checked);
    if (e.target.checked) {
      const allIndices = form.getFieldValue('aoneList').map((_, index) => index);
      setSelectedApis(allIndices);
    } else {
      setSelectedApis([]);
    }
  };

  return (
    <div style={{ marginLeft: "0px", paddingTop: '1px' }} className="aoneList">
      <Affix offsetTop={10} onChange={(affixed: any) => setIsAffixed(affixed)}>
        <div style={{
          position: isAffixed ? 'fixed' : 'absolute',
          right: isAffixed ? '30px' : '0',
          top: isAffixed ? '50%' : 'auto',
          transform: isAffixed ? 'translateY(-50%)' : 'none',
          zIndex: 1000
        }}>
          {isAffixed ? (
            <Button
              type="primary"
              icon={<UserSwitchOutlined />}
              onClick={showModal}
              disabled={selectedApis.length === 0}
              style={{ width: '40px', height: '40px', padding: 0 }}
            />
          ) : (
            <Button
              type="primary"
              onClick={showModal}
              disabled={selectedApis.length === 0}
            >
              批量配置前端
            </Button>
          )}
        </div>
      </Affix>
      <div className='mb-10'></div>
      <Form.List name="aoneList">
        {(fields, { add, remove }) => (
          <>
            <div style={{ display: "flex", marginBottom: 8, paddingLeft: '0px' }}>
              <div style={{ width: "30px", marginRight: "10px" }}>
                <Checkbox
                  checked={selectAll}
                  onChange={handleSelectAll}
                />
              </div>
              <div style={{ width: "180px", marginRight: "10px" }}>接口名</div>
              <div style={{ width: "390px", marginRight: "10px" }}>接口路径</div>
              <div style={{ width: "190px", marginRight: "0px" }}>前端</div>
              <div style={{ width: "150px", marginRight: "0px" }}>自定义分组</div>
            </div>
            {fields.map(({ key, name, ...restField }) => (
              <Space key={key} style={{ display: "flex", marginBottom: 8 }} align="baseline">
                <Checkbox
                  checked={selectedApis.includes(name)}
                  onChange={(e) => {
                    if (e.target.checked) {
                      setSelectedApis([...selectedApis, name]);
                    } else {
                      setSelectedApis(selectedApis.filter(item => item !== name));
                      setSelectAll(false);
                    }
                  }}
                />
                <Form.Item
                  {...restField}
                  name={[name, "apiName"]}
                  rules={[{ required: false, message: "Missing" }]}
                >
                  <Input style={{ width: "180px" }} />
                </Form.Item>
                <Form.Item
                  {...restField}
                  name={[name, "apiPath"]}
                  rules={[{ required: false, message: "Missing" }]}
                >
                  <Input style={{ width: "390px" }} />
                </Form.Item>
                <Form.Item
                  {...restField}
                  name={[name, "user"]}
                  rules={[{ required: false, message: "Missing" }]}
                >
                  <Select mode="multiple" style={{ width: "190px" }} options={renderOptionsBasedOnFE()}></Select>
                </Form.Item>
                <Form.Item
                  {...restField}
                  name={[name, "apiGroup"]}
                  dependencies={['aoneSubGroup']}
                  rules={[{ required: false, message: "Missing" }]}
                >
                  <Select
                    style={{ width: "160px" }}
                    popupClassName="small-select"
                    options={renderOptionsBasedOnGroup()}
                    onChange={(value, option) => handleApiGroupChange(value, option, name)}
                    allowClear
                    clearIcon={<CloseCircleOutlined />}
                  />
                </Form.Item>
                <Form.Item
                  style={{ display: 'none' }}
                  {...restField}
                  name={[name, "isIntegrated"]}
                  valuePropName="checked"
                  rules={[{ required: false, message: "Missing" }]}
                >
                  <Switch />
                </Form.Item>
                <MinusCircleOutlined onClick={() => remove(name)} />
              </Space>
            ))}
            <Form.Item>
              <Space direction="vertical" style={{ width: '100%' }}>
                <Space direction="vertical" style={{ width: '100%' }}>
                  <Space>
                    <Button type="primary" onClick={() => add()} icon={<PlusOutlined />}>
                      添加接口信息
                    </Button>
                    <Search
                      placeholder="输入wiki地址 或用下面的示例链接试一试"
                      enterButton="智能解析"
                      value={apiUrl}
                      onChange={(e) => {
                        setApiUrl(e.target.value);
                        form.setFieldsValue({ hiddenAPIUrl: e.target.value });
                      }}
                      onSearch={onSearch}
                      style={{ width: 500 }}
                    />
                    <Button type="primary" danger onClick={clearAPIData}>
                      清空所有API
                    </Button>
                    {apiData && (
                      <Button type="primary" onClick={() => showTableModal(apiData)}>
                        查看解析结果
                      </Button>
                    )}
                  </Space>
                  <Space direction="vertical" style={{ marginTop: '16px', padding: '16px', backgroundColor: '#f9f9f9', borderRadius: '8px', width: '700px' }}>
                    <Typography.Title level={4}>接口文档示例链接：</Typography.Title>
                    <Space>
                      <Typography.Link href="https://wiki.xinc818.com/pages/viewpage.action?pageId=76366525" target="_blank">
                        【常规】接口名 、接口地址
                      </Typography.Link>
                      <Button icon={<CopyOutlined />} onClick={() => copyToClipboard("https://wiki.xinc818.com/pages/viewpage.action?pageId=76366525")} />
                    </Space>
                    <Space>
                      <Typography.Link href="https://wiki.xinc818.com/pages/viewpage.action?pageId=76366523" target="_blank">
                        【常规】接口名 、接口地址、对接人
                      </Typography.Link>
                      <Button icon={<CopyOutlined />} onClick={() => copyToClipboard("https://wiki.xinc818.com/pages/viewpage.action?pageId=76366523")} />
                    </Space>
                    <Space>
                      <Typography.Link href="https://wiki.xinc818.com/pages/viewpage.action?pageId=76366519" target="_blank">
                        【高级】接口名 、接口地址、对接人(支持合并单元格)、模块分组(支持合并单元格)
                      </Typography.Link>
                      <Button icon={<CopyOutlined />} onClick={() => copyToClipboard("https://wiki.xinc818.com/pages/viewpage.action?pageId=76366519")} />
                    </Space>
                    <Table
                      dataSource={[
                        { key: '1', rule: '接口名称', description: '接口名 或 接口描述' },
                        { key: '4', rule: '接口路径', description: '有包含(https://)的昆仑地址即可' },
                        { key: '2', rule: '分组', description: '模块' },
                        { key: '3', rule: '对接人', description: '对接人' },
                      ]}
                      pagination={false}
                      bordered
                      style={{ marginTop: '16px' }}
                    >
                      <Table.Column title="字段" dataIndex="rule" key="rule" />
                      <Table.Column title="提取规则(请修改为下方文字)" dataIndex="description" key="description" />
                    </Table>
                  </Space>
                </Space>
              </Space>
            </Form.Item>
          </>
        )}
      </Form.List>
      <Modal
        title="批量配置前端"
        visible={isModalVisible}
        onOk={handleOk}
        onCancel={handleCancel}
      >
        <Form.Item name="batchUser" label="选择前端">
          <Select mode="multiple" style={{ width: "100%" }} options={renderOptionsBasedOnFE()}></Select>
        </Form.Item>
      </Modal>
    </div>
  );
};

export default EnhancedApiConfig;
