import React, { useState, useEffect } from "react"
import { Table, Modal, Input, Button, Space, message, Form, DatePicker, Select, Tooltip, Popover, Spin } from "antd"
import { Text, Badge, Box, Group, Stack } from "@mantine/core"
import type { ColumnsType } from "antd/es/table"
import { useMutation } from "@blitzjs/rpc"
import { useClipboard } from "@mantine/hooks"
import { showNotification } from "@mantine/notifications"
import dayjs from "dayjs"
import weekdays from "dayjs/plugin/weekday"
import localeData from "dayjs/plugin/localeData"
import getTasks from "app/task/mutations/query"
import deleteTask from "app/task/mutations/delete"
import { useRouter } from "next/router"
import { getEmployeesByTeam } from "@/constants/employees"
import { invoke } from "@blitzjs/rpc"
import getToken from "app/users/queries/getToken"
import { EllipsisOutlined, FileTextOutlined, SearchOutlined, ReloadOutlined } from '@ant-design/icons'

dayjs.extend(weekdays)
dayjs.extend(localeData)

const { confirm } = Modal

const isExceed3Days = (createTime) => {
  const serverTime = dayjs(createTime)
  const now = dayjs()
  return now.diff(serverTime, 'day') >= 3
}

const App: React.FC = () => {
  const [form] = Form.useForm()
  const [data, setData] = useState([])
  const [loading, setLoading] = useState(false)
  const router = useRouter()
  const clipboard = useClipboard()
  const [getTasksMutation] = useMutation(getTasks)
  const [deleteTaskMutation] = useMutation(deleteTask)

  const getTableData = async () => {
    setLoading(true)
    try {
      const res = await getTasksMutation({
        date: dayjs().startOf('week'),
        progressType: 99
      })
      //@ts-ignore
      const arr = res.entry?.map((item) => ({ ...item.data, id: item.id })) || []
      //@ts-ignore
      setData(arr)
    } catch (error) {
      showNotification({
        title: "错误",
        message: "获取数据失败，请稍后重试",
        color: "red"
      })
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    void getTableData()
  }, [])

  const [token, setToken] = useState(null)

  useEffect(() => {
    const getTokenHandle = async () => {
      const token = await invoke(getToken, {}) as any
      setToken(token)
    }
    void getTokenHandle()
  }, [])

  const handleOpenInNewTab = (e, href) => {
    e.preventDefault()
    window.open(href, '_blank')
  }

  const task_enum = {
    type: { 1: "项目", 2: "日常" },
    category: { 1: "供应链中台", 2: "设计师平台", 3: "集团需求", 99: "其他" },
    classification: { 1: "业务驱动", 2: "技术驱动" },
    team: { "1": "后端开发部", "2": "质量保障组", "3": "前端组", "4": "UED", "5": "架构部" },
    progressType: { 1: "未开始", 2: "进行中", 3: "已上线", 4: "已暂停" },
    quality: { 1: "暂无", 2: "差", 3: "一般", 4: "较好", 5: "好" },
  }

  const columns: ColumnsType<any> = [
    {
      title: "序号",
      width: 60,
      render: (_, __, index) => index + 1,
    },
    {
      title: "需求名",
      dataIndex: "title",
      width: 180,
      render: (value, record) => (
        <Space align="start">
          <Text lineClamp={3}>{value}</Text>
        </Space>
      ),
    },
    {
      title: "需求信息",
      width: 220,
      render: (_, record) => (
        <Tooltip title={
          <>
            <div>类型: {task_enum.type[record.type]}</div>
            <div>涉及业务: {task_enum.category[record.category]}</div>
            <div>业务/技术: {task_enum.classification[record.classification]}</div>
          </>
        }>
          <Text>{task_enum.type[record.type]} / {task_enum.category[record.category]} / {task_enum.classification[record.classification]}</Text>
        </Tooltip>
      ),
    },
    {
      title: "评审时间",
      width: "100px",
      dataIndex: "reviewTime",
      render(value, record, index) {
        return value ? dayjs(value).format("YY/MM/DD") : "-"
      },
    },
    {
      title: "发布时间",
      dataIndex: "releaseTime",
      width: "100px",
      render(value, record, index) {
        return value ? dayjs(value).format("YY/MM/DD") : "-"
      },
    },
    {
      title: "开始时间",
      dataIndex: "startTime",
      width: "100px",
      render(value, record, index) {
        return value ? dayjs(value).format("YY/MM/DD") : "-"
      },
    },
    {
      title: "联调结束",
      dataIndex: "endTime",
      width: "100px",
      render(value, record, index) {
        return value ? dayjs(value).format("YY/MM/DD") : "-"
      },
    },
    {
      title: "进度",
      dataIndex: "progressType",
      width: 100,
      render: (value) => {
        const gradients = {
          1: { from: "gray", to: "gray" },
          2: { from: "yellow", to: "orange", deg: 105 },
          3: { from: "teal", to: "lime", deg: 105 },
          4: { from: "red", to: "red", deg: 105 },
        }
        return (
          <Badge variant="gradient" gradient={gradients[value]}>
            {task_enum.progressType[value]}
          </Badge>
        )
      },
    },
    {
      title: "项目人员",
      width: 180,
      render: (_, record) => (
        <Popover content={
          <Stack spacing="xs">
            {record.pd?.length > 0 && <Text size="sm">产品: {record.pd.join("、")}</Text>}
            {record.ued?.length > 0 && <Text size="sm">UED: {record.ued.join("、")}</Text>}
            {record.fe?.length > 0 && <Text size="sm">前端: {record.fe.join("、")}</Text>}
            {record.be?.length > 0 && <Text size="sm">后端: {record.be.join("、")}</Text>}
            {record.tester?.length > 0 && <Text size="sm">测试: {record.tester.join("、")}</Text>}
          </Stack>
        } trigger="hover">
          <Stack spacing="xs">
            {record.pd?.length > 0 && <Text size="sm">产品: {record.pd.join("、")}</Text>}
            {record.fe?.length > 0 && <Text size="sm">前端: {record.fe.join("、")}</Text>}
          </Stack>
        </Popover>
      ),
    },
    {
      title: "文档地址",
      dataIndex: "prd",
      width: 120,
      render: (value, record) => (
        <Text lineClamp={2}>
          {value ? (
            value.indexOf("http") !== -1 ? (
              <a href={value} className="flow-link flow-shine-link" target="_blank" rel="noreferrer">
                {record.title}
              </a>
            ) : (
              value
            )
          ) : (
            "-"
          )}
        </Text>
      ),
    },
    {
      title: "操作",
      fixed: "right",
      width: 120,
      render: (_, record) => (
        <Space size="small">
          <Button type="link" onClick={(e) => handleOpenInNewTab(e, `/task/task_editor?id=${record.id}`)}>
            编辑
          </Button>
          <Popover
            content={
              <Space direction="vertical">
                <Button
                  type="link"
                  onClick={() => {
                    if (isExceed3Days(record.createTime)) {
                      showNotification({
                        title: "温馨提示",
                        color: "red",
                        message: "创建时间超过3天，无法删除数据 请联系管理员",
                      })
                      return
                    }
                    confirm({
                      title: "温馨提示",
                      content: "是否删除数据",
                      onOk: async () => {
                        try {
                          const res = await deleteTaskMutation({ id: parseInt(record.id) })
                          if (res.status) {
                            showNotification({ title: "通知", message: "删除数据成功" })
                            form.submit()
                          } else {
                            throw new Error("删除失败")
                          }
                        } catch (error) {
                          showNotification({ title: "错误", message: "删除数据失败", color: "red" })
                        }
                      },
                    })
                  }}
                >
                  删除
                </Button>
              </Space>
            }
            trigger="click"
          >
            <Button type="link" icon={<EllipsisOutlined />} />
          </Popover>
        </Space>
      ),
    },
  ]

  const onFinish = async (values: any) => {
    setLoading(true)
    try {
      const result = await getTasksMutation(values)
      const arr = (result.entry?.map((item: any) => ({ ...item.data, id: item.id })) || []) as any;
      setData(arr)
      void message.success('查询成功')
    } catch (error) {
      showNotification({
        title: "错误",
        message: "查询失败，请稍后重试",
        color: "red"
      })
    } finally {
      setLoading(false)
    }
  }

  const onReset = async () => {
    form.resetFields()
    setLoading(true)
    try {
      const result = await getTasksMutation({})
      const arr = (result.entry?.map((item: any) => ({ ...item.data, id: item.id })) || []) as any
      setData(arr)
      void message.success('重置成功')
    } catch (error) {
      showNotification({
        title: "错误",
        message: "重置失败，请稍后重试",
        color: "red"
      })
    } finally {
      setLoading(false)
    }
  }

  const onlyThisWeek = () => {
    const thisWeekMonday = dayjs().startOf('week')
    form.setFieldsValue({ date: thisWeekMonday })
    form.submit()
  }

  return (
    <Box p="md">
      <Form
        form={form}
        name="control-hooks"
        onFinish={onFinish}
        layout="inline"
        initialValues={{
          fe: [],
          title: "",
          category: "",
          progressType: 99,
          classification: "",
          projectFilter: 2,
        }}
        style={{ marginBottom: '16px' }}
      >
        <Group align="flex-end" spacing="xs" style={{ width: '100%' }}>
          <Form.Item name="fe" label="选择前端">
            <Select
              style={{ width: 120 }}
              placeholder="选择"
              options={getEmployeesByTeam("3")}
              allowClear
            />
          </Form.Item>
          <Form.Item name="title" label="需求名">
            <Input style={{ width: 180 }} />
          </Form.Item>
          <Form.Item name="category" label="所属业务">
            <Select style={{ width: 120 }}>
              <Select.Option value={1}>供应链中台</Select.Option>
              <Select.Option value={2}>设计师平台</Select.Option>
              <Select.Option value={3}>集团需求</Select.Option>
              <Select.Option value={99}>其他</Select.Option>
            </Select>
          </Form.Item>
          <Form.Item name="date" label="日期筛选">
            <DatePicker />
          </Form.Item>
          <Button type="link" style={{ marginLeft: '-20px' }} onClick={onlyThisWeek}>只看本周</Button>
          <Form.Item name="classification" label="驱动类型">
            <Select style={{ width: 120 }}>
              <Select.Option value="">全部</Select.Option>
              <Select.Option value={1}>业务驱动</Select.Option>
              <Select.Option value={2}>技术驱动</Select.Option>
            </Select>
          </Form.Item>
          <Form.Item name="progressType" label="进度">
            <Select style={{ width: 120 }}>
              <Select.Option value={99}>正常进度</Select.Option>
              <Select.Option value={4}>暂停任务</Select.Option>
            </Select>
          </Form.Item>
          <Form.Item style={{ marginLeft: 'auto' }}>
            <Space>
              <Button type="primary" htmlType="submit" icon={<SearchOutlined />}>
                查询
              </Button>
              <Button onClick={onReset} icon={<ReloadOutlined />}>
                重置
              </Button>
            </Space>
          </Form.Item>
        </Group>
      </Form>
      <Spin spinning={loading}>
        <Table
          columns={columns}
          dataSource={data}
          rowKey="id"
          pagination={{ pageSize: 50, showSizeChanger: true, showQuickJumper: true }}
          scroll={{ x: 'max-content' }}
          style={{ backgroundColor: 'white', borderRadius: 8 }}
        />
      </Spin>
    </Box>
  )
}

export default App
