// import { Textarea } from "@mantine/core"
import {
  Button,
  DatePicker,
  Form,
  Input,
  InputNumber,
  Select,
  Tabs,
  Modal, Table,
  message,
} from "antd"
import dayjs from "dayjs"
import React, { useState, useEffect } from "react"
import submitTask from "app/task/mutations/submit"
import getTaskDetail from "app/task/mutations/detail"
import queryProject from "app/project/mutations/queryProject"
import { useRouter } from "next/router"
import { showNotification } from "@mantine/notifications"
import Divider from "app/components/Divider"
import { useMutation } from "@blitzjs/rpc"
import weekdays from "dayjs/plugin/weekday"
import localeData from "dayjs/plugin/localeData"
dayjs.extend(weekdays)
dayjs.extend(localeData)
const { Option } = Select
const { TextArea } = Input
const { Search } = Input;
const { confirm } = Modal
const { TabPane } = Tabs;
import { getEmployeesByTeam } from "@/constants/employees"
import EnhancedApiConfig from './EnhancedApiConfig';



type SizeType = Parameters<typeof Form>[0]["size"]

const handleOpenInNewTab = (e, href) => {
  e.preventDefault();
  window.open(href, '_blank');
};

function getPageIdFromUrl(url) {
  try {
    const urlObj = new URL(url);
    const params = urlObj.searchParams;
    return params.get('pageId') || null;  // 如果没有 pageId 参数，返回 null
  } catch (error) {
    console.error("Invalid URL:", error);
    return null;  // 如果 URL 格式不正确，也返回 null
  }
}

const App: React.FC = () => {
  const router = useRouter()
  const postId = router.query.id as any
  const from = router.query.from as any
  const [form] = Form.useForm()
  const [projects, setProjects] = useState([])
  const [detail, setDetail] = useState([]) as any;
  const [options, setOptions] = useState([]);
  const [messageApi, contextHolder] = message.useMessage();
  const [apiUrl, setApiUrl] = useState("");


  const [submitTaskMutation] = useMutation(submitTask, {})
  const [getTaskDetailMutation] = useMutation(getTaskDetail, {})
  const [queryProjectMutation] = useMutation(queryProject, {})

  const onFormLayoutChange = ({ size }: { size: SizeType }) => {
    // setComponentSize(size)
  }

  const onFinish = async (values) => {
    console.log('valuesvaluesvalues', values)

    if (values.progressType === 3) {  // 3 代表 "已上线"
      if (!values.releaseTime || !values.endTime) {
        showNotification({
          title: "notification",
          color: 'red',
          message: '进度设置为"已上线"时，必须填写发布时间和开发结束时间',
        })
        return;
      }
    }

    const params = {
      ...values,
    }
    const res = await submitTaskMutation({
      id: parseInt(postId) || "",
      data: params,
    }) as any;
    if (res && res.status) {
      showNotification({
        title: "notification",
        message: "同步数据成功! 🤥",
      })
      if (!router.query.id) {
        showNotification({
          title: "notification",
          message: "正在跳转! 🤥",
        })
        router.push({
          pathname: "/task/task_editor",
          query: {
            id: res.entry.id
          }
        })
      }
    } else {
      showNotification({
        title: "notification",
        color: 'red',
        message: "暂无权限! 🤥",
      })
    }

  }


  const renderOptionsBasedOnFE = () => {
    const feValues = form.getFieldValue('fe'); // 获取 "fe" 字段的值
    if (!feValues) {
      return null; // 如果没有值，则不显示选项
    }
    // 直接使用 feValues 作为选项
    return feValues.map((value: any, idx) => {
      return {
        label: value,
        value: value,
        key: idx,
      }
    })
  };

  const renderOptionsBasedOnGroup = () => {
    const feValues = form.getFieldValue('aoneSubGroup'); // 获取 "fe" 字段的值
    if (!feValues) {
      return null; // 如果没有值，则不显示选项
    }
    // 直接使用 feValues 作为选项
    return feValues.map((value: any, idx) => {
      return {
        label: value,
        value: value,
        key: idx,
      }
    })
  }

  const initData = async () => {
    const res = await queryProjectMutation({ pid: "all" })
    if (res) {
      let _projects = res.entry.map((item, idx) => {
        return {
          label: item.title + "(" + item.repoName + ")",
          value: item.id,
          key: item.id,
        }
      })
      console.log("_projects", _projects, res.entry)
      setProjects(_projects)
    }
    if (postId) {
      const result = await getTaskDetailMutation({
        id: parseInt(postId),
      })
      console.log("resultresultresult", result)
      if (result.status) {
        let data = result.entry as any
        setTimeout(() => {
          form.setFieldsValue({
            title: data.title,
            type: data.type,
            desc: data.desc,
            category: data.category,
            progressType: data.progressType,
            classification: data.classification,
            team: data.team,
            quality: data.quality,
            reviewTime: data.reviewTime ? dayjs(data.reviewTime) : null,
            releaseTime: data.releaseTime ? dayjs(data.releaseTime) : null,
            startTime: data.startTime ? dayjs(data.startTime) : null,
            endTime: data.endTime ? dayjs(data.endTime) : null,
            pd: data.pd,
            ued: data.ued,
            fe: data.fe,
            be: data.be,
            businessTeam: data.businessTeam,
            workingHours: data.workingHours,
            tester: data.tester,
            prd: data.prd,
            xmind: data.xmind,
            aoneZTLink: data.aoneZTLink,
            aoneDesignLink: data.aoneDesignLink,
            aonePRDLink: data.aonePRDLink,
            content: data.content,
            aoneList: data.aoneList,
            hiddenAPIUrl: data.hiddenAPIUrl,
            aoneSubGroup: data.aoneSubGroup,
            aoneCustomPage: data.aoneCustomPage,
            risk: data.risk,
            plan: data.plan,
            project: data.project,
          })
          // setOptions(data.fe);
          setDetail(data);
          setApiUrl(data.hiddenAPIUrl || "")
        }, 100)
      }
    }
  }

  Form.useWatch(['aoneSubGroup'], form);
  Form.useWatch(['fe'], form);

  useEffect(() => {
    void initData()
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [postId])

  const showTableModal = (data, fn) => {
    const addMultipleFields = () => {
      // 获取当前表单中的字段列表
      const fields = form.getFieldValue('aoneList') || [];
      // 准备要添加的新字段列表
      const newFields = data;

      // 创建一个包含所有当前 apiPath 的集合，用于快速查找
      const existingPaths = new Set(fields.map(field => field.apiPath));

      // 过滤出不包含在已有 apiPath 集合中的新字段
      const uniqueNewFields = newFields.filter(field => !existingPaths.has(field.apiPath));

      // 设置表单字段，将去重后的新字段列表添加到现有字段列表中
      form.setFields([
        {
          name: 'aoneList',
          value: [...fields, ...uniqueNewFields] // 合并现有字段和去重后的新字段
        }
      ]);
    };

    const columns = [
      {
        title: '接口名称',
        dataIndex: 'apiName',
        key: 'apiName',
      },
      {
        title: '接口路径',
        dataIndex: 'apiPath',
        key: 'apiPath',
      }
    ];

    Modal.info({
      title: '接口信息',
      width: 800,  // 设置一个适中的宽度
      content: (
        <div style={{ maxHeight: 600, overflow: 'auto' }}>
          <Table
            columns={columns}
            dataSource={data}
            pagination={false}  // 如果数据不多，可以不显示分页器
          />
        </div>
      ),
      closable: true,
      onCancel() { },
      onOk() {
        addMultipleFields()
        message.success('已去重后插入数据!')
      },  // 此处不执行任何操作
      okText: '插入数据',  // 修改确定按钮文本
      cancelText: '取消',  // 显示取消按钮
    });
  }

  function displayMessagesWithFixedDelay(messagesList, delay) {
    // 将字符串分割成数组
    const messages = messagesList.split(',');

    messages.forEach((msg, index) => {
      setTimeout(() => {
        message.success(msg.trim()); // 显示消息，并去除可能的首尾空格
      }, delay * index); // 每个消息延迟时间是固定的，乘以索引实现顺序延迟
    });
  }



  function checkProjectDisabled() {
    return false;
  }


  return (
    <Form
      form={form}
      labelCol={{ span: 4 }}
      wrapperCol={{ span: 10 }}
      layout="horizontal"
      className="mx-auto max-w-5xl"
      initialValues={{
        title: "",
        type: 1,
        workingHours: "",
        category: 1,
        progressType: 1,
        classification: 1,
        team: ["3"],
        quality: 1,
        reviewTime: "",
        releaseTime: "",
        startTime: "",
        endTime: "",
        pd: [],
        ued: [],
        fe: [],
        be: [],
        tester: [],
        prd: "",
        xmind: "",
        // aoneLink: "",
        aoneZTLink: "",
        aoneDesignLink: "",
        aonePRDLink: "",
        aoneList: [],
        hiddenAPIUrl: "",
        aoneSubGroup: [],
        aoneCustomPage: []
      }}
      autoComplete="off"
      onFinish={onFinish}
      onValuesChange={onFormLayoutChange}
    >
      <Tabs defaultActiveKey="1" type="card" className="mt-4">
        <TabPane tab="项目信息" key="1" className="pt-4" forceRender={true}>
          {/* <Divider name="基本信息" classNames="mt-5 mb-10" /> */}
          <Form.Item label="需求名称" name="title" rules={[{ required: true, message: "请填写完整!" }]}>
            <Input placeholder="填入需求名称，尽量和prd保持一致" />
          </Form.Item>
          <Form.Item label="项目目标" name="desc">
            <TextArea placeholder="填入项目背景/目标" autoSize={{ minRows: 3, maxRows: 10 }} />
          </Form.Item>
          <Form.Item label="prd地址" name="prd">
            <TextArea placeholder="填入PRD地址" autoSize={{ minRows: 2, maxRows: 5 }} />
          </Form.Item>
          <Form.Item label="xmind地址" name="xmind">
            <TextArea placeholder="填入XMIND地址" autoSize={{ minRows: 2, maxRows: 5 }} />
          </Form.Item>
          <Form.Item label="需求类型(项目/日常)" name="type" required>
            <Select>
              <Select.Option value={1}>项目</Select.Option>
              <Select.Option value={2}>日常</Select.Option>
            </Select>
          </Form.Item>
          <Form.Item label="项目归属(选填)" name="category" required>
            <Select>
              <Select.Option value={1}>供应链中台</Select.Option>
              <Select.Option value={2}>设计师平台</Select.Option>
              <Select.Option value={3}>集团需求支撑</Select.Option>
              <Select.Option value={99}>其他</Select.Option>
            </Select>
          </Form.Item>
          <Form.Item label="涉及业务方" name="businessTeam">
            <Select mode="multiple">
              <Option value="zzzx">招商中心</Option>
              <Option value="spzx">商品中心</Option>
              <Option value="yyzx">运营中心</Option>
              <Option value="hjzx">核价中心</Option>
              <Option value="cwxx">财务中心</Option>
              <Option value="zhpk">综合品控中心</Option>
              <Option value="sppk">食品品控中心</Option>
              <Option value="yp">样品管理部</Option>
              <Option value="cg">集团采购部</Option>
              <Option value="hgjc">合规监察中心</Option>
              <Option value="nkns">内控内审部</Option>
              <Option value="xy">辛语</Option>
              <Option value="mks">海南麦克斯</Option>
              <Option value="zcb">集团总裁办</Option>
              <Option value="gylzt">供应链中台</Option>
              <Option value="design">设计师平台</Option>
              <Option value="other">其他</Option>
            </Select>
          </Form.Item>
          <Form.Item label="业务驱动/技术驱动" name="classification" required>
            <Select>
              <Select.Option value={1}>业务驱动</Select.Option>
              <Select.Option value={2}>技术驱动</Select.Option>
            </Select>
          </Form.Item>
          {/* <Form.Item label="涉及业务方" name="bizName" required>
        <Select mode="multiple">
          <Option value="1">用户体验中心</Option>
          <Option value="green">品控部门</Option>
          <Option value="blue">运营中心</Option>
        </Select>
      </Form.Item> */}
          <Form.Item label="涉及技术团队" name="team" required>
            <Select mode="multiple">
              <Option value="1">后端开发部</Option>
              <Option value="2">质量保障组</Option>
              <Option value="3">前端组</Option>
              <Option value="4">UED</Option>
              <Option value="5">架构部</Option>
            </Select>
          </Form.Item>
          <Form.Item label="评审时间" name="reviewTime">
            <DatePicker />
          </Form.Item>
          <Form.Item label="发布时间" name="releaseTime">
            <DatePicker />
          </Form.Item>
          <Form.Item label="开发开始时间" name="startTime">
            <DatePicker />
          </Form.Item>
          <Form.Item label="开发结束时间(包含联调)" name="endTime">
            <DatePicker />
          </Form.Item>
          <Form.Item label="工时(包含联调, 单位:天)" name="workingHours" wrapperCol={{ span: 2 }}>
            <InputNumber min={0} max={30} step={0.5} />
          </Form.Item>
          <Form.Item label="进度" name={"progressType"} required>
            <Select>
              <Select.Option value={1}>未开始</Select.Option>
              <Select.Option value={2}>进行中</Select.Option>
              <Select.Option value={3}>已上线</Select.Option>
              <Select.Option value={4}>已暂停</Select.Option>
            </Select>
          </Form.Item>
          <Form.Item label="提测质量" name="quality" required>
            <Select>
              <Select.Option value={1}>暂无</Select.Option>
              <Select.Option value={2}>差</Select.Option>
              <Select.Option value={3}>一般</Select.Option>
              <Select.Option value={4}>较好</Select.Option>
              <Select.Option value={5}>好</Select.Option>
            </Select>
          </Form.Item>
          <Form.Item label="关联项目" name="project" rules={[{ required: true, message: "请选择对应仓库, 若不存在联系TL创建!" }]}>
            <Select mode="multiple" showSearch optionFilterProp="label" options={projects}></Select>
          </Form.Item>
          <Divider name="项目人员" />
          <Form.Item label="产品经理" name="pd" rules={[{ required: true, message: "请填写完整!" }]}>
            <Select mode="tags" placeholder='支持自定义输入' options={getEmployeesByTeam("1")}></Select>
          </Form.Item>
          <Form.Item label="设计师(视觉/交互)" name="ued">
            <Select mode="multiple" options={getEmployeesByTeam("2")}></Select>
          </Form.Item>
          <Form.Item label="前端开发" name="fe" rules={[{ required: true, message: "请填写完整!" }]}>
            <Select mode="multiple" options={getEmployeesByTeam("3")}></Select>
          </Form.Item>
          <Form.Item label="后端开发" name="be">
            <Select
              mode="tags"
              options={getEmployeesByTeam("5").concat(getEmployeesByTeam("6"))}
            ></Select>
          </Form.Item>
          <Form.Item label="测试" name="tester">
            <Select mode="tags" options={getEmployeesByTeam("4")}></Select>
          </Form.Item>
          <Divider name="项目概况" />
          <Form.Item label="具体情况" name="content">
            <TextArea autoSize={{ minRows: 2, maxRows: 5 }} />
          </Form.Item>
          <Form.Item label="风险和问题" name="risk">
            <TextArea autoSize={{ minRows: 2, maxRows: 5 }} />
          </Form.Item>
          <Form.Item label="下周计划" name="plan">
            <TextArea autoSize={{ minRows: 2, maxRows: 5 }} />
          </Form.Item>
        </TabPane>
      </Tabs>
      {/* <Divider show={false} className="mb-10" /> */}
      <Form.Item wrapperCol={{ offset: 8, span: 16 }}>
        <Button type="primary" htmlType="submit" className="mr-4" disabled={checkProjectDisabled()}>
          保存
        </Button>
        <Button onClick={(e) => {
          handleOpenInNewTab(e, `/task/aone?id=${postId}`)
        }}>
          访问文档站点
        </Button>
      </Form.Item>

    </Form >
  )
}

export default App
