// import { Suspense, useEffect } from "react"
// import Image from "next/image"
// import Link from "next/link"
import React, { useState, useEffect } from "react"
import Layout from "pages/layouts/Layout"
import { useMutation, useQuery } from "@blitzjs/rpc"
import type { BadgeProps } from "antd"
import { Badge, Calendar, Alert, Select, Tag, Table, Button, Modal, message } from "antd"
import getTasks from "app/task/mutations/query"
import getAuth from "@/app/users/mutations/getAuthRoles"
import { getEmployeesByTeam } from "@/constants/employees"
import Bardge from "app/components/Bardge"
import dayjs from "dayjs"
import SectionHeadings from "app/components/SectionHeadings"
import { Router, useRouter } from "next/router"
import type { Dayjs } from "dayjs"
import isBetween from "dayjs/plugin/isBetween"
import weekday from "dayjs/plugin/weekday"
import localeData from "dayjs/plugin/localeData"
import "dayjs/locale/zh-cn"
// import zhCN from 'antd/lib/locale/zh_CN';


dayjs.extend(weekday)
dayjs.extend(isBetween)
dayjs.extend(localeData)
dayjs.locale("zh-cn")

import { Routes, BlitzPage } from "@blitzjs/next"
import TaskTable from "./components/task/table"

const Task: BlitzPage = (props) => {
  const getLocalStorage = () => {
    let fe = localStorage.getItem("flow_task_fe")
    if (!fe || fe == "undefined") {
      return ""
    } else {
      return fe
    }
  }
  const router = useRouter()
  const [key, setKey] = useState(1)
  const [auth, setAuth] = useState(false)
  const [selectData, setSelectData] = useState([]) as any
  const [fe, setFe] = useState(process.browser ? getLocalStorage() : "")
  const [value, setValue] = useState(() => dayjs(new Date()))
  const [selectedValue, setSelectedValue] = useState(() => dayjs(new Date()))

  const [data, setData] = useState([]) as any
  const [getTasksMutation] = useMutation(getTasks, {})
  const [getAuthMutation] = useMutation(getAuth, {})
  const [isModalVisible, setIsModalVisible] = useState(false)
  const [selectedDayTasks, setSelectedDayTasks] = useState([])

  const getMyAuth = async () => {
    const res = await getAuthMutation()
    if (res && res.role.indexOf("FE_MASTER") !== -1) {
      setAuth(true)
      setSelectData([{ label: "全部", value: "" }].concat(getEmployeesByTeam("3")))
    } else {
      setAuth(false)
      if (res?.name) {
        setFe(res.name)
        setSelectData([
          {
            label: res?.name,
            value: res?.name,
          },
        ])
      }
    }
  }
  const getTableData = async () => {
    const res = await getTasksMutation({ fe: fe })
    let arr = [] as any
    if (res.entry) {
      arr = res.entry.map((item: any) => {
        item.data.id = item.id
        return item.data
      })
    }
    setData(arr || [])

    // 检查今天是否有项目发布
    const today = dayjs()
    const releasesToday = arr.filter((item: any) =>
      dayjs(item.releaseTime).isSame(today, 'day')
    )

    if (releasesToday.length > 0) {
      message.info(`温馨提示，今天有 ${releasesToday.length} 个项目发布，请重点关注`, 5)
    }
  }

  useEffect(() => {
    void getMyAuth()
    setTimeout(() => {
      void getTableData()
    }, 200)
    if (auth) {
      setTimeout(() => {
        setKey(key + 1)
      }, 300)
    }

    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [fe])

  const getListData = (value: Dayjs) => {
    let listData = [] as any

    data.forEach((item, idx) => {
      let _reviewTime = dayjs(item.reviewTime)
      let _startTime = dayjs(item.startTime)
      let _endTime = dayjs(item.endTime)
      let _releaseTime = dayjs(item.releaseTime)
      const isInBetween = value.isBetween(_startTime, _endTime, "day", "[]") //进行中
      const isInBetween2 = value.isBetween(_endTime, _releaseTime, "day", "()") //提测

      // 如果是星期日(0)或星期一(1)，则跳过
      if (value.day() === 0 || value.day() === 1) {
        return;
      }

      // 评审时间
      if (_reviewTime && dayjs(value).format("YYYY-MM-DD") == _reviewTime.format("YYYY-MM-DD")) {
        listData.push({
          type: 0,
          id: item.id,
          content: item.title,
          details: item
        })
      }

      // 如果设置了发布时间，才显示数据
      if (_releaseTime) {
        // 提测时间
        if (isInBetween2 && item.endTime && item.releaseTime) {
          listData.push({
            id: item.id,
            type: 2,
            content: item.title,
            details: item
          })
        } else if (item.startTime && item.endTime && isInBetween) {
          // 开发时间
          listData.push({
            id: item.id,
            type: 1,
            content: item.title,
            details: item
          })
        }
      }
      // 发布时间
      if (_releaseTime && dayjs(value).format("YYYY-MM-DD") == _releaseTime.format("YYYY-MM-DD")) {
        listData.push({
          type: 3,
          id: item.id,
          content: item.title,
          details: item
        })
      }
    })

    return listData || []
  }

  const handleOpenUrl = (item?) => {
    void router.push({
      pathname: "/task/task_editor",
      query: { id: item.id },
    })
  }

  const showModal = (date: Dayjs) => {
    const tasksForDay = getListData(date)
    setSelectedDayTasks(tasksForDay)
    setIsModalVisible(true)
  }

  const handleOk = () => {
    setIsModalVisible(false)
  }

  const handleCancel = () => {
    setIsModalVisible(false)
  }

  const dateCellRender = (value: Dayjs) => {
    const listData = getListData(value)
    return (
      <div
        className="events"
        style={{ maxHeight: '100%', overflowY: 'auto', overflowX: 'hidden' }}
        onClick={() => showModal(value)}
      >
        {listData.map((item: any, idx) => {
          if (item.type == 0) {
            return (
              <Bardge
                key={idx}
                name={item.content}
                color="green"
              />
            )
          } else if (item.type == 1) {
            return (
              <Bardge
                key={idx}
                name={item.content}
                color="blue"
              />
            )
          } else if (item.type == 2) {
            return (
              <Bardge
                key={idx}
                name={item.content}
                color="yellow"
              />
            )
          } else if (item.type == 3) {
            return (
              <Bardge
                key={idx}
                name={item.content}
                color="red"
              />
            )
          }
          return null
        })}
      </div>
    )
  }

  const onSelect = (newValue: Dayjs) => {
    setValue(newValue)
    setSelectedValue(newValue)
    showModal(newValue)
  }

  const onPanelChange = (newValue: Dayjs) => {
    setValue(newValue)
  }

  if (!data) {
    return (
      <Layout title="task" {...props}>
        {" "}
      </Layout>
    )
  }

  const columns = [
    {
      title: '标题',
      dataIndex: 'content',
      key: 'content',
    },
    {
      title: '类型',
      dataIndex: 'type',
      key: 'type',
      render: (type) => {
        const typeMap = {
          0: { text: '评审', color: 'green' },
          1: { text: '开发中', color: 'blue' },
          2: { text: '测试中', color: 'yellow' },
          3: { text: '上线', color: 'red' }
        }
        return (
          <Tag color={typeMap[type].color}>
            {typeMap[type].text}
          </Tag>
        )
      }
    },
    {
      title: '开始时间',
      dataIndex: ['details', 'startTime'],
      key: 'startTime',
      render: (time) => dayjs(time).format('YYYY年MM月DD日')
    },
    {
      title: '结束时间',
      dataIndex: ['details', 'endTime'],
      key: 'endTime',
      render: (time) => dayjs(time).format('YYYY年MM月DD日')
    },
    {
      title: '评审时间',
      dataIndex: ['details', 'reviewTime'],
      key: 'reviewTime',
      render: (time) => dayjs(time).format('YYYY年MM月DD日')
    },
    {
      title: '发布时间',
      dataIndex: ['details', 'releaseTime'],
      key: 'releaseTime',
      render: (time) => dayjs(time).format('YYYY年MM月DD日')
    },
    {
      title: '操作',
      key: 'action',
      render: (_, record) => (
        <Button onClick={() => handleOpenUrl(record)}>查看详情</Button>
      ),
    },
  ]

  return (
    <Layout title="task" {...props}>
      <SectionHeadings name="任务看板" />
      <div className="mb-5 flex">
        {auth && (
          <Select
            style={{ width: "120px" }}
            placeholder="选择"
            value={fe}
            onChange={(value) => {
              setFe(value)
              process.browser && localStorage.setItem("flow_task_fe", value)
            }}
            options={selectData}
            allowClear
          ></Select>
        )}
      </div>
      <p>
        <span className="mr-2 inline-flex items-center rounded-full bg-green-100 px-2.5 py-0.5 text-xs font-medium text-green-800">
          <svg
            className="-ml-0.5 mr-1.5 h-2 w-2 text-green-800"
            fill="currentColor"
            viewBox="0 0 8 8"
          >
            <circle cx={4} cy={4} r={3} />
          </svg>
          评审日
        </span>
        <span className="mr-2 inline-flex items-center rounded-full bg-blue-100 px-2.5 py-0.5 text-xs font-medium text-blue-800">
          <svg
            className="-ml-0.5 mr-1.5 h-2 w-2 text-blue-800"
            fill="currentColor"
            viewBox="0 0 8 8"
          >
            <circle cx={4} cy={4} r={3} />
          </svg>
          开发中
        </span>
        <span className="mr-2 inline-flex items-center rounded-full bg-yellow-100 px-2.5 py-0.5 text-xs font-medium text-yellow-800">
          <svg
            className="-ml-0.5 mr-1.5 h-2 w-2 text-yellow-800"
            fill="currentColor"
            viewBox="0 0 8 8"
          >
            <circle cx={4} cy={4} r={3} />
          </svg>
          测试中
        </span>
        <span className="mr-2 inline-flex items-center rounded-full bg-red-100 px-2.5 py-0.5 text-xs font-medium text-red-800">
          <svg
            className="-ml-0.5 mr-1.5 h-2 w-2 text-red-800"
            fill="currentColor"
            viewBox="0 0 8 8"
          >
            <circle cx={4} cy={4} r={3} />
          </svg>
          上线日
        </span>
      </p>
      <Calendar
        key={key}
        value={value}
        onSelect={onSelect}
        dateCellRender={dateCellRender}
        onPanelChange={onPanelChange}
      />
      <Modal
        title={`${selectedValue.format('YYYY-MM-DD')} 任务详情`}
        visible={isModalVisible}
        onOk={handleOk}
        onCancel={handleCancel}
        width={1000}
        footer={[
          <Button key="close" onClick={handleCancel}>
            关闭
          </Button>
        ]}
      >
        <Table
          dataSource={selectedDayTasks}
          columns={columns}
          rowKey="id"
        />
      </Modal>
    </Layout>
  )
}

export default Task