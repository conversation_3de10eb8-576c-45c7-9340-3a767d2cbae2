import { Suspense, useEffect } from "react"
import Image from "next/image"
import Link from "next/link"
import Layout from "pages/layouts/Layout"
import { useCurrentUser } from "app/users/hooks/useCurrentUser"
import logout from "app/auth/mutations/logout"
import logo from "public/logo.png"
import { useMutation } from "@blitzjs/rpc"
import { Grid, Stack, Container, Skeleton, SimpleGrid, Space } from "@mantine/core"
import { Routes, BlitzPage } from "@blitzjs/next"
import TaskForm from "./components/task/form"
import Breadcrumbs from "app/components/Breadcrumbs"
import { Router, useRouter } from "next/router"

const Home: BlitzPage = (props) => {
  const router = useRouter()
  const isEditPage = router.query.id as any

  return (
    <Layout title="task" {...props}>
      <Breadcrumbs
        pages={[
          { name: "任务列表", href: "/task/list", current: false },
          { name: isEditPage ? "编辑任务" : "新建任务", current: true },
        ]}
      />
      <div className="mb-10 mt-0 hidden border-gray-200 bg-white px-4 py-5 dark:bg-gray-800 sm:px-6">
        <h3 className="text-lg font-medium leading-6 text-gray-900 dark:text-gray-100">
          {isEditPage ? "编辑任务" : "新建任务"}
        </h3>
        {/* <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
          你可以随心所欲地创建各种大小的项目和日常任务，还能轻松管理它们。无论你付出了多少努力，所有的记录都会被精心保存在这里，为你的工作保驾护航。
        </p> */}
      </div>
      <TaskForm />
    </Layout>
  )
}

export default Home
