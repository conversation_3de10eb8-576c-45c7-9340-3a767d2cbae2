/**
 * @file weeklyReport.tsx
 * @description 周报编辑页
 * @lastModified 2024-07-23
**/

import React, { useEffect, useState, useCallback, useMemo } from "react";
import { Table, Modal, Input, Button, Card, Statistic, Row, Col, Tag, Typography, Avatar } from "antd";
import { useRouter } from "next/router";
import { PlusOutlined, EditOutlined, CheckCircleOutlined, ClockCircleOutlined } from '@ant-design/icons';
import { useMutation } from "@blitzjs/rpc";
import { showNotification } from "@mantine/notifications";

import Layout from "pages/layouts/Layout";
import SectionHeadings from "app/components/SectionHeadings";
import queryReport from "app/task/mutations/queryReport";
import createReports from "app/task/mutations/createReports";

const { Title, Text } = Typography;

const TeamWeeklyReport = (props) => {
  const [data, setData] = useState({});
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [newReportTitle, setNewReportTitle] = useState("");
  const [expandedRowKeys, setExpandedRowKeys] = useState<any>([]);

  const router = useRouter();
  const [queryReportMutation] = useMutation(queryReport);
  const [createReportsMutation] = useMutation(createReports);

  const fetchTableData = useCallback(async () => {
    const res = await queryReportMutation({});
    if (res.entry) {
      setData(res.entry);
      const latestWeek = Object.keys(res.entry)[0];
      setExpandedRowKeys([latestWeek]);
    }
  }, [queryReportMutation]);

  useEffect(() => {
    fetchTableData();
  }, [fetchTableData]);

  const handleEdit = useCallback((id) => {
    router.push({
      pathname: "/task/weeklyReportEditor",
      query: { id },
    });
  }, [router]);

  const columnsConfig = useMemo(() => [
    {
      title: "成员",
      width: '180px',
      dataIndex: ["author", "name"],
      render: (_, record) => (
        <div style={{ display: 'flex', alignItems: 'center', transition: 'transform 0.3s ease-in-out' }} className="hover:scale-105">
          <Avatar src={record.author.avatar || 'https://s.xinc818.com/files/webcim1brdmp7vut38i/ddd111.jpg'} style={{ marginRight: 8 }} className="hover:rotate-12 transition-transform duration-300" />
          <Text strong className="hover:text-blue-500 transition-colors duration-300">{record.author.name}</Text>
        </div>
      ),
    },
    {
      title: "周报标题",
      dataIndex: "title",
      render: (title) => <Text ellipsis={{ tooltip: title }}>{title}</Text>,
    },
    {
      title: "进度",
      dataIndex: "isCompleted",
      width: '120px',
      render: (isCompleted) => (
        isCompleted ?
          <Tag icon={<CheckCircleOutlined />} color="success">完成</Tag> :
          <Tag icon={<ClockCircleOutlined />} color="warning">待填写</Tag>
      ),
    },
    {
      title: "操作",
      width: '100px',
      render: (_, record) => (
        <Button type="link" icon={<EditOutlined />} onClick={() => handleEdit(record.id)}>
          编辑
        </Button>
      ),
    },
  ], [handleEdit]);

  const handleCreate = () => setIsModalVisible(true);

  const handleOk = async () => {
    setIsModalVisible(false);
    const res = await createReportsMutation({ title: newReportTitle });
    if (!res.status) {
      showNotification({
        title: "Error",
        color: 'red',
        message: "权限不足，由于工作日的灵活动态安排，由TL统一创建，以确保流程的高效和无缝衔接。"
      });
    } else {
      fetchTableData();
    }
    setNewReportTitle("");
  };

  const expandedRowRender = useCallback((record) => {
    const reports = data[record.title] || [];
    const completedCount = reports.filter(r => r.isCompleted).length;
    const totalCount = reports.length;

    return (
      <Card>
        <Row gutter={16} style={{ marginBottom: 16 }}>
          <Col span={8}>
            <Statistic title="完成率" value={`${(completedCount / totalCount * 100).toFixed(2)}%`} />
          </Col>
          <Col span={8}>
            <Statistic title="已完成" value={completedCount} suffix={`/ ${totalCount}`} />
          </Col>
          <Col span={8}>
            <Statistic title="待填写" value={totalCount - completedCount} />
          </Col>
        </Row>
        <Table
          columns={columnsConfig}
          dataSource={reports}
          pagination={false}
          rowKey="id"
        />
      </Card>
    );
  }, [data, columnsConfig]);

  return (
    <Layout title="工作周报" {...props}>
      <SectionHeadings
        name="工作周报"
        btnText={(<><PlusOutlined /> 批量创建</>)}
        onClick={handleCreate}
      />
      <Table
        columns={[{
          title: "周报标题",
          dataIndex: "title",
          render: (title) => <Title level={4}>{title}</Title>
        }]}
        expandable={{
          expandedRowRender,
          expandedRowKeys,
          onExpand: (expanded, record) => {
            setExpandedRowKeys(expanded ? [record.title] : []);
          },
        }}
        dataSource={Object.keys(data).map(title => ({ title }))}
        pagination={{ pageSize: 50 }}
        rowKey="title"
      />
      <Modal
        title="新建周报"
        visible={isModalVisible}
        onOk={handleOk}
        onCancel={() => setIsModalVisible(false)}
      >
        <Input
          placeholder="请输入周报标题"
          value={newReportTitle}
          onChange={(e) => setNewReportTitle(e.target.value)}
        />
      </Modal>
    </Layout>
  );
};

export default TeamWeeklyReport;
