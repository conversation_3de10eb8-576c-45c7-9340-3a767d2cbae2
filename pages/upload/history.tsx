/* eslint-disable react/no-unknown-property */
import Layout from "pages/layouts/Layout"
import { Image, Button, Table, Modal, Space, Typography, Tag, Statistic } from "antd"
import { Suspense, useState, useMemo } from "react"
import { useCurrentAssets } from "app/users/hooks/useCurrentAssets"
import { BlitzPage } from "@blitzjs/next"
import { CopyButton } from "@mantine/core"
import SectionHeadings from "app/components/SectionHeadings"
import dayjs from "dayjs"
import { FileImageOutlined, LinkOutlined, CalendarOutlined, CompressOutlined, PictureOutlined, FileOutlined } from '@ant-design/icons'

const { Text } = Typography;

const isImageUrl = (url: string): boolean => {
  const imageExtensions = [".jpg", ".jpeg", ".png", ".gif", ".webp", ".avif"]
  const fileExtension = url.substring(url.lastIndexOf(".")).toLowerCase()
  return imageExtensions.includes(fileExtension)
}

const UploadStatistics = ({ uploadedFiles }) => {
  const statistics = useMemo(() => {
    if (!uploadedFiles) return null;
    const total = uploadedFiles.length;
    const compressed = uploadedFiles.filter(file => file.url.indexOf("tinty/") !== -1).length;
    const images = uploadedFiles.filter(file => isImageUrl(file.url)).length;
    return { total, compressed, images };
  }, [uploadedFiles]);

  if (!statistics) return null;

  return (
    <Space size="large" style={{ marginBottom: 16 }}>
      <Statistic title="总文件数" value={statistics.total} />
      <Statistic title="图片数量" value={statistics.images} />
      <Statistic title="已压缩文件" value={statistics.compressed} />
    </Space>
  );
};

const UploadHistory = () => {
  const uploadedFiles = useCurrentAssets()
  const [imagePreview, setImagePreview] = useState<{ visible: boolean; url: string }>({ visible: false, url: '' })

  const handlePreview = (url: string) => {
    setImagePreview({ visible: true, url })
  }

  const handleClosePreview = () => {
    setImagePreview({ visible: false, url: '' })
  }

  const tableColumns = [
    {
      title: "图片预览",
      width: "120px",
      dataIndex: "url",
      render: (url: string) => {
        if (!isImageUrl(url)) {
          return <FileImageOutlined style={{ fontSize: '24px', color: '#1890ff' }} />
        }
        return (
          <Image
            src={url}
            className="image-preview"
            preview={false}
            onClick={() => handlePreview(url)}
          />
        )
      },
    },
    {
      title: "URL地址",
      dataIndex: "url",
      render: (url: string) => <Text>{url}</Text>,
    },
    {
      title: "上传时间",
      width: "180px",
      dataIndex: "createdAt",
      render: (timestamp: string) => (
        <Space>
          <CalendarOutlined />
          <span>{dayjs(timestamp).format("YYYY-MM-DD HH:mm:ss")}</span>
        </Space>
      ),
      sorter: (a, b) => dayjs(a.createdAt).unix() - dayjs(b.createdAt).unix(),
    },
    {
      title: "文件类型",
      width: "120px",
      dataIndex: "url",
      render: (url: string) => (
        <Tag icon={isImageUrl(url) ? <PictureOutlined /> : <FileOutlined />} color={isImageUrl(url) ? "blue" : "orange"}>
          {isImageUrl(url) ? "图片" : "文件"}
        </Tag>
      ),
      filters: [
        { text: '图片', value: 'image' },
        { text: '文件', value: 'file' },
      ],
      onFilter: (value, record) =>
        value === 'image' ? isImageUrl(record.url) : !isImageUrl(record.url),
    },
    {
      title: "压缩状态",
      width: "120px",
      dataIndex: "url",
      render: (url: string) => (
        <Tag icon={<CompressOutlined />} color={url.indexOf("tinty/") !== -1 ? "green" : "default"}>
          {url.indexOf("tinty/") !== -1 ? "已压缩" : "原图"}
        </Tag>
      ),
      filters: [
        { text: '已压缩', value: 'compressed' },
        { text: '原图', value: 'original' },
      ],
      onFilter: (value, record) =>
        value === 'compressed' ? record.url.indexOf("tinty/") !== -1 : record.url.indexOf("tinty/") === -1,
    },
    {
      title: "操作",
      width: "120px",
      dataIndex: "options",
      render: (_, record) => (
        <Space>
          <CopyButton value={record.url}>
            {({ copied, copy }) => (
              <Button
                type={copied ? 'primary' : 'default'}
                icon={<LinkOutlined />}
                onClick={copy}
              >
                {copied ? "已复制" : "复制"}
              </Button>
            )}
          </CopyButton>
        </Space>
      ),
    },
  ]

  if (!uploadedFiles) return null;

  return (
    <>
      <UploadStatistics uploadedFiles={uploadedFiles} />
      <Table
        columns={tableColumns}
        dataSource={uploadedFiles}
        rowKey="url"
        pagination={{ pageSize: 10, showSizeChanger: true, showQuickJumper: true }}
      />
      <Modal
        visible={imagePreview.visible}
        footer={null}
        onCancel={handleClosePreview}
        width={800}
      >
        <img alt="preview" style={{ width: '100%', height: 'auto' }} src={imagePreview.url} />
      </Modal>
    </>
  )
}

const UploadHistoryPage: BlitzPage = (props) => {
  return (
    <Layout title="上传历史" {...props}>
      <SectionHeadings name="上传历史记录" />
      <div className="container">
        <Suspense fallback="Loading...">
          <UploadHistory />
        </Suspense>
      </div>
      <style jsx global>{`
        .container {
          background: #fff;
        }
      `}</style>
    </Layout>
  )
}

export default UploadHistoryPage
