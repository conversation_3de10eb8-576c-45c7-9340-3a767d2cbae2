import { useEffect, useState } from "react"
import Layout from "pages/layouts/Layout"
import getToken from "app/users/queries/getToken"
import uploadImagePro from "@/app/task/mutations/tinifyImage"
import { invoke, useMutation } from "@blitzjs/rpc"
import { message } from "antd"
import { DropzoneProps } from "@mantine/dropzone"
import SectionHeadings from "app/components/SectionHeadings"
import { VanishInput as PlaceholdersAndVanishInput } from "oocode"

const COMPRESSION_PLACEHOLDERS = [
  "只需粘贴URL，让我们来把你的图片放上减肥计划！",
  "图片压缩的第一法则是什么？别让像素跑出来！",
  "谁是压缩界的潮流领袖？",
  "丢失的画质藏在哪里？",
  "如何用JavaScript写一个图片瘦身术？",
  "如何搭建自己的图片压缩工作站？"
]

interface CompressedImageResult {
  entry: string;
}

interface AuthToken {
  signature: any;
  policy: string;
  host: string;
  OSSAccessKeyId: string;
  success_action_status: number;
  dirPath: string;
}

const isUrlNeedingEncoding = (url: string): boolean => {
  const hasUnencodedChars = url.indexOf('%') !== -1
  return hasUnencodedChars
}

const getFileTypeFromBase64 = (base64String: string): string | null => {
  const mimeTypeMatch = base64String.match(/^data:([a-zA-Z0-9]+\/[a-zA-Z0-9-.+]+)\;base64,/)
  return mimeTypeMatch && mimeTypeMatch.length > 1 ? mimeTypeMatch[1] || null : null
}

const base64ToUint8Array = (base64: string): Uint8Array => {
  const binaryString = atob(base64)
  const byteArray = new Uint8Array(binaryString.length)
  for (let i = 0; i < binaryString.length; i++) {
    byteArray[i] = binaryString.charCodeAt(i)
  }
  return byteArray
}

const ImageCompressorPage = (props: Partial<DropzoneProps>) => {
  const [authToken, setAuthToken] = useState<AuthToken | null>(null)
  const [uploadImageProMutation] = useMutation(uploadImagePro)

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    console.log(e.target.value)
  }

  const downloadCompressedImage = (blob: Blob) => {
    const downloadUrl = URL.createObjectURL(blob)
    const downloadLink = document.createElement('a')
    downloadLink.href = downloadUrl
    downloadLink.download = 'compressed_image.jpg'

    document.body.appendChild(downloadLink)
    downloadLink.click()
    document.body.removeChild(downloadLink)
    URL.revokeObjectURL(downloadUrl)
  }

  const compressImage = async (imageUrl: string): Promise<Blob | null> => {
    try {
      const result = await uploadImageProMutation({
        url: imageUrl,
      }) as CompressedImageResult

      const byteArray = base64ToUint8Array(result.entry)
      const mimeType = getFileTypeFromBase64(result.entry)
      return new Blob([byteArray], { type: mimeType || undefined })
    } catch (error) {
      console.error('Image compression failed:', error)
      return null
    }
  }

  const handleCompression = async (e: React.FormEvent<HTMLFormElement>, inputUrl: string) => {
    e.preventDefault()
    message.success('正在开始压缩, 请等待')

    const processedUrl = isUrlNeedingEncoding(inputUrl)
      ? inputUrl.trim()
      : encodeURI(inputUrl.trim())

    const compressedImageBlob = await compressImage(processedUrl)

    if (compressedImageBlob) {
      downloadCompressedImage(compressedImageBlob)
      message.success('压缩成功, 已保存本地')
    } else {
      message.error('压缩失败，请检查图片URL是否有效')
    }
  }

  useEffect(() => {
    const initializeAuthToken = async () => {
      const token = await invoke(getToken, {})
      setAuthToken(token)
    }
    void initializeAuthToken()
  }, [])

  if (!authToken) {
    return (
      <Layout title="图片压缩" {...props}>
        <span>正在加载...</span>
      </Layout>
    )
  }

  return (
    <Layout title="图片压缩" {...props}>
      <SectionHeadings name="Compress-o-Matic" />
      <div className="h-[40rem] flex flex-col justify-center items-center px-4">
        <h2 className="mb-10 sm:mb-17 text-xl text-center sm:text-5xl text-black">
          The Ultimate File Diet Plan
        </h2>
        <PlaceholdersAndVanishInput
          placeholders={COMPRESSION_PLACEHOLDERS}
          onChange={handleInputChange}
          onSubmit={handleCompression}
        />
      </div>
    </Layout>
  )
}

export default ImageCompressorPage
