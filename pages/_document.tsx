import Document, { Html, Main, Next<PERSON>, Head } from "next/document"

class MyDocument extends Document {
  render() {
    return (
      <Html lang="en">
        <Head />
        <meta httpEquiv="Content-Security-Policy" content="upgrade-insecure-requests" />
        <title>FLOW</title>
        <body style={{ margin: 0, padding: 0 }}>
          <Main />
          <NextScript />
        </body>
      </Html>
    )
  }
}

export default MyDocument
