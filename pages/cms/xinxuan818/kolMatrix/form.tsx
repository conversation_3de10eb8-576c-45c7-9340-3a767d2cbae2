/* eslint-disable @next/next/no-img-element */
import {
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  DatePicker,
  Form,
  Input,
  InputNumber,
  Radio,
  Select,
  Upload,
  Switch,
  TreeSelect,
  message,
} from "antd"
import React, { useState, useRef, useEffect } from "react"
import { PlusOutlined } from "@ant-design/icons"
const { Option } = Select
import { Breadcrumbs, Anchor } from "@mantine/core"
import Layout from "pages/layouts/Layout"
import { Router, useRouter } from "next/router"
import { invalidateQuery, useMutation, useQuery } from "@blitzjs/rpc"
import submittKolRecord from "app/cms/mutations/submitKolMatrix"
import getKolRecord from "app/cms/mutations/getkolMatrix"
import { upload } from "../../../../utils/aliyun"
// import type { FormInstance } from "antd/es/form"
import dayjs from "dayjs"
import weekdays from "dayjs/plugin/weekday"
dayjs.extend(weekdays)
dayjs.extend(localeData)
import localeData from "dayjs/plugin/localeData"
import { showNotification } from "@mantine/notifications"
import { invoke } from "@blitzjs/rpc"
import getToken from "app/users/queries/getToken"
import updateXC818 from "app/cms/mutations/updateXC818"
// import { useParam } from "@blitzjs/next"

type SizeType = Parameters<typeof Form>[0]["size"]

function getRandomName() {
  return "cms/xinxuan818-website"
}

function uploadImages(file, uploadConfig) {
  let domain = "https://s.xinc818.com/"
  console.log("uploadConfig~~~", uploadConfig)

  return new Promise((resolve, reject) => {
    var xhr = new XMLHttpRequest()
    var formData = new FormData()
    Object.keys(uploadConfig).forEach((key) => {
      formData.append(key, uploadConfig[key])
    })
    const fileName = file.name
    // let uploadName = `${getRandomName() + fileName.substr(fileName.lastIndexOf('.'))}`;
    let uploadName = `${getRandomName()}/${fileName}`
    // if (elChecked.checked) {
    //   uploadName = fileName;
    // }
    const key = `${uploadConfig.dirPath}${uploadName}`
    formData.append("key", key)
    formData.append("x-oss-content-type", "text/javascript;charset=UTF-8")
    // formData.append("content-encoding", "UTF-8")
    // formData.append("text/html", "charset=utf-8")
    formData.append("file", file.file)
    xhr.open("POST", uploadConfig.host)
    xhr.send(formData)
    xhr.onreadystatechange = function () {
      if (xhr.readyState == 4 && xhr.status >= 200 && xhr.status < 300) {
        resolve([domain + key, true])
      }
      if (xhr.status > 300) {
        resolve([false, false])
        xhr.abort()
      }
    }
    xhr.timeout = 10000
    xhr.ontimeout = (event) => {
      resolve([false, false])
    }
  })
}

const App: React.FC = (props) => {
  //   const [componentSize, setComponentSize] = useState<SizeType | "default">("default")
  const router = useRouter()
  const [form] = Form.useForm()
  const [token, setToken] = useState(null)
  const formRef = React.createRef<any>()
  const [imgUrl, setImgUrl] = useState("")
  const postId = router.query.id as any
  console.log("postId", postId)
  const [param, setParam] = useState(null)
  const [submittKolRecordMutation] = useMutation(submittKolRecord, {})
  const [getKolRecordMutation] = useMutation(getKolRecord, {})
  const [updateXC818Mutation] = useMutation(updateXC818, {})

  const initData = async () => {
    if (postId) {
      const result = await getKolRecordMutation({
        id: parseInt(postId),
        type: "detail",
      })
      console.log("resultresultresult", result)
      if (result.status) {
        let data = result.entry as any
        setTimeout(() => {
          setImgUrl(data.avatar)
          form.setFieldsValue({
            imgUrl: data.avatar,
            idNo: data.idNo,
            name: data.name,
            fans: data.fans,
            sortNum: data.sortNum,
          })
        }, 100)
      }
    }
  }

  useEffect(() => {
    const getTokenHandle = async () => {
      const token = (await invoke(getToken, {})) as any
      setToken(token)
    }
    void getTokenHandle()
    //
  }, [])

  useEffect(() => {
    void initData()
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [postId])

  const onFormLayoutChange = (values) => {
    // console.log("values", values)
    // setComponentSize(size)
  }

  const onFinish = async (values: any) => {
    const params = {
      ...values,
      avatar: imgUrl,
      id: postId || "",
      // thumb: "https://s.xinc818.com/files/webcil1nbaofklvbsk3/news1_1.png",
      // date: values.date.format("YYYY-MM-DD"),
    }
    console.log("values", params)
    if (!imgUrl) {
      void message.error("请上传图片!")
      return false
    }
    const result = await submittKolRecordMutation(params)
    console.log("result~~", result)
    if (result.status == true) {
      void message.success("保存成功")

      showNotification({
        title: "notification",
        message: "正在同步日常数据..",
      })
      let json = (await updateXC818Mutation({
        env: "dev",
      })) as any
      json.entry.env = "dev"
      let jsonStr = JSON.stringify(json.entry)
      const [result, status] = (await uploadImages(
        { name: "data_dev.js", file: Buffer.from(jsonStr) },
        token
      )) as any
      showNotification({
        title: "notification",
        message: "同步数据成功! 🤥",
      })

      setTimeout(() => {
        formRef.current?.resetFields()
        router.back()
      }, 250)
    }
  }

  const uploadButton = (
    <div>
      <PlusOutlined />
      <div style={{ marginTop: 8 }}>Upload</div>
    </div>
  )

  return (
    <Layout title="social-form" {...props}>
      <Button
        onClick={() => {
          router.back()
        }}
      >
        返回
      </Button>
      <Form
        labelCol={{ span: 4 }}
        wrapperCol={{ span: 12 }}
        ref={formRef}
        form={form}
        layout="horizontal"
        initialValues={{
          idNo: "",
          fans: "",
          name: "",
          sortNum: 999,
        }}
        onFinish={onFinish}
        onValuesChange={onFormLayoutChange}
      //   size={componentSize as SizeType}
      >
        <Form.Item label="主播图" name="imgUrl">
          <Upload
            maxCount={1}
            listType="picture-card"
            className="avatar-uploader"
            showUploadList={false}
            accept="image/png, image/jpeg"
            customRequest={async (options: any) => {
              let result = (await upload(options.file, () => { })) as any
              setImgUrl(result)
              // let tmp = {}
              // tmp[`thumb`] = result
              // console.log("resultresultresult", result)
              // formRef.current!.setFieldsValue(result)
            }}
          >
            <div>
              {imgUrl ? <img src={imgUrl} alt="avatar" style={{ width: "100%" }} /> : uploadButton}
            </div>
          </Upload>
        </Form.Item>
        {/* <Form.Item label="新闻时间" name="date">
          <DatePicker />
        </Form.Item>
        <Form.Item label="内容" name="commerce">
          <Input.TextArea placeholder="填入内容" />
        </Form.Item> */}
        <Form.Item label="主播名" name="name">
          <Input placeholder="输入数量或总额" />
        </Form.Item>
        <Form.Item label="idNo" name="idNo">
          <Input placeholder="输入idNo" />
        </Form.Item>
        <Form.Item label="fans数量(万)" name="fans">
          <Input placeholder="输入fans数量 (9800万,就输入9800即可)" />
        </Form.Item>
        <Form.Item label="置顶排序" name="sortNum">
          <Input placeholder="输入数字" />
        </Form.Item>
        <Form.Item wrapperCol={{ span: 16, offset: 8 }}>
          <Button type="primary" htmlType="submit">
            保存
          </Button>
        </Form.Item>
      </Form>
    </Layout>
  )
}

export default App
