import { useEffect, useState } from "react"
import Layout from "pages/layouts/Layout"
import getAuth from "app/cms/mutations/getAuth"
import { useMutation } from "@blitzjs/rpc"
import { BlitzPage } from "@blitzjs/next"
import { ErrorComponent } from "@blitzjs/next"
import SocialTable from "./table"

const Home: BlitzPage = (props) => {
  const [getAuthMutation] = useMutation(getAuth, {})
  const [loaded, setLoaded] = useState(false)
  const [hasAuth, setHasAuth] = useState(false)

  useEffect(() => {
    const checkAuth = async () => {
      const res = await getAuthMutation({})
      if (res.status) {
        setHasAuth(true)
      }
      setLoaded(true)
    }
    void checkAuth()
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [])

  return (
    <Layout title="social" {...props}>
      {loaded ? (
        hasAuth ? (
          <SocialTable />
        ) : (
          <ErrorComponent statusCode={403} title={"暂无访问权限"} />
        )
      ) : null}
    </Layout>
  )
}

export default Home
