import { Table, Modal, Input, Button } from "antd"
import { Text, Flex } from "@mantine/core"
import type { ColumnsType } from "antd/es/table"
import type { TableRowSelection } from "antd/es/table/interface"
import React, { useState, useEffect } from "react"
import { useMutation, useQuery } from "@blitzjs/rpc"
import { Tag, Tabs, Select, Form, Switch, Radio, Image } from "antd"
import dayjs from "dayjs"
// import axios from "axios"
import getArticleSocial from "app/cms/mutations/getArticleSocial"
import getArticleNews from "app/cms/mutations/getArticleNews"
import getKols from "app/cms/mutations/getKols"
import getArticleSocialList from "app/cms/mutations/getArticleSocialList"
// import { useXinXuanArticleSocial } from "app/users/hooks/cms/useXinXuanArticleSocial"
import { Router, useRouter } from "next/router"
import { invoke } from "@blitzjs/rpc"
import getToken from "app/users/queries/getToken"
import { showNotification } from "@mantine/notifications"
import deleteKols from "app/cms/mutations/deleteKols"

// import { buffer } from "stream/consumers"
const { confirm } = Modal
const { Option } = Select

function getRandomName() {
  return "cms/xinxuan-website"
}

function uploadImages(file, uploadConfig) {
  let domain = "https://s.xinc818.com/"
  console.log("uploadConfig~~~", uploadConfig)

  return new Promise((resolve, reject) => {
    var xhr = new XMLHttpRequest()
    var formData = new FormData()
    Object.keys(uploadConfig).forEach((key) => {
      formData.append(key, uploadConfig[key])
    })
    const fileName = file.name
    // let uploadName = `${getRandomName() + fileName.substr(fileName.lastIndexOf('.'))}`;
    let uploadName = `${getRandomName()}/${fileName}`
    // if (elChecked.checked) {
    //   uploadName = fileName;
    // }
    const key = `${uploadConfig.dirPath}${uploadName}`
    formData.append("key", key)
    formData.append("x-oss-content-type", "text/javascript")
    // formData.append("content-encoding", "UTF-8")
    // formData.append("text/html", "charset=utf-8")
    formData.append("file", file.file)
    xhr.open("POST", uploadConfig.host)
    xhr.send(formData)
    xhr.onreadystatechange = function () {
      if (xhr.readyState == 4 && xhr.status >= 200 && xhr.status < 300) {
        resolve([domain + key, true])
      }
      if (xhr.status > 300) {
        resolve([false, false])
        xhr.abort()
      }
    }
    xhr.timeout = 10000
    xhr.ontimeout = (event) => {
      resolve([false, false])
    }
  })
}

const App: React.FC = () => {
  const [form] = Form.useForm()
  const [data, setData] = useState([]) as any
  const [news, setNews] = useState([]) as any
  const [kols, setKols] = useState([]) as any
  const router = useRouter()

  const [group, setGroup] = useState("")
  const [getArticleSocialListMutation] = useMutation(getArticleSocialList, {})
  const [deleteKolsMutation] = useMutation(deleteKols, {})

  const getTableData = async () => {
    const res = await getKols({})
    console.log("res", res)
    setData(res.entry || [])
  }
  useEffect(() => {
    void getTableData()
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [])

  const [token, setToken] = useState(null)

  useEffect(() => {
    const getTokenHandle = async () => {
      const token = (await invoke(getToken, {})) as any
      setToken(token)
    }
    void getTokenHandle()
    //
  }, [])

  const columns: ColumnsType<any> = [
    {
      title: "图片(主播介绍)",
      width: "120px",
      dataIndex: "recommendAvatar",
      render(value, record, index) {
        // eslint-disable-next-line jsx-a11y/alt-text
        return <Image src={value} />
      },
    },
    {
      title: "图片(主播带货案例)",
      width: "120px",
      dataIndex: "imgUrl",
      render(value, record, index) {
        // eslint-disable-next-line jsx-a11y/alt-text
        return <Image src={value} />
      },
    },
    {
      title: "主播名",
      width: "120px",
      dataIndex: "name",
    },
    {
      title: "主播案例排序",
      width: "120px",
      dataIndex: "sortNum2",
      sorter: {
        compare: (a, b) => a.sortNum2 - b.sortNum2,
        multiple: 2,
      },
    },
    {
      title: "主播介绍排序",
      width: "120px",
      dataIndex: "sortNum",
      sorter: {
        compare: (a, b) => a.sortNum - b.sortNum,
        multiple: 3,
      },
    },
    {
      title: "主播名(英文)",
      width: "160px",
      dataIndex: "name_en",
    },
    {
      title: "主播标签",
      width: "160px",
      dataIndex: "info",
    },
    // {
    //   title: "主播标签(英文)",
    //   width: "160px",
    //   dataIndex: "info_en",
    // },
    // {
    //   title: "简介",
    //   width: "160px",
    //   dataIndex: "intro",
    // },
    // {
    //   title: "简介(英文)",
    //   width: "160px",
    //   dataIndex: "intro_en",
    // },
    {
      title: "快手ID",
      width: "160px",
      dataIndex: "idNum",
    },
    {
      title: "粉丝数",
      width: "160px",
      dataIndex: "countNum",
    },
    {
      title: "类目",
      width: "160px",
      dataIndex: "category",
    },
    // {
    //   title: "类目(英文)",
    //   width: "160px",
    //   dataIndex: "category_en",
    // },
    {
      title: "操作",
      dataIndex: "options",
      width: "100px",
      render(value, record, index) {
        return (
          <>
            <Button
              type="link"
              onClick={() => {
                void router.push({
                  pathname: "/cms/xinxuan/kols/form",
                  query: { id: record.id },
                })
              }}
            >
              编辑
            </Button>
            <Button
              type="link"
              onClick={() => {
                confirm({
                  title: "温馨提示!!",
                  content: "是否删除数据",
                  onOk: async () => {
                    const status = await deleteKolsMutation({
                      id: record.id,
                    })
                    showNotification({
                      title: "notification",
                      message: "删除数据成功",
                    })
                    form.submit()
                    showNotification({
                      title: "notification",
                      message: "正在同步测试数据..",
                    })
                    let json = await getArticleSocialListMutation({
                      env: "dev",
                    })
                    let jsonStr = JSON.stringify(json.entry)
                    await uploadImages({ name: "data_dev.js", file: Buffer.from(jsonStr) }, token)
                    showNotification({
                      title: "notification",
                      message: "测试数据同步完成。",
                    })
                  },
                  onCancel() {
                    // console.log("Cancel")
                  },
                })
              }}
            >
              删除
            </Button>
          </>
        )
      },
    },
  ]

  const onFinish = async (values: any) => {
    const result = await getKols(values)
    setData(result.entry)
  }

  const onReset = async () => {
    form.resetFields()
    const result = await getKols({})
    setData(result.entry)
  }

  return (
    <>
      <Form
        layout="inline"
        initialValues={{
          id: "",
          title: "",
          title_en: "",
        }}
        className="mb-5"
        form={form}
        name="control-hooks"
        onFinish={onFinish}
      >
        <Form.Item name="name" label="主播名" rules={[{ required: false }]}>
          <Input />
        </Form.Item>
        <Form.Item>
          <Button type="primary" htmlType="submit" style={{ marginRight: "10px" }}>
            查询
          </Button>
          <Button htmlType="button" onClick={onReset} style={{ marginRight: "10px" }}>
            重置
          </Button>
          <Button
            htmlType="button"
            onClick={() => {
              window.open("https://xinxuan-dev.xinc818.com/section/kols")
            }}
          >
            访问测试环境
          </Button>
          <Button
            style={{
              marginLeft: "10px",
            }}
            onClick={async () => {
              void router.push({
                pathname: "/cms/xinxuan/kols/form",
              })
              // showNotification({
              //   title: "notification",
              //   message: "正在同步日常数据..",
              // })
              // let json = await getArticleSocialListMutation({
              //   env: "dev",
              // })
              // json.entry.env = "dev"
              // let jsonStr = JSON.stringify(json.entry)
              // const [result, status] = (await uploadImages(
              //   { name: "data_dev.js", file: Buffer.from(jsonStr) },
              //   token
              // )) as any
              // showNotification({
              //   title: "notification",
              //   message: "同步数据成功! 🤥",
              // })
            }}
          >
            新增红人主播
          </Button>
        </Form.Item>
      </Form>
      <Table
        columns={columns}
        pagination={{ pageSize: 100 }}
        dataSource={data || []}
        scroll={{ x: "max-content" }}
      />
    </>
  )
}

export default App
