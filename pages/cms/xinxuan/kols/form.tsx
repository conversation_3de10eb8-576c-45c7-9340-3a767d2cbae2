// @ts-nocheck
import {
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  DatePicker,
  Form,
  Input,
  InputNumber,
  Radio,
  Select,
  Upload,
  Switch,
  TreeSelect,
  message,
  Space,
  Checkbox,
} from "antd"
import React, { useState, useRef, useEffect } from "react"
import { PlusOutlined, MinusCircleOutlined } from "@ant-design/icons"
import { Breadcrumbs, Anchor } from "@mantine/core"
import Layout from "pages/layouts/Layout"
import { Router, useRouter } from "next/router"
import { invalidateQuery, useMutation, useQuery } from "@blitzjs/rpc"
import submitKols from "app/cms/mutations/submitKols"
import getKolsItem from "app/cms/mutations/getKolsItem"
import { upload } from "../../../../utils/aliyun"
// import type { FormInstance } from "antd/es/form"
import dayjs from "dayjs"
// import { useParam } from "@blitzjs/next"

import { RichTextEditor, Link, useRichTextEditorContext } from "@mantine/tiptap"
import { useEditor } from "@tiptap/react"
// import Highlight from "@tiptap/extension-highlight"
import StarterKit from "@tiptap/starter-kit"
// import Underline from "@tiptap/extension-underline"
// import TextAlign from "@tiptap/extension-text-align"
// import Superscript from "@tiptap/extension-superscript"
// import SubScript from "@tiptap/extension-subscript"
import ImageExtension from "@tiptap/extension-image"
import { FileImageOutlined } from "@ant-design/icons"
import { showNotification } from "@mantine/notifications"
import { invoke } from "@blitzjs/rpc"
import getToken from "app/users/queries/getToken"
import getArticleSocialList from "app/cms/mutations/getArticleSocialList"
const { Option } = Select

type SizeType = Parameters<typeof Form>[0]["size"]

const areas = [
  { label: "Beijing", value: "Beijing" },
  { label: "Shanghai", value: "Shanghai" },
]

const sights = {
  Beijing: ["Tiananmen", "Great Wall"],
  Shanghai: ["Oriental Pearl", "The Bund"],
}

function getRandomName() {
  return "cms/xinxuan-website"
}

function uploadImages(file, uploadConfig) {
  let domain = "https://s.xinc818.com/"
  console.log("uploadConfig~~~", uploadConfig)

  return new Promise((resolve, reject) => {
    var xhr = new XMLHttpRequest()
    var formData = new FormData()
    Object.keys(uploadConfig).forEach((key) => {
      formData.append(key, uploadConfig[key])
    })
    const fileName = file.name
    // let uploadName = `${getRandomName() + fileName.substr(fileName.lastIndexOf('.'))}`;
    let uploadName = `${getRandomName()}/${fileName}`
    // if (elChecked.checked) {
    //   uploadName = fileName;
    // }
    const key = `${uploadConfig.dirPath}${uploadName}`
    formData.append("key", key)
    formData.append("x-oss-content-type", "text/javascript")
    // formData.append("content-encoding", "UTF-8")
    // formData.append("text/html", "charset=utf-8")
    formData.append("file", file.file)
    xhr.open("POST", uploadConfig.host)
    xhr.send(formData)
    xhr.onreadystatechange = function () {
      if (xhr.readyState == 4 && xhr.status >= 200 && xhr.status < 300) {
        resolve([domain + key, true])
      }
      if (xhr.status > 300) {
        resolve([false, false])
        xhr.abort()
      }
    }
    xhr.timeout = 10000
    xhr.ontimeout = (event) => {
      resolve([false, false])
    }
  })
}

const App: React.FC = (props) => {
  //   const [componentSize, setComponentSize] = useState<SizeType | "default">("default")
  const router = useRouter()
  const [form] = Form.useForm()
  const formRef = React.createRef<any>()
  const [thumb, setThumb] = useState("")
  const [recommendAvatar, setRecommendAvatar] = useState("")
  const [token, setToken] = useState(null)
  const [content, setContent] = useState("")
  const postId = router.query.id as any
  console.log("postId", postId)
  const [param, setParam] = useState(null)
  const [submitKolsMutation] = useMutation(submitKols, {})
  const [getKolsItemMutation] = useMutation(getKolsItem, {})
  const [getArticleSocialListMutation] = useMutation(getArticleSocialList, {})

  const editor = useEditor({
    extensions: [StarterKit, ImageExtension, Link],
    content,
  })

  const editorEn = useEditor({
    extensions: [StarterKit, ImageExtension, Link],
    content,
  })

  function InsertStarControl() {
    const { editor } = useRichTextEditorContext()
    return (
      <RichTextEditor.Control
        // onClick={() => editor?.commands.insertContent("⭐")}
        aria-label="Insert star emoji"
        title="Insert star emoji"
      >
        {/* <FileImageOutlined size={16} /> */}
        <Upload
          maxCount={1}
          iconRender={FileImageOutlined}
          className="avatar-uploader"
          showUploadList={false}
          accept="image/png, image/jpeg"
          customRequest={async (options: any) => {
            let result = (await upload(options.file, () => { })) as any
            console.log("result", result)
            // editor?.commands.insertContent(<img src={`${result}`} />)
            editor.chain().focus().setImage({ src: result }).run()
            // editor?.commands.insertContent(<div>1313</div>)
          }}
        >
          <FileImageOutlined size={16} />
        </Upload>

        {/* <IconStar stroke={1.5} size={16} /> */}
      </RichTextEditor.Control>
    )
  }

  const items = [{ title: "辛选", href: "/cms/xinxuan" }, { title: "编辑" }].map((item, index) => (
    <Anchor href={item.href} key={index}>
      {item.title}
    </Anchor>
  ))

  const initData = async () => {
    if (postId) {
      const result = await getKolsItemMutation({
        id: postId - 0, //parseInt(postId)
      })
      console.log("resultresultresult", result)
      if (result.status) {
        let data = result.entry as any
        setTimeout(() => {
          setThumb(data.imgUrl)
          setRecommendAvatar(data.recommendAvatar)
          let list = []
          if (data.list) {
            list = data.list
            list.forEach((item: any) => {
              item.date = dayjs(item.date)
            })
          }
          form.setFieldsValue({
            imgUrl: data.imgUrl,
            recommendAvatar: data.recommendAvatar,
            name: data.name,
            name_en: data.name_en,
            info: data.info,
            info_en: data.info_en,
            intro: data.intro,
            intro_en: data.intro_en,
            idNum: data.idNum,
            countNum: data.countNum,
            category: data.category,
            sortNum: data.sortNum,
            sortNum2: data.sortNum2,
            category_en: data.category_en,
            list: list,
          })
        }, 300)
      }
    }
  }

  useEffect(() => {
    const getTokenHandle = async () => {
      const token = (await invoke(getToken, {})) as any
      setToken(token)
    }
    void getTokenHandle()
    //
  }, [])

  useEffect(() => {
    void initData()
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [postId])

  const onFormLayoutChange = (values) => {
    // console.log("values", values)
    // setComponentSize(size)
  }

  const onFinish = async (values: any) => {
    console.log("values", values)
    let list = values.list
    if (list) {
      list.forEach((item) => {
        item.date = dayjs(item.date).format("YYYY-MM-DD")
        item.year = dayjs(item.date).format("YYYY")
      })
    }

    const params = {
      ...values,
      imgUrl: thumb,
      recommendAvatar: recommendAvatar,
      id: postId || "",
      list: list,
      // content: editor?.getHTML(),
      // content_en: editorEn?.getHTML(),
      // thumb: "https://s.xinc818.com/files/webcil1nbaofklvbsk3/news1_1.png",
      // time: values.time.format("YYYY-MM-DD"),
    }

    console.log("values", params)
    if (!thumb) {
      void message.error("请上传图片!")
      return false
    }
    const result = await submitKolsMutation(params)
    console.log("result~~", result)
    if (result.status == true) {
      void message.success("创建成功")

      showNotification({
        title: "notification",
        message: "正在同步日常数据..",
      })
      let json = await getArticleSocialListMutation({
        env: "dev",
      })
      json.entry.env = "dev"
      let jsonStr = JSON.stringify(json.entry)
      const [result, status] = (await uploadImages(
        { name: "data_dev.js", file: Buffer.from(jsonStr) },
        token
      )) as any
      showNotification({
        title: "notification",
        message: "同步数据成功! 🤥",
      })

      setTimeout(() => {
        formRef.current?.resetFields()
        router.back()
      }, 250)
    }
  }

  const uploadButton = (
    <div>
      <PlusOutlined />
      <div style={{ marginTop: 8 }}>Upload</div>
    </div>
  )

  const formItemLayout = {
    labelCol: {
      xs: { span: 24 },
      sm: { span: 4 },
    },
    wrapperCol: {
      xs: { span: 24 },
      sm: { span: 20 },
    },
  }
  const formItemLayoutWithOutLabel = {
    wrapperCol: {
      xs: { span: 24, offset: 0 },
      sm: { span: 20, offset: 4 },
    },
  }

  return (
    <Layout title="social-form" {...props}>
      <Button
        onClick={() => {
          router.back()
        }}
      >
        返回
      </Button>
      <Form
        labelCol={{ span: 4 }}
        wrapperCol={{ span: 16 }}
        ref={formRef}
        form={form}
        layout="horizontal"
        initialValues={{
          imgUrl: "",
          recommendAvatar: "",
          name: "",
          name_en: "",
          info: "",
          info_en: "",
          intro: "",
          intro_en: "",
          idNum: "",
          countNum: "",
          category: "",
          category_en: "",
          // sights: {
          //   type: "1",
          // },
        }}
        onFinish={onFinish}
        onValuesChange={onFormLayoutChange}
      //   size={componentSize as SizeType}
      >
        <Form.Item label="主播图片(主播介绍)" name="recommendAvatar">
          <Upload
            maxCount={1}
            listType="picture-card"
            className="avatar-uploader"
            showUploadList={false}
            accept="image/png, image/jpeg"
            customRequest={async (options: any) => {
              let result = (await upload(options.file, () => { })) as any
              setRecommendAvatar(result)
              // let tmp = {}
              // tmp[`thumb`] = result
              // console.log("resultresultresult", result)
              // formRef.current!.setFieldsValue(result)
            }}
          >
            <div>
              {thumb ? <img src={recommendAvatar} alt="avatar" style={{ width: "100%" }} /> : uploadButton}
            </div>
          </Upload>
        </Form.Item>
        <Form.Item label="主播图片(带货案例, 联系开发开启)" name="imgUrl">
          <Upload
            maxCount={1}
            listType="picture-card"
            className="avatar-uploader"
            showUploadList={false}
            accept="image/png, image/jpeg"
            customRequest={async (options: any) => {
              let result = (await upload(options.file, () => { })) as any
              setThumb(result)
              // let tmp = {}
              // tmp[`thumb`] = result
              // console.log("resultresultresult", result)
              // formRef.current!.setFieldsValue(result)
            }}
          >
            <div>
              {thumb ? <img src={thumb} alt="avatar" style={{ width: "100%" }} /> : uploadButton}
            </div>
          </Upload>
        </Form.Item>
        <Form.Item label="主播名" name="name" required>
          <Input placeholder="" />
        </Form.Item>
        <Form.Item label="主播名(英文)" name="name_en" required>
          <Input placeholder="" />
        </Form.Item>
        <Form.Item label="主播标签" name="info" required>
          <Input placeholder="" />
        </Form.Item>
        <Form.Item label="主播标签(英文)" name="info_en" required>
          <Input placeholder="" />
        </Form.Item>
        <Form.Item label="主播简介" name="intro" required>
          <Input placeholder="" />
        </Form.Item>
        <Form.Item label="主播简介(英文)" name="intro_en" required>
          <Input placeholder="" />
        </Form.Item>
        <Form.Item label="快手ID" name="idNum" required>
          <Input placeholder="" />
        </Form.Item>
        <Form.Item label="粉丝数量" name="countNum" required>
          <Input placeholder="" />
        </Form.Item>
        <Form.Item label="主播介绍的排序值" name="sortNum">
          <Input placeholder="" />
        </Form.Item>
        <Form.Item label="主播案例的排序值" name="sortNum2">
          <Input placeholder="" />
        </Form.Item>
        <Form.Item label="类目" name="category" required>
          <Input placeholder="" />
        </Form.Item>
        <Form.Item label="类目(英文)" name="category_en" required>
          <Input placeholder="" />
        </Form.Item>
        <div style={{ marginLeft: "110px" }}>
          <Form.List name="list">
            {(fields, { add, remove }) => (
              <>
                {fields.map(({ key, name, ...restField }) => (
                  <Space key={key} style={{ display: "flex", marginBottom: 8 }} align="baseline">
                    {/* <Form.Item
                      {...restField}
                      name={[name, "type"]}
                      label="战绩类型"
                      labelCol={{ offset: 1 }}
                      rules={[{ required: true, message: "Missing first name" }]}
                    >
                      <Select style={{ width: "60px" }}>
                        <Select.Option key={"1"} value={"1"}>
                          默认
                        </Select.Option>
                        <Select.Option key={"2"} value={"2"}>
                          重要
                        </Select.Option>
                      </Select>
                    </Form.Item> */}
                    <Form.Item
                      {...restField}
                      name={[name, "date"]}
                      label="日期"
                      labelCol={{ offset: 0 }}
                      rules={[{ required: true, message: "Missing" }]}
                    >
                      <DatePicker />
                    </Form.Item>
                    <Form.Item
                      {...restField}
                      valuePropName="checked"
                      name={[name, "dateType"]}
                      label="只显示年"
                      labelCol={{ offset: 0 }}
                      rules={[{ required: false, message: "Missing" }]}
                    >
                      <Switch />
                    </Form.Item>
                    <Form.Item
                      {...restField}
                      name={[name, "customStr"]}
                      label="特殊日期"
                      labelCol={{ offset: 0 }}
                      rules={[{ required: false, message: "Missing" }]}
                    >
                      <Input style={{ width: "60px" }} />
                    </Form.Item>
                    <Form.Item
                      {...restField}
                      name={[name, "content"]}
                      label="内容"
                      labelCol={{ offset: 0 }}
                      rules={[{ required: true, message: "Missing" }]}
                    >
                      <Input.TextArea style={{ width: "210px" }} placeholder="Last Name" />
                    </Form.Item>
                    <Form.Item
                      {...restField}
                      name={[name, "content_en"]}
                      label="内容(英)"
                      labelCol={{ offset: 0 }}
                      rules={[{ required: false, message: "Missing" }]}
                    >
                      <Input.TextArea style={{ width: "200px" }} placeholder="Last Name" />
                    </Form.Item>
                    <MinusCircleOutlined onClick={() => remove(name)} />
                  </Space>
                ))}
                <Form.Item>
                  <Button type="dashed" onClick={() => add()} block icon={<PlusOutlined />}>
                    添加战绩
                  </Button>
                </Form.Item>
              </>
            )}
          </Form.List>
        </div>
        <Form.Item wrapperCol={{ span: 16, offset: 8 }}>
          <Button type="primary" htmlType="submit">
            保存
          </Button>
        </Form.Item>
      </Form>
    </Layout>
  )
}

export default App
