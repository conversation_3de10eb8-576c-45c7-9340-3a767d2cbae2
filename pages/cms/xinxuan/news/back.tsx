import { <PERSON>, Modal, Button } from "antd"
import { Text, Flex } from "@mantine/core"
import type { ColumnsType } from "antd/es/table"
import type { TableRowSelection } from "antd/es/table/interface"
import React, { useState, useEffect } from "react"
import { useMutation, useQuery } from "@blitzjs/rpc"
import { Tag, Tabs, Select, Form, Switch, Radio, Image } from "antd"
// import dayjs from "dayjs"
// import axios from "axios"
import getArticleSocial from "app/cms/mutations/getArticleSocial"
import getArticleNews from "app/cms/mutations/getArticleNews"
import getKols from "app/cms/mutations/getKols"
import getArticleSocialList from "app/cms/mutations/getArticleSocialList"
// import { useXinXuanArticleSocial } from "app/users/hooks/cms/useXinXuanArticleSocial"
import { Router, useRouter } from "next/router"
import { invoke } from "@blitzjs/rpc"
import getToken from "app/users/queries/getToken"
// import { buffer } from "stream/consumers"
const { confirm } = Modal

interface DataType {
  key: React.Key
  name: string
  age: number
  address: string
}

function getRandomName() {
  return "cms/xinxuan-website"
}

function uploadImages(file, uploadConfig) {
  let domain = "https://s.xinc818.com/"
  console.log("uploadConfig~~~", uploadConfig)

  return new Promise((resolve, reject) => {
    var xhr = new XMLHttpRequest()
    var formData = new FormData()
    Object.keys(uploadConfig).forEach((key) => {
      formData.append(key, uploadConfig[key])
    })
    const fileName = file.name
    // let uploadName = `${getRandomName() + fileName.substr(fileName.lastIndexOf('.'))}`;
    let uploadName = `${getRandomName()}/${fileName}`
    // if (elChecked.checked) {
    //   uploadName = fileName;
    // }
    const key = `${uploadConfig.dirPath}${uploadName}`
    formData.append("key", key)
    formData.append("x-oss-content-type", "text/javascript")
    // formData.append("content-encoding", "UTF-8")
    // formData.append("text/html", "charset=utf-8")
    formData.append("file", file.file)
    xhr.open("POST", uploadConfig.host)
    xhr.send(formData)
    xhr.onreadystatechange = function () {
      if (xhr.readyState == 4 && xhr.status >= 200 && xhr.status < 300) {
        resolve([domain + key, true])
      }
      if (xhr.status > 300) {
        resolve([false, false])
        xhr.abort()
      }
    }
    xhr.timeout = 10000
    xhr.ontimeout = (event) => {
      resolve([false, false])
    }
  })
}

const App: React.FC = () => {
  const [data, setData] = useState([]) as any
  const [news, setNews] = useState([]) as any
  const [kols, setKols] = useState([]) as any
  const router = useRouter()
  const [getArticleSocialMutation] = useMutation(getArticleSocial, {})
  const [getArticleNewsMutation] = useMutation(getArticleNews, {})
  const [getArticleSocialListMutation] = useMutation(getArticleSocialList, {})

  const [group, setGroup] = useState("")

  const getTableData = async () => {
    const res = await getArticleSocialMutation({})
    console.log("res", res)
    setData(res.entry || [])
  }
  const viewJsonContent = async () => {
    let json = await getArticleSocialListMutation()
    console.log("jsonjson", json)
    return json
    // console.log("viewJsonContent1")
    // const data = await axios.get(
    //   "https://s.xinc818.com/files/cms/xinxuan-website/data.js?t=" + Date.now(),
    //   {}
    // )
    // console.log("viewJsonContent2", data)
  }
  useEffect(() => {
    void getTableData()
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [])

  const [token, setToken] = useState(null)

  useEffect(() => {
    const getTokenHandle = async () => {
      const token = (await invoke(getToken, {})) as any
      setToken(token)
    }
    void getTokenHandle()
    //
  }, [])

  const columns: ColumnsType<any> = [
    {
      title: "封面图",
      width: "80px",
      dataIndex: "thumb",
      render(value, record, index) {
        // eslint-disable-next-line jsx-a11y/alt-text
        return <Image src={value} />
      },
    },
    {
      title: "内容",
      width: "160px",
      dataIndex: "content",
      render(value, record, index) {
        return <Text lineClamp={2}>{value}</Text>
      },
    },
    {
      title: "内容(英文)",
      width: "160px",
      dataIndex: "content_en",
      render(value, record, index) {
        return <Text lineClamp={2}>{value}</Text>
      },
    },
    {
      title: "类目",
      width: "60px",
      dataIndex: "group",
      render(value, record, index) {
        const data = {
          csjz: "慈善救灾",
          xhzx: "辛火助学",
          xhjh: "辛火计划",
          xczx: "乡村振兴",
          kyjz: "抗疫捐赠",
          qtgy: "其他公益",
        }
        return data[value] || "/"
      },
    },
    {
      title: "新闻日期",
      dataIndex: "timeStr",
      width: "80px",
      // render(value, record, index) {
      //   return value
      // },
    },
    {
      title: "操作",
      dataIndex: "options",
      width: "100px",
      render(value, record, index) {
        return (
          <>
            <Button
              type="link"
              onClick={() => {
                void router.push({
                  pathname: "/cms/xinxuan/social/form",
                  query: { id: record.id },
                })
              }}
            >
              编辑
            </Button>
            <Button
              type="link"
              onClick={() => {
                void router.push({
                  pathname: "/cms/xinxuan/social/form",
                  query: { id: record.id },
                })
              }}
            >
              预览
            </Button>
          </>
        )
      },
    },
  ]

  const columns2: ColumnsType<any> = [
    {
      title: "封面图",
      width: "80px",
      dataIndex: "thumb",
      render(value, record, index) {
        // eslint-disable-next-line jsx-a11y/alt-text
        return <Image src={value} />
      },
    },
    {
      title: "标题",
      width: "160px",
      dataIndex: "title",
      render(value, record, index) {
        return <Text lineClamp={2}>{value}</Text>
      },
    },
    {
      title: "标题(英文)",
      width: "160px",
      dataIndex: "title_en",
      render(value, record, index) {
        return <Text lineClamp={2}>{value}</Text>
      },
    },
    // {
    //   title: "内容",
    //   width: "160px",
    //   dataIndex: "content",
    // },
    // {
    //   title: "内容(英文)",
    //   width: "160px",
    //   dataIndex: "content_en",
    // },
    {
      title: "新闻日期",
      dataIndex: "timeStr",
      width: "100px",
      // render(value, record, index) {
      //   return value
      // },
    },
    {
      title: "来源",
      dataIndex: "from",
      width: "100px",
    },
    {
      title: "来源(英文)",
      dataIndex: "from_en",
      width: "100px",
    },
    // {
    //   title: "链接",
    //   dataIndex: "link",
    //   width: "100px",
    //   render(value, record, index) {
    //     return <Text lineClamp={2}>{value}</Text>
    //   },
    // },
    {
      title: "操作",
      dataIndex: "options",
      width: "100px",
      render(value, record, index) {
        return (
          <>
            <Button
              type="link"
              onClick={() => {
                void router.push({
                  pathname: "/cms/xinxuan/news/form",
                  query: { id: record.id },
                })
              }}
            >
              编辑
            </Button>
            <Button
              type="link"
              onClick={() => {
                void router.push({
                  pathname: "/cms/xinxuan/social/form",
                  query: { id: record.id },
                })
              }}
            >
              预览
            </Button>
          </>
        )
      },
    },
  ]

  const columns3: ColumnsType<any> = [
    {
      title: "图片",
      width: "120px",
      dataIndex: "imgUrl",
      render(value, record, index) {
        // eslint-disable-next-line jsx-a11y/alt-text
        return <Image src={value} />
      },
    },
    {
      title: "主播名",
      width: "120px",
      dataIndex: "name",
    },
    {
      title: "主播案例排序值",
      width: "120px",
      dataIndex: "sortNum2",
      sorter: {
        compare: (a, b) => a.sortNum2 - b.sortNum2,
        multiple: 2,
      },
    },
    {
      title: "主播介绍排序值(人多的模块)",
      width: "120px",
      dataIndex: "sortNum",
      sorter: {
        compare: (a, b) => a.sortNum - b.sortNum,
        multiple: 3,
      },
    },
    {
      title: "主播名(英文)",
      width: "160px",
      dataIndex: "name_en",
    },
    {
      title: "主播标签",
      width: "160px",
      dataIndex: "info",
    },
    {
      title: "主播标签(英文)",
      width: "160px",
      dataIndex: "info_en",
    },
    {
      title: "简介",
      width: "160px",
      dataIndex: "intro",
    },
    {
      title: "简介(英文)",
      width: "160px",
      dataIndex: "intro_en",
    },
    {
      title: "快手ID",
      width: "160px",
      dataIndex: "idNum",
    },
    {
      title: "粉丝数",
      width: "160px",
      dataIndex: "countNum",
    },
    {
      title: "类目",
      width: "160px",
      dataIndex: "category",
    },
    {
      title: "类目(英文)",
      width: "160px",
      dataIndex: "category_en",
    },
    {
      title: "操作",
      dataIndex: "options",
      width: "100px",
      render(value, record, index) {
        return (
          <>
            <Button
              type="link"
              onClick={() => {
                void router.push({
                  pathname: "/cms/xinxuan/kols/form",
                  query: { id: record.id },
                })
              }}
            >
              编辑
            </Button>
          </>
        )
      },
    },
  ]

  // const onChange = () => {
  //   const tasks = useXinXuanArticleSocial()
  // }

  const items = [
    {
      label: "社会公益",
      key: "1",
      children: (
        <>
          <Flex gap="md" justify="space-between" direction="row" wrap="wrap">
            <Form
              layout="inline"
              className="components-table-demo-control-bar"
              style={{ marginBottom: 16 }}
            >
              <Form.Item label="类目">
                <Radio.Group
                  value={group}
                  onChange={async (e) => {
                    let val = e.target.value
                    setGroup(val)
                    const result = await getArticleSocialMutation({
                      group: val,
                    })
                    setData(result.entry)
                  }}
                >
                  <Radio.Button value="">所有</Radio.Button>
                  <Radio.Button value="csjz">慈善救灾</Radio.Button>
                  <Radio.Button value="xhzx">辛火助学</Radio.Button>
                  <Radio.Button value="xhjh">辛火计划</Radio.Button>
                  <Radio.Button value="xczx">乡村振兴</Radio.Button>
                  <Radio.Button value="kyjz">抗议捐赠</Radio.Button>
                  <Radio.Button value="qtgy">其他公益</Radio.Button>
                </Radio.Group>
              </Form.Item>
            </Form>
            <Button
              style={{
                marginRight: "10px",
              }}
              onClick={() => {
                void router.push({
                  pathname: "/cms/xinxuan/social/form",
                })
              }}
            >
              新增公益文章
            </Button>
          </Flex>

          <Table
            columns={columns}
            pagination={{ pageSize: 50 }}
            dataSource={data || []}
            scroll={{ x: "max-content" }}
          />
        </>
      ),
    }, // 务必填写 key
    {
      label: "新闻",
      key: "2",
      children: (
        <>
          <Button
            style={{
              marginRight: "10px",
            }}
            onClick={() => {
              void router.push({
                pathname: "/cms/xinxuan/news/form",
              })
            }}
          >
            新增新闻
          </Button>

          <Table columns={columns2} dataSource={news || []} scroll={{ x: "max-content" }} />
        </>
      ),
    },
    {
      label: "主播数据",
      key: "3",
      children: (
        <>
          <Button
            style={{
              marginRight: "10px",
            }}
            onClick={() => {
              void router.push({
                pathname: "/cms/xinxuan/kols/form",
              })
            }}
          >
            新增主播
          </Button>
          <Table
            columns={columns3}
            dataSource={kols || []}
            scroll={{
              x: "max-content",
            }}
          />
        </>
      ),
    },
  ]

  return (
    <>
      <Flex gap="md" justify="flex-end" align="flex-start" direction="row" wrap="wrap">
        <Button
          style={{
            marginRight: "10px",
          }}
          onClick={async () => {
            let json = await getArticleSocialListMutation({
              env: "dev",
            })
            json.entry.env = "dev"
            let jsonStr = JSON.stringify(json.entry)
            const [result, status] = (await uploadImages(
              { name: "data_dev.js", file: Buffer.from(jsonStr) },
              token
            )) as any
            console.log("statusstatus", status, result)
          }}
        >
          保存&预览日常环境
        </Button>
        <Button
          style={{
            marginRight: "10px",
          }}
          onClick={async () => {
            confirm({
              title: "温馨提示!",
              // icon: <ExclamationCircleFilled />,
              content: "确定部署到线上",
              onOk: async () => {
                let json = await getArticleSocialListMutation({
                  env: "prod",
                })
                let jsonStr = JSON.stringify(json.entry)
                const [result, status] = (await uploadImages(
                  { name: "data.js", file: Buffer.from(jsonStr) },
                  token
                )) as any
                console.log("statusstatus", status, result)
              },
              onCancel() {
                console.log("Cancel")
              },
            })
          }}
        >
          线上部署
        </Button>
        {/* <Button
          style={{
            marginRight: "10px",
          }}
          onClick={async () => {
            let json = await getArticleSocialListMutation()
            let jsonStr = JSON.stringify(json.entry)
            const [result, status] = (await uploadImages(
              { name: "test.js", file: Buffer.from(jsonStr) },
              token
            )) as any
          }}
        >
          test
        </Button> */}
        <Button
          onClick={async () => {
            // window.open("")
            const res = (await viewJsonContent()) as any
            console.log("cccc", res)
            try {
              let str = JSON.stringify(res.entry)
              let o = JSON.parse(str)
              console.log("ttt", o)
            } catch (e) {
              console.log("ffff_error", e)
            }
          }}
        >
          查看日常环境内容
        </Button>
      </Flex>

      <Tabs
        items={items}
        onChange={async (e) => {
          console.log("e", e)
          if (e == "1") {
            await getArticleSocialMutation({})
          } else if (e == "2") {
            const newsData = await getArticleNewsMutation({})
            setNews(newsData.entry)
          } else if (e == "3") {
            const newsData = await getKols({})
            setKols(newsData.entry)
          }
          // onChange()
        }}
      />
    </>
  )
}

export default App
