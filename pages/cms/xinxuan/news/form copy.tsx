/* eslint-disable @next/next/no-img-element */
// @ts-nocheck

import {
  Button,
  Cascader,
  DatePicker,
  Form,
  Input,
  InputNumber,
  Radio,
  Select,
  Upload,
  Switch,
  TreeSelect,
  message,
} from "antd"
import React, { useState, useRef, useEffect } from "react"
import { PlusOutlined } from "@ant-design/icons"
const { Option } = Select
import { Breadcrumbs, Anchor } from "@mantine/core"
import Layout from "pages/layouts/Layout"
import { Router, useRouter } from "next/router"
import { invalidateQuery, useMutation, useQuery } from "@blitzjs/rpc"
import submitArticleNews from "app/cms/mutations/submitArticleNews"
import getArticleNewsItem from "app/cms/mutations/getArticleNewsItem"
import { upload } from "../../../../utils/aliyun"
import type { FormInstance } from "antd/es/form"
import dayjs from "dayjs"
// import { useParam } from "@blitzjs/next"

import { RichTextEditor, Link, useRichTextEditorContext } from "@mantine/tiptap"
import { useEditor } from "@tiptap/react"
// import Highlight from "@tiptap/extension-highlight"
import StarterKit from "@tiptap/starter-kit"
// import Underline from "@tiptap/extension-underline"
// import TextAlign from "@tiptap/extension-text-align"
// import Superscript from "@tiptap/extension-superscript"
// import SubScript from "@tiptap/extension-subscript"
import ImageExtension from "@tiptap/extension-image"
import { FileImageOutlined } from "@ant-design/icons"
import { showNotification } from "@mantine/notifications"
import { invoke } from "@blitzjs/rpc"
import getToken from "app/users/queries/getToken"
import getArticleSocialList from "app/cms/mutations/getArticleSocialList"

type SizeType = Parameters<typeof Form>[0]["size"]

function getRandomName() {
  return "cms/xinxuan-website"
}

function uploadImages(file, uploadConfig) {
  let domain = "https://s.xinc818.com/"
  console.log("uploadConfig~~~", uploadConfig)

  return new Promise((resolve, reject) => {
    var xhr = new XMLHttpRequest()
    var formData = new FormData()
    Object.keys(uploadConfig).forEach((key) => {
      formData.append(key, uploadConfig[key])
    })
    const fileName = file.name
    // let uploadName = `${getRandomName() + fileName.substr(fileName.lastIndexOf('.'))}`;
    let uploadName = `${getRandomName()}/${fileName}`
    // if (elChecked.checked) {
    //   uploadName = fileName;
    // }
    const key = `${uploadConfig.dirPath}${uploadName}`
    formData.append("key", key)
    formData.append("x-oss-content-type", "text/javascript")
    // formData.append("content-encoding", "UTF-8")
    // formData.append("text/html", "charset=utf-8")
    formData.append("file", file.file)
    xhr.open("POST", uploadConfig.host)
    xhr.send(formData)
    xhr.onreadystatechange = function () {
      if (xhr.readyState == 4 && xhr.status >= 200 && xhr.status < 300) {
        resolve([domain + key, true])
      }
      if (xhr.status > 300) {
        resolve([false, false])
        xhr.abort()
      }
    }
    xhr.timeout = 10000
    xhr.ontimeout = (event) => {
      resolve([false, false])
    }
  })
}

const App: React.FC = () => {
  //   const [componentSize, setComponentSize] = useState<SizeType | "default">("default")
  const router = useRouter()
  const [form] = Form.useForm()
  const formRef = React.createRef<any>()
  const [thumb, setThumb] = useState("")
  const [token, setToken] = useState(null)
  const [content, setContent] = useState("")
  const postId = router.query.id as any
  console.log("postId", postId)
  const [param, setParam] = useState(null)
  const [ssubmitArticleNewsMutation] = useMutation(submitArticleNews, {})
  const [getArticleNewsItemMutation] = useMutation(getArticleNewsItem, {})
  const [getArticleSocialListMutation] = useMutation(getArticleSocialList, {})

  const editor = useEditor({
    extensions: [StarterKit, ImageExtension, Link],
    content,
  })

  const editorEn = useEditor({
    extensions: [StarterKit, ImageExtension, Link],
    content,
  })

  function InsertStarControl() {
    const { editor } = useRichTextEditorContext()
    return (
      <RichTextEditor.Control
        // onClick={() => editor?.commands.insertContent("⭐")}
        aria-label="Insert star emoji"
        title="Insert star emoji"
      >
        {/* <FileImageOutlined size={16} /> */}
        <Upload
          maxCount={1}
          iconRender={FileImageOutlined}
          className="avatar-uploader"
          showUploadList={false}
          accept="image/png, image/jpeg"
          customRequest={async (options: any) => {
            let result = (await upload(options.file, () => { })) as any
            console.log("result", result)
            // editor?.commands.insertContent(<img src={`${result}`} />)
            editor.chain().focus().setImage({ src: result }).run()
            // editor?.commands.insertContent(<div>1313</div>)
          }}
        >
          <FileImageOutlined size={16} />
        </Upload>

        {/* <IconStar stroke={1.5} size={16} /> */}
      </RichTextEditor.Control>
    )
  }

  const items = [{ title: "辛选", href: "/cms/xinxuan" }, { title: "编辑" }].map((item, index) => (
    <Anchor href={item.href} key={index}>
      {item.title}
    </Anchor>
  ))

  const initData = async () => {
    if (postId) {
      const result = await getArticleNewsItemMutation({
        id: parseInt(postId),
      })
      console.log("resultresultresult", result)
      if (result.status) {
        let data = result.entry as any
        setTimeout(() => {
          setThumb(data.thumb)
          console.log("ffff_isfocusNews", data.isfocusNews)
          form.setFieldsValue({
            title: data.title,
            title_en: data.title_en,
            from: data.from,
            from_en: data.from_en,
            link: data.link,
            thumb: data.thumb,
            content: data.content,
            content_en: data.content_en,
            time: dayjs(data.time),
            info: data.info,
            info: data.info_en,
            coverImage: data.coverImage,
            isHomeNews: data.isHomeNews,
            isfocusNews: data.isfocusNews,
          })
          editor?.commands.setContent(data.content)
          editorEn?.commands.setContent(data.content_en)
        }, 300)
      }
    }
  }

  useEffect(() => {
    const getTokenHandle = async () => {
      const token = (await invoke(getToken, {})) as any
      setToken(token)
    }
    void getTokenHandle()
    //
  }, [])

  useEffect(() => {
    setTimeout(() => {
      void initData()
    }, 300)
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [postId, editor])

  const onFormLayoutChange = (values) => {
    // console.log("values", values)
    // setComponentSize(size)
  }

  const onFinish = async (values: any) => {
    const params = {
      ...values,
      thumb: thumb,
      id: postId || "",
      content: editor?.getHTML(),
      content_en: editorEn?.getHTML(),
      // thumb: "https://s.xinc818.com/files/webcil1nbaofklvbsk3/news1_1.png",
      time: values.time.format("YYYY-MM-DD"),
    }
    console.log("values", params)
    if (!thumb) {
      void message.error("请上传图片!")
      return false
    }
    const result = await ssubmitArticleNewsMutation(params)
    console.log("result~~", result)

    showNotification({
      title: "notification",
      message: "正在同步日常数据..",
    })
    let json = await getArticleSocialListMutation({
      env: "dev",
    })
    json.entry.env = "dev"
    let jsonStr = JSON.stringify(json.entry)
    const [result2, status] = (await uploadImages(
      { name: "data_dev.js", file: Buffer.from(jsonStr) },
      token
    )) as any
    showNotification({
      title: "notification",
      message: "同步数据成功! 🤥",
    })

    if (result.status == true) {
      void message.success("创建成功")
      setTimeout(() => {
        formRef.current?.resetFields()
        router.back()
      }, 250)
    }
  }

  const uploadButton = (
    <div>
      <PlusOutlined />
      <div style={{ marginTop: 8 }}>Upload</div>
    </div>
  )

  return (
    <Layout title="social-form">
      <Button
        onClick={() => {
          router.back()
        }}
      >
        返回
      </Button>
      <Form
        labelCol={{ span: 4 }}
        wrapperCol={{ span: 12 }}
        ref={formRef}
        form={form}
        layout="horizontal"
        initialValues={{
          type: "csjz",
          content: "",
          content_en: "",
          time: "",
          title: "",
          title_en: "",
          form: "",
          form_en: "",
          isfocusNews: false,
          isHomeNews: false,
        }}
        onFinish={onFinish}
        onValuesChange={onFormLayoutChange}
      //   size={componentSize as SizeType}
      >
        <Form.Item label="封面图" name="thumb">
          <Upload
            maxCount={1}
            listType="picture-card"
            className="avatar-uploader"
            showUploadList={false}
            accept="image/png, image/jpeg"
            customRequest={async (options: any) => {
              let result = (await upload(options.file, () => { })) as any
              setThumb(result)
              // let tmp = {}
              // tmp[`thumb`] = result
              // console.log("resultresultresult", result)
              // formRef.current!.setFieldsValue(result)
            }}
          >
            <div>
              {thumb ? <img src={thumb} alt="avatar" style={{ width: "100%" }} /> : uploadButton}
            </div>
          </Upload>
        </Form.Item>
        <Form.Item label="标题" name="title" required>
          <Input placeholder="" />
        </Form.Item>
        <Form.Item label="标题(英文)" name="title_en">
          <Input placeholder="" />
        </Form.Item>
        <Form.Item label="简介描述" name="info" required>
          <Input placeholder="" />
        </Form.Item>
        <Form.Item label="简介描述(英文)" name="info_en">
          <Input placeholder="" />
        </Form.Item>
        <Form.Item label="内容" name="content" required>
          {/* <Input.TextArea placeholder="" /> */}
          <RichTextEditor editor={editor}>
            <RichTextEditor.Toolbar sticky stickyOffset={60}>
              <RichTextEditor.ControlsGroup>
                <RichTextEditor.Bold />
                <RichTextEditor.Italic />
              </RichTextEditor.ControlsGroup>

              <RichTextEditor.ControlsGroup>
                <RichTextEditor.H1 />
                <RichTextEditor.H2 />
                <RichTextEditor.H3 />
                <RichTextEditor.H4 />
              </RichTextEditor.ControlsGroup>

              <RichTextEditor.ControlsGroup>
                <RichTextEditor.Blockquote />
                <RichTextEditor.Hr />
                <InsertStarControl />
              </RichTextEditor.ControlsGroup>
            </RichTextEditor.Toolbar>

            <RichTextEditor.Content />
          </RichTextEditor>
        </Form.Item>
        <Form.Item label="内容(英文)" name="content_en">
          <RichTextEditor editor={editorEn}>
            <RichTextEditor.Toolbar sticky stickyOffset={60}>
              <RichTextEditor.ControlsGroup>
                <RichTextEditor.Bold />
                <RichTextEditor.Italic />
              </RichTextEditor.ControlsGroup>

              <RichTextEditor.ControlsGroup>
                <RichTextEditor.H1 />
                <RichTextEditor.H2 />
                <RichTextEditor.H3 />
                <RichTextEditor.H4 />
              </RichTextEditor.ControlsGroup>

              <RichTextEditor.ControlsGroup>
                <RichTextEditor.Blockquote />
                <RichTextEditor.Hr />
                <InsertStarControl />
              </RichTextEditor.ControlsGroup>
            </RichTextEditor.Toolbar>

            <RichTextEditor.Content />
          </RichTextEditor>
        </Form.Item>
        <Form.Item label="新闻来源" name="from" required>
          <Input placeholder="" />
        </Form.Item>
        <Form.Item label="新闻来源(英文)" name="from_en">
          <Input placeholder="" />
        </Form.Item>
        <Form.Item label="链接地址" name="link" required>
          <Input placeholder="填入URL链接" />
        </Form.Item>
        <Form.Item label="首页焦点大图(可选)" name="coverImage">
          <Input placeholder="填入URL图片链接" />
        </Form.Item>
        <Form.Item label="是否显示在首页(可选)" name="isHomeNews" valuePropName="checked">
          <Switch />
        </Form.Item>
        <Form.Item label="是否设置为焦点新闻(可选)" name="isfocusNews" valuePropName="checked">
          <Switch />
        </Form.Item>
        <Form.Item label="新闻时间" name="time" required>
          <DatePicker />
        </Form.Item>
        <Form.Item wrapperCol={{ span: 16, offset: 8 }}>
          <Button type="primary" htmlType="submit">
            创建
          </Button>
        </Form.Item>
      </Form>
    </Layout>
  )
}

export default App
