import { Table, Modal, Input, Button } from "antd"
import { Text, Flex } from "@mantine/core"
import type { ColumnsType } from "antd/es/table"
import type { TableRowSelection } from "antd/es/table/interface"
import React, { useState, useEffect } from "react"
import { useMutation, useQuery } from "@blitzjs/rpc"
import { Tag, Tabs, Select, Form, Switch, Radio, Image } from "antd"
import dayjs from "dayjs"
// import axios from "axios"
import getArticleSocial from "app/cms/mutations/getArticleSocial"
import getArticleNews from "app/cms/mutations/getArticleNews"
import getKols from "app/cms/mutations/getKols"
import getArticleSocialList from "app/cms/mutations/getArticleSocialList"
// import { useXinXuanArticleSocial } from "app/users/hooks/cms/useXinXuanArticleSocial"
import { Router, useRouter } from "next/router"
import { invoke } from "@blitzjs/rpc"
import getToken from "app/users/queries/getToken"
import { showNotification } from "@mantine/notifications"
import deleteArticleNews from "app/cms/mutations/deleteArticleNews"
import SectionHeadings from "app/components/SectionHeadings"
// import { buffer } from "stream/consumers"
const { confirm } = Modal
const { Option } = Select

function getRandomName() {
  return "cms/xinxuan-website"
}

function uploadImages(file, uploadConfig) {
  let domain = "https://s.xinc818.com/"
  console.log("uploadConfig~~~", uploadConfig)

  return new Promise((resolve, reject) => {
    var xhr = new XMLHttpRequest()
    var formData = new FormData()
    Object.keys(uploadConfig).forEach((key) => {
      formData.append(key, uploadConfig[key])
    })
    const fileName = file.name
    // let uploadName = `${getRandomName() + fileName.substr(fileName.lastIndexOf('.'))}`;
    let uploadName = `${getRandomName()}/${fileName}`
    // if (elChecked.checked) {
    //   uploadName = fileName;
    // }
    const key = `${uploadConfig.dirPath}${uploadName}`
    formData.append("key", key)
    formData.append("x-oss-content-type", "text/javascript")
    // formData.append("content-encoding", "UTF-8")
    // formData.append("text/html", "charset=utf-8")
    formData.append("file", file.file)
    xhr.open("POST", uploadConfig.host)
    xhr.send(formData)
    xhr.onreadystatechange = function () {
      if (xhr.readyState == 4 && xhr.status >= 200 && xhr.status < 300) {
        resolve([domain + key, true])
      }
      if (xhr.status > 300) {
        resolve([false, false])
        xhr.abort()
      }
    }
    xhr.timeout = 10000
    xhr.ontimeout = (event) => {
      resolve([false, false])
    }
  })
}

const App: React.FC = () => {
  const [form] = Form.useForm()
  const [data, setData] = useState([]) as any
  const [news, setNews] = useState([]) as any
  const [kols, setKols] = useState([]) as any
  const router = useRouter()
  const [getArticleSocialMutation] = useMutation(getArticleSocial, {})
  const [getArticleNewsMutation] = useMutation(getArticleNews, {})
  const [getArticleSocialListMutation] = useMutation(getArticleSocialList, {})
  const [deleteArticleNewsMutation] = useMutation(deleteArticleNews, {})

  const [group, setGroup] = useState("")

  const getTableData = async () => {
    const res = await getArticleNewsMutation({})
    console.log("res", res)
    setData(res.entry || [])
  }
  useEffect(() => {
    void getTableData()
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [])

  const [token, setToken] = useState(null)

  useEffect(() => {
    const getTokenHandle = async () => {
      const token = (await invoke(getToken, {})) as any
      setToken(token)
    }
    void getTokenHandle()
    //
  }, [])

  const columns: ColumnsType<any> = [
    {
      title: "id",
      width: "20px",
      dataIndex: "id",
    },
    {
      title: "封面图",
      width: "80px",
      dataIndex: "thumb",
      render(value, record, index) {
        // eslint-disable-next-line jsx-a11y/alt-text
        return <Image src={value} />
      },
    },
    {
      title: "标题",
      width: "160px",
      dataIndex: "title",
      render(value, record, index) {
        return <Text lineClamp={2}>{value}</Text>
      },
    },
    {
      title: "标题(英文)",
      width: "160px",
      dataIndex: "title_en",
      render(value, record, index) {
        return <Text lineClamp={2}>{value}</Text>
      },
    },
    {
      title: "新闻日期",
      dataIndex: "timeStr",
      width: "100px",
    },
    {
      title: "来源",
      dataIndex: "from",
      width: "100px",
    },
    {
      title: "来源(英文)",
      dataIndex: "from_en",
      width: "100px",
    },
    {
      title: "操作",
      dataIndex: "options",
      width: "100px",
      render(value, record, index) {
        return (
          <>
            <Button
              type="link"
              onClick={() => {
                void router.push({
                  pathname: "/cms/xinxuan/news/form",
                  query: { id: record.id },
                })
              }}
            >
              编辑
            </Button>
            <Button
              type="link"
              onClick={() => {
                window.open("https://xinxuan-dev.xinc818.com/new-detail/" + record.id)
                // void router.push({
                //   pathname: "/cms/xinxuan/social/form",
                //   query: { id: record.id },
                // })
              }}
            >
              预览
            </Button>
            <Button
              type="link"
              onClick={() => {
                confirm({
                  title: "温馨提示!!",
                  content: "是否删除数据",
                  onOk: async () => {
                    showNotification({
                      title: "notification",
                      message: "正在操作",
                    })
                    const status = await deleteArticleNewsMutation({
                      id: record.id,
                    })
                    showNotification({
                      title: "notification",
                      message: "删除数据成功",
                    })
                    form.submit()
                    showNotification({
                      title: "notification",
                      message: "正在同步测试数据..",
                    })
                    let json = await getArticleSocialListMutation({
                      env: "dev",
                    })
                    let jsonStr = JSON.stringify(json.entry)
                    await uploadImages({ name: "data_dev.js", file: Buffer.from(jsonStr) }, token)
                    showNotification({
                      title: "notification",
                      message: "测试数据同步完成。",
                    })
                  },
                  onCancel() {
                    // console.log("Cancel")
                  },
                })
              }}
            >
              删除
            </Button>
          </>
        )
      },
    },
  ]

  const onFinish = async (values: any) => {
    const result = await getArticleNewsMutation(values)
    setData(result.entry)
  }

  const onReset = async () => {
    form.resetFields()
    const result = await getArticleNewsMutation({})
    setData(result.entry)
  }

  return (
    <>
      <SectionHeadings
        name="新闻列表"
        btnText="新增新闻"
        onClick={() => {
          void router.push({
            pathname: "/cms/xinxuan/news/form",
          })
        }}
      />

      <Form
        layout="inline"
        initialValues={{
          id: "",
          title: "",
          title_en: "",
        }}
        className="mb-5"
        form={form}
        name="control-hooks"
        onFinish={onFinish}
      >
        <Form.Item name="id" label="id" rules={[{ required: false }]}>
          <Input />
        </Form.Item>
        <Form.Item name="title" label="中文标题" rules={[{ required: false }]}>
          <Input />
        </Form.Item>
        <Form.Item name="title_en" label="英文标题" rules={[{ required: false }]}>
          <Input />
        </Form.Item>
        <Form.Item>
          <Button type="primary" htmlType="submit" style={{ marginRight: "10px" }}>
            查询
          </Button>
          <Button htmlType="button" onClick={onReset}>
            重置
          </Button>
          {/* <Button
            style={{
              marginLeft: "10px",
            }}
            onClick={() => {
              void router.push({
                pathname: "/cms/xinxuan/news/form",
              })
            }}
          >
            新增新闻
          </Button> */}
        </Form.Item>
      </Form>
      <Table
        columns={columns}
        pagination={{ pageSize: 100 }}
        dataSource={data || []}
        scroll={{ x: "max-content" }}
      />
    </>
  )
}

export default App
