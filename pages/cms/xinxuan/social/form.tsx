/* eslint-disable @next/next/no-img-element */
import {
  <PERSON><PERSON>,
  <PERSON>r,
  DatePicker,
  Form,
  Input,
  InputNumber,
  Radio,
  Select,
  Upload,
  Switch,
  TreeSelect,
  message,
} from "antd"
import React, { useState, useRef, useEffect } from "react"
import { PlusOutlined } from "@ant-design/icons"
const { Option } = Select
import { Breadcrumbs, Anchor } from "@mantine/core"
import Layout from "pages/layouts/Layout"
import { Router, useRouter } from "next/router"
import { invalidateQuery, useMutation, useQuery } from "@blitzjs/rpc"
import submitArticleSocial from "app/cms/mutations/submitArticleSocial"
import getArticleSocialItem from "app/cms/mutations/getArticleSocialItem"
import { upload } from "../../../../utils/aliyun"
// import type { FormInstance } from "antd/es/form"
import dayjs from "dayjs"
import weekdays from "dayjs/plugin/weekday"
import localeData from "dayjs/plugin/localeData"
import { showNotification } from "@mantine/notifications"
import { invoke } from "@blitzjs/rpc"
import getToken from "app/users/queries/getToken"
import getArticleSocialList from "app/cms/mutations/getArticleSocialList"
// import { useParam } from "@blitzjs/next"
dayjs.extend(weekdays)
dayjs.extend(localeData)

type SizeType = Parameters<typeof Form>[0]["size"]

function getRandomName() {
  return "cms/xinxuan-website"
}

function uploadImages(file, uploadConfig) {
  let domain = "https://s.xinc818.com/"
  console.log("uploadConfig~~~", uploadConfig)

  return new Promise((resolve, reject) => {
    var xhr = new XMLHttpRequest()
    var formData = new FormData()
    Object.keys(uploadConfig).forEach((key) => {
      formData.append(key, uploadConfig[key])
    })
    const fileName = file.name
    // let uploadName = `${getRandomName() + fileName.substr(fileName.lastIndexOf('.'))}`;
    let uploadName = `${getRandomName()}/${fileName}`
    // if (elChecked.checked) {
    //   uploadName = fileName;
    // }
    const key = `${uploadConfig.dirPath}${uploadName}`
    formData.append("key", key)
    formData.append("x-oss-content-type", "text/javascript")
    // formData.append("content-encoding", "UTF-8")
    // formData.append("text/html", "charset=utf-8")
    formData.append("file", file.file)
    xhr.open("POST", uploadConfig.host)
    xhr.send(formData)
    xhr.onreadystatechange = function () {
      if (xhr.readyState == 4 && xhr.status >= 200 && xhr.status < 300) {
        resolve([domain + key, true])
      }
      if (xhr.status > 300) {
        resolve([false, false])
        xhr.abort()
      }
    }
    xhr.timeout = 10000
    xhr.ontimeout = (event) => {
      resolve([false, false])
    }
  })
}

const App: React.FC = (props) => {
  //   const [componentSize, setComponentSize] = useState<SizeType | "default">("default")
  const router = useRouter()
  const [form] = Form.useForm()
  const [token, setToken] = useState(null)
  const formRef = React.createRef<any>()
  const [thumb, setThumb] = useState("")
  const postId = router.query.id as any
  console.log("postId", postId)
  const [param, setParam] = useState(null)
  const [submitArticleSocialMutation] = useMutation(submitArticleSocial, {})
  const [getArticleSocialItemMutation] = useMutation(getArticleSocialItem, {})
  const [getArticleSocialListMutation] = useMutation(getArticleSocialList, {})

  const items = [{ title: "辛选", href: "/cms/xinxuan" }, { title: "编辑" }].map((item, index) => (
    <Anchor href={item.href} key={index}>
      {item.title}
    </Anchor>
  ))

  const initData = async () => {
    if (postId) {
      const result = await getArticleSocialItemMutation({
        id: parseInt(postId),
      })
      console.log("resultresultresult", result)
      if (result.status) {
        let data = result.entry as any
        setTimeout(() => {
          setThumb(data.thumb)
          form.setFieldsValue({
            thumb: data.thumb,
            content: data.content,
            content_en: data.content_en,
            time: dayjs(data.time),
            group: data.group,
            isIgnoreDay: data.isIgnoreDay,
            sortNum: data.sortNum,
          })
        }, 100)
      }
    }
  }

  useEffect(() => {
    const getTokenHandle = async () => {
      const token = (await invoke(getToken, {})) as any
      setToken(token)
    }
    void getTokenHandle()
    //
  }, [])

  useEffect(() => {
    void initData()
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [postId])

  const onFormLayoutChange = (values) => {
    // console.log("values", values)
    // setComponentSize(size)
  }

  const onFinish = async (values: any) => {
    const params = {
      ...values,
      thumb: thumb,
      id: postId || "",
      // thumb: "https://s.xinc818.com/files/webcil1nbaofklvbsk3/news1_1.png",
      time: values.time.format("YYYY-MM-DD"),
    }
    console.log("values", params)
    if (!thumb) {
      void message.error("请上传图片!")
      return false
    }
    const result = await submitArticleSocialMutation(params)
    console.log("result~~", result)
    if (result.status == true) {
      void message.success("保存成功")

      showNotification({
        title: "notification",
        message: "正在同步日常数据..",
      })
      let json = await getArticleSocialListMutation({
        env: "dev",
      })
      json.entry.env = "dev"
      let jsonStr = JSON.stringify(json.entry)
      const [result, status] = (await uploadImages(
        { name: "data_dev.js", file: Buffer.from(jsonStr) },
        token
      )) as any
      showNotification({
        title: "notification",
        message: "同步数据成功! 🤥",
      })

      setTimeout(() => {
        formRef.current?.resetFields()
        router.back()
      }, 250)
    }
  }

  const uploadButton = (
    <div>
      <PlusOutlined />
      <div style={{ marginTop: 8 }}>Upload</div>
    </div>
  )

  return (
    <Layout title="social-form" {...props}>
      <Button
        onClick={() => {
          router.back()
        }}
      >
        返回
      </Button>
      <Form
        labelCol={{ span: 4 }}
        wrapperCol={{ span: 12 }}
        ref={formRef}
        form={form}
        layout="horizontal"
        initialValues={{
          type: "csjz",
          content: "",
          content_en: "",
          time: "",
          sortNum: "",
          isIgnoreDay: false,
        }}
        onFinish={onFinish}
        onValuesChange={onFormLayoutChange}
        //   size={componentSize as SizeType}
      >
        <Form.Item label="封面图" name="thumb">
          <Upload
            maxCount={1}
            listType="picture-card"
            className="avatar-uploader"
            showUploadList={false}
            accept="image/png, image/jpeg"
            customRequest={async (options: any) => {
              let result = (await upload(options.file, () => {})) as any
              setThumb(result)
              // let tmp = {}
              // tmp[`thumb`] = result
              // console.log("resultresultresult", result)
              // formRef.current!.setFieldsValue(result)
            }}
          >
            <div>
              {thumb ? <img src={thumb} alt="avatar" style={{ width: "100%" }} /> : uploadButton}
            </div>
          </Upload>
        </Form.Item>
        {/* <Form.Item label="直连URL图片" name="image_url">
          <Input placeholder="填入URL地址" />
        </Form.Item> */}
        <Form.Item label="社会公益-板块" name="group">
          <Select>
            <Select.Option value="csjz">慈善救灾</Select.Option>
            <Select.Option value="xhzx">辛火助学</Select.Option>
            <Select.Option value="xhjh">辛火计划</Select.Option>
            <Select.Option value="xczx">乡村振兴</Select.Option>
            <Select.Option value="kyjz">抗疫捐赠</Select.Option>
            <Select.Option value="qtgy">其他公益</Select.Option>
          </Select>
        </Form.Item>
        <Form.Item label="内容" name="content">
          <Input.TextArea placeholder="填入中文内容" />
        </Form.Item>
        <Form.Item label="内容(英文)" name="content_en">
          <Input.TextArea placeholder="填入英文内容" />
        </Form.Item>
        <Form.Item label="新闻时间" name="time">
          <DatePicker />
        </Form.Item>
        <Form.Item label="是否只精确到月" name="isIgnoreDay" valuePropName="checked">
          <Switch />
        </Form.Item>
        <Form.Item label="置顶排序" name="sortNum">
          <Input placeholder="输入数字" />
        </Form.Item>
        <Form.Item wrapperCol={{ span: 16, offset: 8 }}>
          <Button type="primary" htmlType="submit">
            保存
          </Button>
        </Form.Item>
      </Form>
    </Layout>
  )
}

export default App
