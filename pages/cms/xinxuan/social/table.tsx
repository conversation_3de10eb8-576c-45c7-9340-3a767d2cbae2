import { Table, Modal, Input, Button } from "antd"
import { Text, Flex } from "@mantine/core"
import type { ColumnsType } from "antd/es/table"
import type { TableRowSelection } from "antd/es/table/interface"
import React, { useState, useEffect } from "react"
import { useMutation, useQuery } from "@blitzjs/rpc"
import { Tag, Tabs, Select, Form, Switch, Radio, Image } from "antd"
import { showNotification } from "@mantine/notifications"
import { IconX } from "@tabler/icons"
import { driver } from "driver.js";
import dayjs from "dayjs"
// import axios from "axios"
import getArticleSocial from "app/cms/mutations/getArticleSocial"
import getArticleNews from "app/cms/mutations/getArticleNews"
import getKols from "app/cms/mutations/getKols"
import getArticleSocialList from "app/cms/mutations/getArticleSocialList"
import getNewsUpdate from "app/cms/mutations/getNewsUpdate"
import deleteArticleSocial from "app/cms/mutations/deleteArticleSocial"
import getAuth from "app/cms/mutations/getPublishAuth"
// import { useXinXuanArticleSocial } from "app/users/hooks/cms/useXinXuanArticleSocial"
import { Router, useRouter } from "next/router"
import { invoke } from "@blitzjs/rpc"
import getToken from "app/users/queries/getToken"
import SectionHeadings from "app/components/SectionHeadings"
// import { buffer } from "stream/consumers"
const { confirm } = Modal
const { Option } = Select

import "driver.js/dist/driver.css";

interface DataType {
  key: React.Key
  name: string
  age: number
  address: string
}

function getRandomName() {
  return "cms/xinxuan-website"
}

function uploadImages(file, uploadConfig) {
  let domain = "https://s.xinc818.com/"
  console.log("uploadConfig~~~", uploadConfig)

  return new Promise((resolve, reject) => {
    var xhr = new XMLHttpRequest()
    var formData = new FormData()
    Object.keys(uploadConfig).forEach((key) => {
      formData.append(key, uploadConfig[key])
    })
    const fileName = file.name
    // let uploadName = `${getRandomName() + fileName.substr(fileName.lastIndexOf('.'))}`;
    let uploadName = `${getRandomName()}/${fileName}`
    // if (elChecked.checked) {
    //   uploadName = fileName;
    // }
    const key = `${uploadConfig.dirPath}${uploadName}`
    formData.append("key", key)
    formData.append("x-oss-content-type", "text/javascript")
    // formData.append("content-encoding", "UTF-8")
    // formData.append("text/html", "charset=utf-8")
    formData.append("file", file.file)
    xhr.open("POST", uploadConfig.host)
    xhr.send(formData)
    xhr.onreadystatechange = function () {
      if (xhr.readyState == 4 && xhr.status >= 200 && xhr.status < 300) {
        resolve([domain + key, true])
      }
      if (xhr.status > 300) {
        resolve([false, false])
        xhr.abort()
      }
    }
    xhr.timeout = 10000
    xhr.ontimeout = (event) => {
      resolve([false, false])
    }
  })
}

const App: React.FC = () => {
  const [form] = Form.useForm()

  const onGenderChange = (value: string) => {
    switch (value) {
      case "male":
        form.setFieldsValue({ note: "Hi, man!" })
        return
      case "female":
        form.setFieldsValue({ note: "Hi, lady!" })
        return
      case "other":
        form.setFieldsValue({ note: "Hi there!" })
        break
      default:
    }
  }
  const [data, setData] = useState([]) as any
  const [news, setNews] = useState([]) as any
  const [kols, setKols] = useState([]) as any
  const router = useRouter()
  const [getArticleSocialMutation] = useMutation(getArticleSocial, {})
  const [getArticleNewsMutation] = useMutation(getArticleNews, {})
  const [getArticleSocialListMutation] = useMutation(getArticleSocialList, {})
  const [getNewsUpdateMutation] = useMutation(getNewsUpdate, {})
  const [deleteArticleSocialMutation] = useMutation(deleteArticleSocial, {})
  const [getPublishAuthMutation] = useMutation(getAuth, {})

  const [group, setGroup] = useState("")

  const getTableData = async () => {
    const res = await getArticleSocialMutation({})
    console.log("res", res)
    setData(res.entry || [])
  }
  const viewJsonContent = async () => {
    let json = await getArticleSocialListMutation()
    console.log("jsonjson", json)
    return json
    // console.log("viewJsonContent1")
    // const data = await axios.get(
    //   "https://s.xinc818.com/files/cms/xinxuan-website/data.js?t=" + Date.now(),
    //   {}
    // )
    // console.log("viewJsonContent2", data)
  }
  useEffect(() => {
    void getTableData()
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [])

  const [token, setToken] = useState(null)

  useEffect(() => {
    const getTokenHandle = async () => {
      const token = (await invoke(getToken, {})) as any
      setToken(token)
    }
    void getTokenHandle()
    //
  }, [])

  const columns: ColumnsType<any> = [
    {
      title: "id",
      width: "20px",
      dataIndex: "id",
    },
    {
      title: "封面图",
      width: "60px",
      dataIndex: "thumb",
      render(value, record, index) {
        // eslint-disable-next-line jsx-a11y/alt-text
        return <Image src={value} />
      },
    },
    {
      title: "内容",
      width: "140px",
      dataIndex: "content",
      render(value, record, index) {
        return <Text lineClamp={3}>{value}</Text>
      },
    },
    {
      title: "内容(英文)",
      width: "140px",
      dataIndex: "content_en",
      render(value, record, index) {
        return <Text lineClamp={3}>{value}</Text>
      },
    },
    {
      title: "类目",
      width: "60px",
      dataIndex: "group",
      render(value, record, index) {
        const data = {
          csjz: "慈善救灾",
          xhzx: "辛火助学",
          xhjh: "辛火计划",
          xczx: "乡村振兴",
          kyjz: "抗疫捐赠",
          qtgy: "其他公益",
        }
        return data[value] || "/"
      },
    },
    {
      title: "新闻日期",
      dataIndex: "timeStr",
      width: "60px",
      render(value, record, index) {
        if (record.isIgnoreDay) {
          return dayjs(value).format("YYYY年MM月")
        } else {
          return value
        }
      },
      // sorter: {
      //   compare: (a, b) => +new Date(a.timeStr) - +new Date(b.timeStr),
      //   multiple: 3,
      // },
    },
    {
      title: "排序值",
      width: "60px",
      dataIndex: "sortNum",
      render(value, record, index) {
        return value || "默认"
      },
    },
    {
      title: "操作",
      dataIndex: "options",
      width: "100px",
      render(value, record, index) {
        return (
          <>
            <Button
              type="link"
              onClick={() => {
                void router.push({
                  pathname: "/cms/xinxuan/social/form",
                  query: { id: record.id },
                })
              }}
            >
              编辑
            </Button>
            <Button
              type="link"
              onClick={() => {
                confirm({
                  title: "温馨提示!!",
                  content: "是否删除数据",
                  onOk: async () => {
                    const res = await deleteArticleSocialMutation({
                      id: record.id,
                    })
                    if (res.status) {
                      showNotification({
                        title: "notification",
                        message: "删除数据成功",
                      })
                    } else {
                      showNotification({
                        title: "notification",
                        message: "删除数据失败",
                      })
                    }
                    form.submit()
                    showNotification({
                      title: "notification",
                      message: "正在同步测试数据..",
                    })
                    let json = await getArticleSocialListMutation({
                      env: "dev",
                    })
                    let jsonStr = JSON.stringify(json.entry)
                    await uploadImages({ name: "data_dev.js", file: Buffer.from(jsonStr) }, token)
                    showNotification({
                      title: "notification",
                      message: "测试数据同步完成。",
                    })
                  },
                  onCancel() {
                    // console.log("Cancel")
                  },
                })
              }}
            >
              删除
            </Button>
          </>
        )
      },
    },
  ]

  const onFinish = async (values: any) => {
    console.log(values)
    const result = await getArticleSocialMutation({
      id: values.id,
      content: values.content,
      group: values.group,
    })
    setData(result.entry)
  }

  const onReset = async () => {
    form.resetFields()
    const result = await getArticleSocialMutation({})
    setData(result.entry)
  }

  return (
    <>
      <SectionHeadings
        name="社会公益"
        btnText="新增文章"
        onClick={() => {
          void router.push({
            pathname: "/cms/xinxuan/social/form",
          })
        }}
        // eslint-disable-next-line react/no-children-prop
        children={(
          <Button className="mr-5" onClick={() => {
            const driverObj = driver({
              showProgress: true,
              steps: [
                { element: 'button:nth-child(2)', popover: { title: '新增文章', description: '在这里，您可以添加官网新闻。', side: "left", align: 'start' } },
                { element: '#control-hooks', popover: { title: '查询文章', description: '个性化查询你想要的文章', side: "bottom", align: 'start' } },
                { element: '.ant-btn.css-dev-only-do-not-override-8z6d52.ant-btn-link', popover: { title: '编辑你的文章', description: '时刻编辑你的新闻内容', side: "bottom", align: 'start' } },
                { element: '.ant-form-item-control-input-content > button:nth-child(3)', popover: { title: '预览你的网站', description: '访问测试环境，查看你修改的数据是否生效', side: "left", align: 'start' } },
                { popover: { title: '🎉🎉🎉', description: '开始向您的官网添加数据吧，在测试环境验证完新闻数据正确后，请联系【子涯】发布' } }
              ]
            });

            driverObj.drive();
          }}>
            点我学习教程
          </Button>
        )}
      />
      {/* <Flex gap="md" justify="flex-end" align="flex-start" direction="row" wrap="wrap"></Flex> */}
      <Form
        layout="inline"
        className="mb-5"
        initialValues={{
          id: "",
          content: "",
          group: "",
        }}
        form={form}
        name="control-hooks"
        onFinish={onFinish}
      >
        <Form.Item name="id" label="id" rules={[{ required: false }]}>
          <Input />
        </Form.Item>
        <Form.Item name="content" label="内容" rules={[{ required: false }]}>
          <Input />
        </Form.Item>
        <Form.Item name="group" label="类目" rules={[{ required: false }]}>
          <Select placeholder="选择类目" allowClear>
            <Option value="">所有类目</Option>
            <Option value="csjz">慈善救灾</Option>
            <Option value="xhzx">辛火助学</Option>
            <Option value="xhjh">辛火计划</Option>
            <Option value="xczx">乡村振兴</Option>
            <Option value="kyjz">抗议捐赠</Option>
          </Select>
        </Form.Item>
        <Form.Item>
          <Button type="primary" htmlType="submit" style={{ marginRight: "10px" }}>
            查询
          </Button>
          <Button htmlType="button" onClick={onReset} style={{ marginRight: "10px" }}>
            重置
          </Button>
          <Button
            htmlType="button"
            style={{ marginRight: "10px" }}
            onClick={() => {
              window.open("https://xinxuan-dev.xinc818.com/social/commitment")
            }}
          >
            访问测试环境
          </Button>
          {/* <Button
            style={{
              marginRight: "10px",
            }}
            onClick={async () => {
              void router.push({
                pathname: "/cms/xinxuan/social/form",
              })
              // showNotification({
              //   title: "notification",
              //   message: "正在同步日常数据..",
              // })
              // let json = await getArticleSocialListMutation({
              //   env: "dev",
              // })
              // json.entry.env = "dev"
              // let jsonStr = JSON.stringify(json.entry)
              // const [result, status] = (await uploadImages(
              //   { name: "data_dev.js", file: Buffer.from(jsonStr) },
              //   token
              // )) as any
              // showNotification({
              //   title: "notification",
              //   message: "同步数据成功! 🤥",
              // })
            }}
          >
            新增文章
          </Button> */}
          <Button
            style={{
              marginRight: "10px",
            }}
            onClick={async () => {
              confirm({
                title: "温馨提示!!",
                content: "重要操作! 确定部署到线上???",
                onOk: async () => {
                  showNotification({
                    title: "notification",
                    message: "正在同步线上数据..",
                  })
                  let authRes = await getPublishAuthMutation({})
                  if (authRes.status == false) {
                    showNotification({
                      title: "notification",
                      icon: <IconX />,
                      color: "red",
                      message: "没有线上发布权限，请联系管理发布",
                    })
                    return false
                  }
                  let json = await getArticleSocialListMutation({
                    env: "prod",
                  })
                  let jsonStr = JSON.stringify(json.entry)
                  const [result, status] = (await uploadImages(
                    { name: "data.js", file: Buffer.from(jsonStr) },
                    token
                  )) as any

                  let newsUpdates = await getNewsUpdateMutation()
                  const [newsResult, newsStatus] = (await uploadImages(
                    { name: "news_updates.js", file: Buffer.from(JSON.stringify(newsUpdates.entry)) },
                    token
                  )) as any

                  showNotification({
                    title: "notification",
                    message: "线上数据同步完成。",
                  })
                },
                onCancel() {
                  // console.log("Cancel")
                },
              })
            }}
          >
            上线
          </Button>
        </Form.Item>
      </Form>
      <Table
        columns={columns}
        pagination={{ pageSize: 50 }}
        dataSource={data || []}
        scroll={{ x: "max-content" }}
      />
    </>
  )
}

export default App
