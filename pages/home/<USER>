import { useEffect, useState } from "react"
import { Routes } from "@blitzjs/next"
import { Group, Box, Collapse, ThemeIcon, Text, UnstyledButton, createStyles } from "@mantine/core"
import { TablerIcon, IconCalendarStats, IconChevronLeft, IconChevronRight } from "@tabler/icons"
import { Router, useRouter } from "next/router"

const useStyles = createStyles((theme, _params, getRef) => {
  const icon = getRef("icon")

  return {
    control: {
      fontWeight: 500,
      display: "block",
      width: "100%",
      padding: `${theme.spacing.xs}px ${theme.spacing.md}px`,
      color: theme.colorScheme === "dark" ? theme.colors.dark[0] : theme.black,
      fontSize: theme.fontSizes.sm,

      "&:hover": {
        backgroundColor: theme.colorScheme === "dark" ? theme.colors.dark[7] : theme.colors.gray[0],
        color: theme.colorScheme === "dark" ? theme.white : theme.black,
      },
    },

    controlActive: {
      backgroundColor: theme.colorScheme === "dark" ? theme.colors.dark[7] : theme.colors.gray[0],
      color: theme.colorScheme === "dark" ? theme.white : theme.black,
    },

    link: {
      fontWeight: 500,
      display: "block",
      textDecoration: "none",
      padding: `${theme.spacing.xs}px ${theme.spacing.md}px`,
      paddingLeft: 31,
      marginLeft: 30,
      fontSize: theme.fontSizes.sm,
      color: theme.colorScheme === "dark" ? theme.colors.dark[0] : theme.colors.gray[7],
      borderLeft: `1px solid ${theme.colorScheme === "dark" ? theme.colors.dark[4] : theme.colors.gray[3]
        }`,

      "&:hover": {
        backgroundColor: theme.colorScheme === "dark" ? theme.colors.dark[7] : theme.colors.gray[0],
        color: theme.colorScheme === "dark" ? theme.white : theme.black,
      },
    },

    chevron: {
      transition: "transform 200ms ease",
    },

    linkActive: {
      "&, &:hover": {
        backgroundColor: theme.fn.variant({ variant: "light", color: theme.primaryColor })
          .background,
        color: theme.fn.variant({ variant: "light", color: theme.primaryColor }).color,
        [`& .${icon}`]: {
          color: theme.fn.variant({ variant: "light", color: theme.primaryColor }).color,
        },
      },
    },
  }
})
interface LinksGroupProps {
  icon: TablerIcon
  label: string
  initiallyOpened?: boolean
  link?: string
  currentAuth?: any
  pageName?: string
  links?: { label: string; link: string }[]
}

export function LinksGroup({
  icon: Icon,
  label,
  currentAuth,
  initiallyOpened,
  links,
  link,
  pageName,
}: LinksGroupProps) {
  const router = useRouter()
  const isCurrentControl = (label) => {
    return label == router.pathname
  }

  const isCurrentGroup = (label) => {
    return label == router.pathname
  }

  const { classes, theme, cx } = useStyles()
  const hasLinks = Array.isArray(links)
  const [active, setActive] = useState("Billing")
  const [opened, setOpened] = useState(initiallyOpened || false)
  const ChevronIcon = theme.dir === "ltr" ? IconChevronRight : IconChevronLeft
  const items = (hasLinks ? links : []).map((link: any) => {
    console.log('fff', link.role);

    if (link.role && currentAuth?.indexOf(link.role) == -1) {
      return null
    }
    return (
      <Text<"a">
        component="a"
        className={cx(classes.link, { [classes.linkActive]: isCurrentGroup(link.link) })}
        //   className={classes.link}
        key={link.label}
        onClick={(event) => {
          void router.push({
            pathname: link.link,
          })
          // event.preventDefault()
        }}
      >
        {link.label}
      </Text>
    )
  })

  useEffect(() => {
    if (hasLinks) {
      links.map((link) => {
        if (isCurrentGroup(link.link)) {
          setOpened(true)
        }
      })
    }
  }, [])

  return (
    <>
      <UnstyledButton
        onClick={() => setOpened((o) => !o)}
        className={cx(classes.control, { [classes.controlActive]: isCurrentControl(link) })}
      // className={classes.control}
      >
        <Group
          position="apart"
          spacing={0}
          onClick={() => {
            if (!hasLinks) {
              void router.push({
                pathname: link,
              })
              // void router.push(
              //   {
              //     pathname: link,
              //   },
              //   {},
              //   { shallow: true }
              // )
            }
          }}
        >
          <Box sx={{ display: "flex", alignItems: "center" }}>
            <ThemeIcon variant="light" size={30}>
              <Icon size={18} />
            </ThemeIcon>
            <Box ml="md">{label}</Box>
          </Box>
          {hasLinks && (
            <ChevronIcon
              className={classes.chevron}
              size={14}
              stroke={1.5}
              style={{
                transform: opened ? `rotate(${theme.dir === "rtl" ? -90 : 90}deg)` : "none",
              }}
            />
          )}
        </Group>
      </UnstyledButton>
      {hasLinks ? <Collapse in={opened}>{items}</Collapse> : null}
    </>
  )
}

const mockdata = {
  label: "Releases",
  icon: IconCalendarStats,
  links: [
    { label: "Upcoming releases", link: "/" },
    { label: "Previous releases", link: "/" },
    { label: "Releases schedule", link: "/" },
  ],
}

export function NavbarLinksGroup() {
  return (
    <Box
      sx={(theme) => ({
        minHeight: 220,
        padding: theme.spacing.md,
        backgroundColor: theme.colorScheme === "dark" ? theme.colors.dark[6] : theme.white,
      })}
    >
      <LinksGroup {...mockdata} />
    </Box>
  )
}

export default function FeaturesCards() {
  return <></>
}
