import "@/styles/global.css"
import { ErrorFallbackProps, ErrorComponent, ErrorBoundary, AppProps } from "@blitzjs/next"
import { MantineProvider, ColorSchemeProvider, ColorScheme } from "@mantine/core"
import { ModalsProvider } from "@mantine/modals"
import { useMutation } from "@blitzjs/rpc"
import { AuthenticationError, AuthorizationError } from "blitz"
import getAuth from "app/users/mutations/getAuthRoles"
import { Router, useRouter } from "next/router"
import React, { useState, useEffect } from "react"
import { withBlitz } from "app/blitz-client"
import { NotificationsProvider } from "@mantine/notifications"
import * as mdxComponents from "@/components/mdx"
import { MDXProvider } from "@mdx-js/react"
import { Layout } from "components/Layout"

// import "antd/dist/antd.css"
import "dayjs/locale/zh-cn"
import zhCN from "antd/locale/zh_CN"
import "antd/dist/reset.css"
import "oocode/dist/style.css"


import dayjs from "dayjs"
import "dayjs/locale/zh-cn"
dayjs.locale("zh-cn")

function RootErrorFallback({ error }: ErrorFallbackProps) {
  if (error instanceof AuthenticationError) {
    return <div>Error: You are not authenticated</div>
  } else if (error instanceof AuthorizationError) {
    return (
      <ErrorComponent
        statusCode={error.statusCode}
        title="Sorry, you are not authorized to access this"
      />
    )
  } else {
    return (
      <ErrorComponent
        statusCode={(error as any)?.statusCode || 400}
        title={error.message || error.name}
      />
    )
  }
}

function MyApp({ Component, pageProps }: AppProps) {
  const router = useRouter()
  const getLayout = Component.getLayout || ((page) => page)
  const [colorScheme, setColorScheme] = useState<ColorScheme>("light")
  const toggleColorScheme = (value?: ColorScheme) =>
    setColorScheme(value || (colorScheme === "dark" ? "light" : "dark"))

  const [getAuthMutation] = useMutation(getAuth, {})
  const [loaded, setLoaded] = useState(false)
  const [auth, setAuth] = useState("")
  const [userInfo, setUserInfo] = useState("")

  const excludedPaths = ["/auth/login", "/", "/changelog", "/lanlan/ux", '/docs'];


  useEffect(() => {
    const checkAuth = async () => {
      const res = (await getAuthMutation()) as any
      console.log("res", res)
      if (res && res.role) {
        setAuth(res.role || "")
        // try {
        //   localStorage.setItem("flow_role", res.role)
        //   localStorage.setItem("flow_username", res.name)
        // } catch (e) {}
        setUserInfo(res)
        localStorage.setItem("flow.userNickName", res.name)
        // pageProps.role = res.role
        // pageProps.username = res.name
      } else {
        console.log("router.pathname", router.pathname)
        if (!excludedPaths.includes(router.pathname)) {
          void router.replace({
            pathname: "/auth/login",
          })
        }
        // if (router.pathname != "" && router.pathname != "/" && router.pathname != "/changelog") {
        //   void router.replace({
        //     pathname: "/auth/login",
        //   })
        // }
      }
      setLoaded(true)
    }
    void checkAuth()
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [])

  if (!userInfo) {
    // if (router.pathname != "/auth/login" && router.pathname != "/" && router.pathname != "/changelog") {
    //   return null
    // }
    if (!excludedPaths.includes(router.pathname)) {
      return null;
    }
  }

  return (
    <ErrorBoundary FallbackComponent={RootErrorFallback}>
      <ColorSchemeProvider colorScheme={colorScheme} toggleColorScheme={toggleColorScheme}>
        <MantineProvider theme={{ colorScheme: colorScheme }} withGlobalStyles withNormalizeCSS>
          <ModalsProvider>
            <NotificationsProvider>
              {(router.pathname != "/changelog") ? (
                getLayout(<Component userInfo={userInfo} {...pageProps} />)
              ) : (
                <MDXProvider components={mdxComponents as any}>
                  <Layout>
                    <Component {...pageProps} />
                  </Layout>
                </MDXProvider>
              )}
            </NotificationsProvider>
          </ModalsProvider>
        </MantineProvider>
      </ColorSchemeProvider>
    </ErrorBoundary>
  )
}

export default withBlitz(MyApp)
