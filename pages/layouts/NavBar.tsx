import { useState, useEffect } from "react"
import { Navbar, Group, ScrollArea, createStyles, Badge } from "@mantine/core"
import { useRouter } from "next/router"
import {
  Cog6ToothIcon,
  CircleStackIcon,
  PencilSquareIcon,
  PhotoIcon,
  CloudIcon,
  FolderIcon,
  ShareIcon,
  HomeIcon,
  UsersIcon,
  PuzzlePieceIcon,
} from "@heroicons/react/24/outline"
import { ChevronDownIcon } from "@heroicons/react/20/solid"

const navigation = [
  {
    name: "工作台",
    href: "/home",
    icon: HomeIcon,
    current: true,
    role: ["FE_DEVELOPER"],
    children: [],
  },
  {
    name: "任务管理",
    href: "/task",
    role: ["FE_DEVELOPER"],
    icon: UsersIcon,
    current: false,
    children: [
      {
        name: "任务看板",
        role: ["FE_DEVELOPER"],
        href: "/task/calendar",
        current: false,
      },
      {
        name: "任务列表",
        role: ["FE_DEVELOPER"],
        href: "/task/list",
        current: false,
      },
      {
        name: "工作周报",
        role: ["FE_DEVELOPER"],
        href: "/task/weeklyReport",
        current: false,
      },
    ],
  },
  {
    name: "图片管理",
    href: "/upload",
    icon: PhotoIcon,
    role: ["FE_DEVELOPER", "DESIGNER", "USER"],
    current: false,
    children: [
      {
        name: "图片上传",
        role: ["FE_DEVELOPER", "DESIGNER", "USER"],
        href: "/upload/upload",
        current: false,
      },
      {
        name: "超级压缩",
        role: ["FE_DEVELOPER", "DESIGNER"],
        href: "/upload/compress",
        current: false,
      },
      {
        name: "历史记录",
        role: ["FE_DEVELOPER", "DESIGNER", "USER"],
        href: "/upload/history",
        current: false,
      },
    ],
  },
  {
    name: "内容管理",
    href: "/cms",
    role: ["XINXUAN_CMS", "FE_DEVELOPER"],
    icon: PencilSquareIcon,
    current: false,
    children: [
      {
        name: "辛选官网-社会公益",
        role: ["XINXUAN_CMS", "FE_DEVELOPER"],
        href: "/cms/xinxuan/social",
        current: false,
      },
      {
        name: "辛选官网-新闻",
        role: ["XINXUAN_CMS", "FE_DEVELOPER"],
        href: "/cms/xinxuan/news",
        current: false,
      },
      {
        name: "辛选官网-主播",
        role: ["XINXUAN_CMS", "FE_DEVELOPER"],
        href: "/cms/xinxuan/kols",
        current: false,
      },
      {
        name: "辛选招商-主播矩阵",
        role: ["XINXUAN_CMS", "FE_DEVELOPER"],
        href: "/cms/xinxuan818/kolMatrix",
        current: false,
      },
      {
        name: "辛选招商-主播战绩",
        role: ["XINXUAN_CMS", "FE_DEVELOPER"],
        href: "/cms/xinxuan818/kolRecord",
        current: false,
      }
    ],
  },
  {
    name: "构建部署",
    href: "/devops",
    icon: CircleStackIcon,
    role: ["FE_DEVELOPER"],
    current: false,
    children: [
      {
        name: "凯撒平台",
        role: ["FE_DEVELOPER"],
        href: "/devops/caesar",
        current: false,
      },
      {
        name: "盘古平台",
        role: ["FE_DEVELOPER"],
        href: "/devops/pangu",
        current: false,
      },
    ],
  },
  {
    name: "云服务",
    href: "/cloud",
    role: ["FE_DEVELOPER", "FE_BIGDATA"],
    icon: CloudIcon,
    current: false,
    children: [
      {
        name: (
          <Group spacing="xs" noWrap>
            <span>JSON配置</span>
          </Group>
        ),
        role: ["FE_DEVELOPER", "FE_BIGDATA"],
        href: "/cloud/json/list",
        current: false,
      },
      {
        name: "EasyH5",
        role: ["FE_DEVELOPER"],
        href: "/cloud/htmlEditor/h5-list",
        current: false,
      },
      {
        name: "Flow服务监控",
        role: ["FE_DEVELOPER"],
        href: "https://grafana.xinc818.com/dashboard/snapshot/GGPfqTFlfxiHCuNoi0OkXYA0268QL1F6?orgId=1",
        current: false,
      },
      {
        name: "前端代码搜索",
        role: ["FE_DEVELOPER"],
        href: "/cloud/search/frontend",
        current: false,
      },
      {
        name: "后端代码搜索",
        role: ["FE_DEVELOPER"],
        href: "/cloud/search/backend",
        current: false,
      }
    ],
  },
  {
    name: "物料管理",
    href: "/material",
    role: ["FE_DEVELOPER", "DESIGNER"],
    icon: PuzzlePieceIcon,
    current: false,
    children: [
      {
        name: (
          <Group spacing="xs" noWrap>
            <span>物料市场</span>
          </Group>
        ),
        role: ["FE_DEVELOPER", "DESIGNER"],
        href: "/material/market",
        current: false,
      },
      {
        name: "Xnpm私库",
        role: ["FE_DEVELOPER", "DESIGNER"],
        href: "http://npm.xinc818.com",
        current: false,
      },
      {
        name: "Xdesign设计资源上传",
        role: ["FE_DEVELOPER", "DESIGNER"],
        href: "/material/resources-upload",
        current: false,
      },
      {
        name: "CoDesign",
        role: ["FE_DEVELOPER", "DESIGNER"],
        href: "/material/codesign",
        current: false,
      },
    ],
  },
  {
    name: "项目沉淀",
    href: "/projects",
    icon: FolderIcon,
    role: ["FE_DEVELOPER"],
    current: false,
    children: [
      {
        name: (
          <Group spacing="xs" noWrap>
            <span>业务项目</span>
          </Group>
        ),
        role: ["FE_DEVELOPER"],
        href: "/projects/list",
        current: false,
      },
      {
        name: "业务数据看板",
        role: ["FE_DEVELOPER"],
        href: "https://track.xinxuan818.com/#/visualization/list",
        current: false
      },
    ],
  },
  {
    name: "技术分享",
    href: "/lanlan",
    icon: ShareIcon,
    role: ["FE_DEVELOPER"],
    current: false,
    children: [
      {
        name: "技术夜谈",
        role: ["FE_DEVELOPER"],
        href: "/lanlan/sharing-session",
        current: false,
      },
      {
        name: "团队共创",
        role: ["FE_DEVELOPER"],
        href: "/lanlan/team-planning",
        current: false,
      },
    ],
  },
  {
    name: "设置",
    href: "/settings",
    role: ["FE_DEVELOPER", "USER"],
    icon: Cog6ToothIcon,
    current: false,
    children: [
      {
        name: "个人设置",
        role: ["FE_DEVELOPER", "USER"],
        href: "/settings/userProfileSettings",
        current: false,
      },
    ],
  },
  // ... 其他菜单项
]

const teams = [
  { id: 2, name: "FE WIKI", href: "https://f2e.xinc818.com", initial: "W", current: false },
  { id: 3, name: "FE XDesign", href: "https://xdesign.xinc818.com", initial: "X", current: false },
  {
    id: 4,
    name: "团队空间",
    href: "https://wiki.xinc818.com/",
    initial: "S",
    current: false,
  },
]

function classNames(...classes) {
  return classes.filter(Boolean).join(" ")
}

const useStyles = createStyles((theme) => ({
  navbar: {
    height: `calc(100vh-59px)`,
    backgroundColor: theme.colorScheme === "dark" ? theme.colors.dark[6] : theme.white,
    paddingBottom: 0,
  },

  header: {
    padding: theme.spacing.md,
    paddingTop: 0,
    marginLeft: -theme.spacing.md,
    marginRight: -theme.spacing.md,
    color: theme.colorScheme === "dark" ? theme.white : theme.black,
    borderBottom: `1px solid ${theme.colorScheme === "dark" ? theme.colors.dark[4] : theme.colors.gray[3]
      }`,
  },

  links: {
    marginLeft: -theme.spacing.md,
    marginRight: -theme.spacing.md,
  },

  linksInner: {
    paddingTop: theme.spacing.xl,
    paddingBottom: theme.spacing.xl,
  },

  footer: {
    marginLeft: -theme.spacing.md,
    marginRight: -theme.spacing.md,
    borderTop: `1px solid ${theme.colorScheme === "dark" ? theme.colors.dark[4] : theme.colors.gray[3]
      }`,
  },
}))

function checkPermission(permissionStr, permissionArray) {
  if (typeof permissionArray === 'undefined') {
    return true;
  }
  const permissions = permissionStr.split(',');
  let flag = permissions.some(permission => permissionArray.includes(permission));
  return flag
}

export default function NavbarNested({ title, openState, userInfo }: any) {
  const { classes } = useStyles()
  const router = useRouter()
  const [expandedMenu, setExpandedMenu] = useState("")

  const checkCurrent = (item) => {
    if (item) {
      if (router.pathname.indexOf(item.href) !== -1) {
        return true
      }
    }
    return false
  }

  const checkSubItemCurrent = (item) => {
    console.log("router.pathname", router.pathname, item.href)
    return router.pathname == item.href
  }

  useEffect(() => {
    const pathArray = router.pathname.split("/")
    const firstLevelPath = pathArray[0]
    navigation.forEach((item: any) => {
      if (router.pathname.indexOf(item.href + "/") !== -1) {
        setExpandedMenu(item.name)
      }
    })
  }, [])

  return (
    <Navbar width={{ sm: 288 }} p="md" className={classes.navbar} style={{ display: openState ? "block" : "none" }}>
      <Navbar.Section grow className={classes.links} component={ScrollArea}>
        <div className="border-r bg-gray-900 lg:fixed lg:inset-y-0 lg:z-50 lg:flex lg:w-72 lg:flex-col lg:border-white/5">
          {/* Sidebar component, swap this element with another sidebar if you like */}
          <div className="flex grow flex-col gap-y-5 overflow-y-auto bg-gray-900 px-6 pb-4">
            <div className="flex h-16 shrink-0 cursor-pointer items-center">
              <img
                className="h-8 w-auto"
                onClick={() => {
                  void router.push({
                    pathname: "/",
                  })
                }}
                src="https://s.xinc818.com/files/webcim1ynyszfm6e33d/mark.svg"
                alt="Your Company"
              />
            </div>
            <nav className="flex flex-1 flex-col">
              <ul role="list" className="flex flex-1 flex-col gap-y-5">
                <li>
                  <ul role="list" className="-mx-2 space-y-1">
                    {navigation.map((item: any) => {
                      console.log("rolerolerolerole=====00", item.role, userInfo)
                      let flag = checkCurrent(item)
                      let hasAuth = false
                      item.role.forEach((o: any) => {
                        if (userInfo?.role?.indexOf(o) != -1) {
                          console.log("pk", userInfo?.role?.indexOf(o) != -1, o)
                          hasAuth = true
                        }
                      })
                      console.log("pk-hasAuth", item.role, hasAuth)
                      if (!hasAuth) return null
                      return (
                        <li key={item.name}>
                          <a
                            href={item.href}
                            onClick={(e) => {
                              e.preventDefault()
                              if (item.children.length == 0) {
                                void router.push({
                                  pathname: item.href,
                                })
                              }
                              setExpandedMenu(expandedMenu === item.name ? "" : item.name)
                            }}
                            className={classNames(
                              checkCurrent(item)
                                ? "bg-gray-800 text-white"
                                : "text-gray-400 hover:bg-gray-800 hover:text-white",
                              "group flex gap-x-3 rounded-md p-1.5 text-sm font-semibold leading-6"
                            )}
                          >
                            <span className="flex w-full items-center justify-between">
                              <span className="flex items-center">
                                <item.icon className="mr-5 h-6 w-6 shrink-0" aria-hidden="true" />
                                {item.name}
                              </span>
                              {item.children.length > 0 && (
                                <ChevronDownIcon
                                  className={`${expandedMenu === item.name ? "rotate-180 transform" : ""
                                    } h-5 w-5 transition-all duration-300`}
                                  aria-hidden="true"
                                />
                              )}
                            </span>
                          </a>
                          {item.children.length > 0 && expandedMenu === item.name && (
                            <ul className="ml-6 space-y-1">
                              {item.children.map((subItem) => {
                                if (!checkPermission(userInfo?.role, subItem.role)) {
                                  return null
                                }
                                return (
                                  <li key={subItem.name}>
                                    <a
                                      href={subItem.href}
                                      onClick={(e) => {
                                        e.preventDefault()
                                        if (subItem.href.indexOf("http") != -1) {
                                          window.open(subItem.href)
                                        } else {
                                          void router.push({
                                            pathname: subItem.href,
                                          })
                                        }
                                      }}
                                      className={classNames(
                                        checkSubItemCurrent(subItem)
                                          ? "text-gray-400 text-white"
                                          : "text-gray-400 hover:text-white",
                                        "group flex gap-x-3 rounded-md p-2 pl-7 text-sm font-semibold leading-6"
                                      )}
                                    >
                                      {subItem.name}
                                    </a>
                                  </li>
                                )
                              })}
                            </ul>
                          )}
                        </li>
                      )
                    })}
                  </ul>
                </li>
                <li>
                  <div className="text-xs font-semibold leading-6 text-gray-400">Your teams</div>
                  <ul role="list" className="-mx-2 mt-2 space-y-1">
                    {teams.map((team) => (
                      <li key={team.name}>
                        <a
                          href={team.href}
                          className={classNames(
                            team.current
                              ? "bg-gray-800 text-white"
                              : "text-gray-400 hover:bg-gray-800 hover:text-white",
                            "group flex gap-x-3 rounded-md p-2 text-sm font-semibold leading-6"
                          )}
                        >
                          <span className="flex h-6 w-6 shrink-0 items-center justify-center rounded-lg border border-gray-700 bg-gray-800 text-[0.625rem] font-medium text-gray-400 group-hover:text-white">
                            {team.initial}
                          </span>
                          <span className="truncate">{team.name}</span>
                        </a>
                      </li>
                    ))}
                  </ul>
                </li>
                <li className="-mx-6 mt-auto">
                  <a
                    href="#"
                    className="flex items-center gap-x-4 px-6 py-3 text-sm font-semibold leading-6 text-white hover:bg-gray-800"
                  >
                    <img
                      className="h-9 w-9 rounded-full bg-gray-800 avatar3d"
                      src={userInfo?.avatar}
                      alt=""
                    />
                    <span className="sr-only">Your profile</span>
                    <span aria-hidden="true">{userInfo?.nickName || userInfo?.email}</span>
                  </a>
                </li>
              </ul>
            </nav>
          </div>
        </div>
      </Navbar.Section>
    </Navbar>
  )
}
