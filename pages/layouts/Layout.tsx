import React from "react"
import { useState } from "react"
import { BlitzLayout } from "@blitzjs/next"
import NavBar from "./NavBar"
import { useRouter } from "next/router"
import { AppShell } from "@mantine/core"
import { useMantineTheme } from "@mantine/core"
import { motion, useScroll, useSpring } from "framer-motion"
import { MouseTrail } from "oocode"

import { ConfigProvider, Tooltip } from "antd"
import zhCN from 'antd/locale/zh_CN';

const Layout: BlitzLayout<any> = ({ title, fixed, userInfo, children, full, pageName }) => {
  const router = useRouter()
  const theme = useMantineTheme()
  const [open, setOpen] = useState(true);
  const [arrow, setArrow] = useState(0);
  const { scrollYProgress } = useScroll();
  const scaleX = useSpring(scrollYProgress, {
    stiffness: 100,
    damping: 30,
    restDelta: 0.001
  });

  const getRotate = () => {
    if (arrow == 0) {
      return [0, 0]
    }
    if (arrow == 1) {
      return ['15deg', '-15deg']
    }
    if (arrow == 2) {
      return ['15deg', '-15deg']
    }
    return [0, 0];
  }

  return (
    <AppShell
      padding="md"
      navbar={<NavBar title={title} userInfo={userInfo} openState={open} />}
      className={`${fixed ? "dark fixed" : "dark"}`}
      styles={(theme) => {
        let style = {
          main: {
            backgroundColor: theme.colorScheme === "dark" ? "#1f2937" : "#fff",
          },
        } as any;
        if (pageName != 'home') {
          style.main.marginLeft = !open ? "-288px" : "auto"
        }
        return style
      }}
    >
      <ConfigProvider locale={zhCN} theme={{ token: { colorPrimary: "#4F46E5" } }}>
        {full ? children : <div className="p-6">
          <motion.div className="progress-bar" style={{ scaleX }} />
          {children}
          {true && (<div className="fixed left-0 top-1/2 z-40" style={{ transform: `translateX(${open ? '288px' : '0'}) translateY(-50%) rotate(${open ? 0 : 180}deg) translateZ(0px)` }}>
            <Tooltip placement="right" title={open ? '隐藏侧边栏' : '展开侧边栏'}>
              <button onMouseEnter={() => {
                if (open) {
                  setArrow(1)
                } else {
                  setArrow(2)
                }
              }} onMouseLeave={() => {
                if (open) {
                  setArrow(0)
                } else {
                  setArrow(2)
                }
              }} onClick={() => {
                setOpen(!open)
              }}>
                <span className="" data-state="closed">
                  <div className="flex h-[72px] w-8 items-center justify-center">
                    <div className="flex h-6 w-6 flex-col items-center">
                      <div className="h-3 w-1 rounded-full" style={{ background: '#cdcdcd', transform: `translateY(0.15rem) rotate(${getRotate()[0]}) translateZ(0px)` }}></div>
                      <div className="h-3 w-1 rounded-full" style={{ background: '#cdcdcd', transform: `translateY(-0.15rem) rotate(${getRotate()[1]}) translateZ(0px)` }}></div>
                    </div>
                  </div>
                  <span style={{ position: 'absolute', border: '0px', width: '1px', height: '1px', padding: '0px', margin: '-1px', overflow: 'hidden', clip: 'rect(0px, 0px, 0px, 0px)', whiteSpace: 'nowrap', overflowWrap: 'normal' }}>关闭边栏</span>
                </span>
              </button>
            </Tooltip>
          </div>)}
        </div>}
      </ConfigProvider>
      <MouseTrail />
    </AppShell>
  )
}

export default Layout
