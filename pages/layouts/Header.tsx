import Head from "next/head"
import React, { Suspense, useState, FC, Fragment } from "react"
import { BlitzLayout } from "@blitzjs/next"
import { IconSun, IconMoonStars } from "@tabler/icons"
import { Routes, BlitzPage, useRouterQuery } from "@blitzjs/next"
import Link from "next/link"
import {
  Container,
  Switch,
  useMantineColorScheme,
  Grid,
  Skeleton,
  useMantineTheme,
} from "@mantine/core"
import { Dialog, Menu, Transition } from "@headlessui/react"
import NavBar from "./NavBar"
import { useMutation } from "@blitzjs/rpc"
import { useCurrentUser } from "app/users/hooks/useCurrentUser"
import logout from "app/auth/mutations/logout"
import { showNotification } from "@mantine/notifications"
import { ChevronDownIcon, MagnifyingGlassIcon } from "@heroicons/react/20/solid"
import {
  Bars3Icon,
  CalendarIcon,
  ChartPieIcon,
  DocumentDuplicateIcon,
  FolderIcon,
  HomeIcon,
  UsersIcon,
  XMarkIcon,
} from "@heroicons/react/24/outline"

function classNames(...classes) {
  return classes.filter(Boolean).join(" ")
}

import {
  createStyles,
  Header,
  HoverCard,
  Group,
  Button,
  UnstyledButton,
  Text,
  SimpleGrid,
  ThemeIcon,
  Anchor,
  Divider,
  Center,
  Box,
  Burger,
  Drawer,
  Collapse,
  ScrollArea,
} from "@mantine/core"
import { openConfirmModal } from "@mantine/modals"
import { MantineLogo } from "@mantine/ds"
import { useDisclosure } from "@mantine/hooks"
import {
  IconNotification,
  IconCode,
  IconBook,
  IconChartPie3,
  IconFingerprint,
  IconCoin,
  IconChevronDown,
} from "@tabler/icons"

const PRIMARY_COL_HEIGHT = 300

const userNavigation = [
  // { name: "Your profile", href: "#" },
  { name: "Sign out", href: "/auth/login" },
]

const useStyles = createStyles((theme) => ({
  link: {
    display: "flex",
    alignItems: "center",
    height: "100%",
    paddingLeft: theme.spacing.md,
    paddingRight: theme.spacing.md,
    textDecoration: "none",
    color: theme.colorScheme === "dark" ? theme.white : theme.black,
    fontWeight: 500,
    fontSize: theme.fontSizes.sm,

    [theme.fn.smallerThan("sm")]: {
      height: 42,
      display: "flex",
      alignItems: "center",
      width: "100%",
    },

    ...theme.fn.hover({
      backgroundColor: theme.colorScheme === "dark" ? theme.colors.dark[6] : theme.colors.gray[0],
    }),
  },

  subLink: {
    width: "100%",
    padding: `${theme.spacing.xs}px ${theme.spacing.md}px`,
    borderRadius: theme.radius.md,

    ...theme.fn.hover({
      backgroundColor: theme.colorScheme === "dark" ? theme.colors.dark[7] : theme.colors.gray[0],
    }),

    "&:active": theme.activeStyles,
  },

  dropdownFooter: {
    backgroundColor: theme.colorScheme === "dark" ? theme.colors.dark[7] : theme.colors.gray[0],
    margin: -theme.spacing.md,
    marginTop: theme.spacing.sm,
    padding: `${theme.spacing.md}px ${theme.spacing.md * 2}px`,
    paddingBottom: theme.spacing.xl,
    borderTop: `1px solid ${
      theme.colorScheme === "dark" ? theme.colors.dark[5] : theme.colors.gray[1]
    }`,
  },

  hiddenMobile: {
    [theme.fn.smallerThan("sm")]: {
      display: "none",
    },
  },

  hiddenDesktop: {
    [theme.fn.largerThan("sm")]: {
      display: "none",
    },
  },
}))

const mockdata = [
  {
    icon: IconCode,
    title: "Xspace",
    description: "美工刀平台 (低代码活动页、banner智能设计)",
  },
  {
    icon: IconCoin,
    title: "Zues",
    description: "宙斯平台",
  },
  {
    icon: IconBook,
    title: "WEBCI",
    description: "前端项目构建部署平台",
  },
  {
    icon: IconFingerprint,
    title: "FE Monitor",
    description: "前端埋点SDK & 数据平台",
  },
  {
    icon: IconChartPie3,
    title: "Docs",
    description: "组件文档平台",
  },
  {
    icon: IconNotification,
    title: "FE MOCK",
    description: "前端MOCK服务",
  },
]

const UserInfo = () => {
  const currentUser = useCurrentUser()
  const [logoutMutation] = useMutation(logout)

  const openModal = () => {
    openConfirmModal({
      title: "请确认操作",
      children: <Text size="sm">是否退出登录?</Text>,
      labels: { confirm: "确定", cancel: "取消" },
      onCancel: () => console.log("Cancel"),
      onConfirm: () => {
        void logoutMutation()
        showNotification({
          title: "notification",
          message: "退出登录啦! 🤥",
        })
        setTimeout(() => {
          location.href = "/auth/login"
        }, 1500)
      },
    })
  }

  if (currentUser) {
    return (
      <>
        <Button
          variant="default"
          className="button small"
          onClick={() => {
            openModal()
          }}
        >
          Logout
        </Button>
      </>
    )
  } else {
    return (
      <>
        <Button
          className="button small"
          onClick={async () => {
            window.location.href = "/auth/login"
          }}
        >
          Login
        </Button>
      </>
    )
  }
}

const Layout = ({ userInfo }) => {
  const [drawerOpened, { toggle: toggleDrawer, close: closeDrawer }] = useDisclosure(false)
  const [linksOpened, { toggle: toggleLinks }] = useDisclosure(false)
  const { classes, theme } = useStyles()
  const _theme = useMantineTheme()
  const SECONDARY_COL_HEIGHT = PRIMARY_COL_HEIGHT / 2 - theme.spacing.md / 2
  const { colorScheme, toggleColorScheme } = useMantineColorScheme()
  const glolbalTheme = useMantineTheme()
  const [sidebarOpen, setSidebarOpen] = useState(false)

  // const router = useRouter()

  const links = mockdata.map((item) => (
    <UnstyledButton className={classes.subLink} key={item.title}>
      <Group noWrap align="flex-start">
        <ThemeIcon size={34} variant="default" radius="md">
          <item.icon size={22} color={theme.fn.primaryColor()} />
        </ThemeIcon>
        <div>
          <Text size="sm" weight={500}>
            {item.title}
          </Text>
          <Text size="xs" color="dimmed">
            {item.description}
          </Text>
        </div>
      </Group>
    </UnstyledButton>
  ))

  return (
    <Header height={60} px="md">
      <Group position="apart" sx={{ height: "100%" }}>
        <div className="sticky top-0 z-40 flex h-16 w-full shrink-0 flex-row-reverse items-center justify-items-end gap-x-6 border-b border-white/5 bg-gray-900 px-4 shadow-sm  sm:px-6 lg:px-8">
          <Menu as="div" className="relative">
            <Menu.Button className="-m-1.5 flex items-center p-1.5">
              <span className="sr-only">Open user menu</span>
              <img
                className="h-8 w-8 rounded-full bg-gray-50"
                src="https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?ixlib=rb-1.2.1&ixid=eyJhcHBfaWQiOjEyMDd9&auto=format&fit=facearea&facepad=2&w=256&h=256&q=80"
                alt=""
              />
              <span className="hidden lg:flex lg:items-center">
                <span
                  className="ml-4 text-sm font-semibold leading-6 text-white"
                  aria-hidden="true"
                >
                  {userInfo.email}
                </span>
                <ChevronDownIcon className="ml-2 h-5 w-5 text-gray-400" aria-hidden="true" />
              </span>
            </Menu.Button>
            <Transition
              as={Fragment}
              enter="transition ease-out duration-100"
              enterFrom="transform opacity-0 scale-95"
              enterTo="transform opacity-100 scale-100"
              leave="transition ease-in duration-75"
              leaveFrom="transform opacity-100 scale-100"
              leaveTo="transform opacity-0 scale-95"
            >
              <Menu.Items className="absolute right-0 z-10 mt-2.5 w-32 origin-top-right rounded-md bg-white py-2 shadow-lg ring-1 ring-gray-900/5 focus:outline-none">
                {userNavigation.map((item) => (
                  <Menu.Item key={item.name}>
                    {({ active }) => (
                      <a
                        href={item.href}
                        className={classNames(
                          active ? "bg-gray-50" : "",
                          "block px-3 py-1 text-sm leading-6 text-gray-900"
                        )}
                      >
                        {item.name}
                      </a>
                    )}
                  </Menu.Item>
                ))}
              </Menu.Items>
            </Transition>
          </Menu>
        </div>

        <Group className={classes.hiddenMobile}>
          {/* Profile dropdown */}

          {/* <Switch
            checked={colorScheme === "dark"}
            onChange={() => toggleColorScheme()}
            size="lg"
            onLabel={<IconSun color={glolbalTheme.white} size={20} stroke={1.5} />}
            offLabel={<IconMoonStars color={glolbalTheme.colors.gray[6]} size={20} stroke={1.5} />}
          />
          <Suspense fallback="Loading...">
            <UserInfo />
          </Suspense> */}

          {/* <Button
            variant="default"
            onClick={() => {
              window.location.href = "/test"
            }}
          >
            Log in
          </Button>
          <Button>Sign up</Button> */}
        </Group>
      </Group>
    </Header>
  )
}

export default Layout
