import jwt from "jsonwebtoken"
import { NextApiRequest, NextApiResponse } from "next"
import { SecurePassword } from "@blitzjs/auth"
import db from "db"

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  res.setHeader("Access-Control-Allow-Origin", "*")
  const token = jwt.sign({ userId: 3 }, "nongfushanquan", {
    expiresIn: "30d",
  })
  let redirectUri = "http://localhost:4567/callback"
  res.redirect(`${redirectUri}?token=${token}`)
}
