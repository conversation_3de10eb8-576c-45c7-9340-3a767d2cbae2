import jwt from "jsonwebtoken"
import { NextApiRequest, NextApiResponse } from "next"

// JWT 验证中间件
const authenticate = async (req, res, next) => {
  try {
    const token = req.headers.authorization?.split(" ")[1] // 从请求头中获取 Token

    if (!token) {
      return res.status(401).json({ status: false, error: "No token provided." })
    }

    // 验证 Token
    jwt.verify(token, "nongfushanquan", (err, user) => {
      // console.log("err", err, typeof err, user)
      if (err) {
        let json = { status: false, message: "Token is not valid." } as any
        if (`${err}`.includes("expired")) {
          json.responseCode = "1000010001"
          json.message = "token失效"
        } else {
          json.message = "token错误"
        }
        return res.status(403).json(json)
      }
      req.user = user
      next()
    })
  } catch (error) {
    return res.status(500).json({ status: false, message: "Internal Server Error" })
  }
}

export default authenticate
