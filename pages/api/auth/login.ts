import jwt from "jsonwebtoken"
import { NextApiRequest, NextApiResponse } from "next"
import { SecurePassword } from "@blitzjs/auth"
// 假设这是你的数据库客户端
import db from "db"

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  res.setHeader("Access-Control-Allow-Origin", "*")
  try {
    const { username, password } = req.body
    const user = await db.user.findFirst({
      where: { email: `${username}@xinxuan.net` },
    })
    if (!user) {
      return res.status(401).json({ status: false, error: "User not found." })
    }
    const passwordValid = await SecurePassword.verify(user.hashedPassword, password)
    if (passwordValid !== SecurePassword.VALID) {
      return res.status(401).json({ status: false, error: "Invalid password." })
    }
    const token = jwt.sign({ userId: user.id }, "nongfushanquan", {
      expiresIn: "30d", // 令牌有效期，例如1小时
    })
    res.status(200).json({ status: true, entry: token })
  } catch (error) {
    res.status(500).json({ status: false, error: "Internal Server Error" })
  }
}
