import { NextApiRequest, NextApiResponse } from "next"
import db from "db"
import authenticate from "../auth/jwt" // 导入刚才创建的中间件

async function handler(req, res) {
  res.setHeader("Access-Control-Allow-Origin", "*")
  const { userId } = req.user
  const user = (await db.user.findFirst({
    where: {
      id: parseInt(userId),
    },
  })) as any

  console.log("userJSON", user)

  res.status(200).send({
    entry: {
      userName: user.name,
      email: user.email,
    },
    status: true,
  })
}

export default async function (req, res) {
  return authenticate(req, res, () => handler(req, res))
}
