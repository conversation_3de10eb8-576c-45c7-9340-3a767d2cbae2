import { NextApiRequest, NextApiResponse } from "next"
import { register, collectDefaultMetrics, Counter, Gauge, Histogram } from "prom-client"


try {
  collectDefaultMetrics({})
} catch (e) {}

const httpMetricsLabelNames = ["method", "path"]

export default async function handler(_: NextApiRequest, res: NextApiResponse<any>) {
  res.setHeader("Content-type", register.contentType)
  res.send(await register.metrics())
}
