import { SecurePassword } from "@blitzjs/auth"
import { resolver } from "@blitzjs/rpc"
import db from "db"

import { NextApiRequest, NextApiResponse } from "next"

function convertToPath(arr) {
  return "/" + arr.join("/")
}

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  console.log("mock-id", req.query.url)
  const origin = req.headers.origin || "http://" + req.headers.host
  res.setHeader("Access-Control-Allow-Origin", origin ? origin : "*")
  res.setHeader("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE")
  res.setHeader(
    "Access-Control-Allow-Headers",
    "Origin, X-Requested-With, Content-Type, Accept, token, app-code, device-type, deviceId, deviceType, channel, appCode, serviceCode, platType, appVersion, vip8, internal, cors, touristId, helper-for-target, clientTime, Authorization, sso, mass_tkt, menu-compatibility, dataType, Kl_device_id, Kl_display_type,Kl_os_type,Kl_platform, Kl_t, kl_trace_id, Kl_token, kL_app_code"
  )
  res.setHeader("Access-Control-Allow-Credentials", "true")

  let result
  let kunlunData
  const _id = req.query.id as any
  const path = convertToPath(req.query.url)

  kunlunData = await db.kunlun.findFirst({
    where: {
      kunlunGroupId: parseInt(_id),
      content: path,
    },
  })

  console.log("kunlunData", _id, path, kunlunData)

  if (!kunlunData) {
    res.status(200).json({ status: false })
  }

  result = await db.kunlunGroupMock.findFirst({
    where: {
      kunlunId: parseInt(kunlunData.id),
    },
  })

  // console.log("result", result)

  let data = {
    ...result.data,
  }

  res.status(200).json({ status: true, origin: req.headers?.origin, ...data })

}
