import { SecurePassword } from "@blitzjs/auth"
import { resolver } from "@blitzjs/rpc"
import db from "db"

import { NextApiRequest, NextApiResponse } from "next"

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  console.log("personal", req.query.id)
  // 在这里处理你的逻辑，返回需要的数据
  let result


  const _id = req.query.id as any

  result = await db.kunlunMock.findFirst({
    where: {
      id: parseInt(_id),
    },
  })

  if (!result) {
    res.status(200).json({ status: false })
    return null
  }

  let data = {
    ...result.data,

  }


  res.status(200).json({ status: true, ...data })

}
