import { SecurePassword } from "@blitzjs/auth"
import { resolver } from "@blitzjs/rpc"
import db from "db"
import { Role } from "types"
import { groupBy } from "lodash"

import dayjs from "dayjs"
import en from "dayjs/locale/en"

const formatSocial = (data, type) => {
  if (!data) {
    return []
  }
  let arr = [] as any
  if (type == "zh") {
    data.forEach((item: any) => {
      arr.push({
        id: item.id,
        thumb: item.thumb,
        time: dayjs(item.timeStr).format("YYYY年MM月DD日"),
        content: item.content,
      })
    })
  } else {
    data.forEach((item: any) => {
      arr.push({
        id: item.id,
        thumb: item.thumb,
        time: dayjs(item.timeStr).locale("es").format("D MMMM, YYYY"),
        content: item.content_en || item.content,
      })
    })
  }
  return arr
}

const formatArticleNews = (data, type) => {
  if (!data) {
    return []
  }
  let arr = [] as any
  if (type == "zh") {
    data.forEach((item: any) => {
      arr.push({
        id: item.id,
        thumb: item.thumb,
        title: item.title,
        time: item.timeStr,
        from: item.from,
        link: item.link,
        content: item.content,
      })
    })
  } else {
    data.forEach((item: any) => {
      arr.push({
        id: item.id,
        thumb: item.thumb,
        title: item.title_en || item.title,
        time: item.timeStr,
        from: item.from_en || item.from,
        link: item.link,
        content: item.content_en || item.content,
      })
    })
  }
  return arr
}

const formatKols = (data, type) => {
  if (!data) {
    return []
  }
  let arr = [] as any
  if (type == "zh") {
    data.forEach((item: any) => {
      let _list = item.list
      if (_list && _list.length > 0) {
        _list.forEach((o) => {
          delete o.content_en
        })
      }
      arr.push({
        id: item.id,
        imgUrl: item.imgUrl,
        name: item.name,
        time: item.timeStr,
        info: item.info,
        intro: item.intro,
        idNum: item.idNum,
        countNum: item.countNum,
        category: item.category,
        list: _list,
      })
    })
  } else {
    data.forEach((item: any) => {
      let _list = item.list
      if (_list && _list.length > 0) {
        _list.forEach((o) => {
          o.content == o.content_en || o.content
          delete o.content_en
        })
      }
      arr.push({
        id: item.id,
        imgUrl: item.imgUrl,
        name: item.name_en || item.name,
        time: item.timeStr,
        info: item.info_en || item.info,
        intro: item.intro_en || item.intro,
        idNum: item.idNum,
        countNum: item.countNum,
        category: item.category_en || item.category,
        list: item.list,
      })
    })
  }
  return arr
}

export default async function handler(req, res) {
  let result = await db.articleSocial.findMany({
    orderBy: [{ time: "desc" }, { sortNum: "desc" }],
  })
  let articleNews = await db.articleNews.findMany({
    orderBy: [{ time: "desc" }, { sortNum: "desc" }],
  })
  let kols = await db.kol.findMany({
    // orderBy: [{ time: "desc" }, { sortNum: "desc" }],
  })
  let groupTmp = groupBy(result, "group")

  /**
   *   csjz: "慈善救灾",
          xhzx: "辛火助学",
          xhjh: "辛火计划",
          xczx: "乡村振兴",
          kyjz: "抗疫捐赠",
          qtgy: "其他公益",
   */

  const params = {
    status: true,
    entry: {
      // zh: {
      //   social: {
      //     commitment: [
      //       {
      //         title: "慈善救灾",
      //         banner: "https://s.xinc818.com/files/xinxuan-portal-social-commitment-title-1.png",
      //         list: formatSocial(groupTmp["csjz"], "zh"),
      //       },
      //       {
      //         title: "辛火助学",
      //         banner: "https://s.xinc818.com/files/xinxuan-portal-social-commitment-title-2.png",
      //         list: formatSocial(groupTmp["xhzx"], "zh"),
      //       },
      //       {
      //         title: "乡村振兴",
      //         banner: "https://s.xinc818.com/files/xinxuan-portal-social-commitment-title-4.png",
      //         list: formatSocial(groupTmp["xczx"], "zh"),
      //       },
      //       {
      //         title: "抗疫捐赠",
      //         banner: "https://s.xinc818.com/files/xinxuan-portal-social-commitment-title-5.pn",
      //         list: formatSocial(groupTmp["kyjz"], "zh"),
      //       },
      //       {
      //         title: "其他公益",
      //         banner: "https://s.xinc818.com/files/xinxuan-portal-social-commitment-title-6.png",
      //         list: formatSocial(groupTmp["qtgy"], "zh"),
      //       },
      //     ],
      //   },
      //   news: formatArticleNews(articleNews, "zh"),
      //   kols: formatKols(kols, "zh"),
      // },
      en: {
        social: {
          commitment: [
            {
              title: "慈善救灾",
              banner: "https://s.xinc818.com/files/xinxuan-portal-social-commitment-title-1_en.png",
              list: formatSocial(groupTmp["csjz"], "en"),
            },
            {
              title: "辛火助学",
              banner: "https://s.xinc818.com/files/xinxuan-portal-social-commitment-title-2_en.png",
              list: formatSocial(groupTmp["xhzx"], "en"),
            },
            {
              title: "乡村振兴",
              banner: "https://s.xinc818.com/files/xinxuan-portal-social-commitment-title-3_en.png",
              list: formatSocial(groupTmp["xczx"], "en"),
            },
            {
              title: "抗疫捐赠",
              banner: "https://s.xinc818.com/files/xinxuan-portal-social-commitment-title-4_en.png",
              list: formatSocial(groupTmp["kyjz"], "en"),
            },
            {
              title: "其他公益",
              banner: "https://s.xinc818.com/files/xinxuan-portal-social-commitment-title-5_en.png",
              list: formatSocial(groupTmp["qtgy"], "en"),
            },
          ],
        },
        news: formatArticleNews(articleNews, "en"),
        kols: formatKols(kols, "en"),
      },
    },
  }

  res.status(200).json({ params })
}
