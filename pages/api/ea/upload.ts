import { NextApiRequest, NextApiResponse } from "next"
import * as OSS from "ali-oss"
import axios from "axios"
import { ossConfig } from "@/constants/config"
import { authenticateJWT } from "@/lib/auth"
import db from "db"

const tinify = require("tinify")

// Tinify API 密钥列表
const keys = [
  "Vrxp9frJqZk9HwFr5ly0KQgyH9rQgV4t", //lbq
]
let currentKeyIndex = 0

// 切换到下一个 API 密钥
const setNextApiKey = () => {
  currentKeyIndex = (currentKeyIndex + 1) % keys.length
  console.log("切换到下一个 API 密钥:", keys[currentKeyIndex])
  tinify.key = keys[currentKeyIndex]
}

// 生成随机文件名
function getRandomName(isTin?) {
  console.log("生成随机文件名，是否为 Tinify 压缩:", isTin)
  if (isTin) {
    return "chrome" + Date.now().toString(36) + Math.random().toString(36).substr(2, 6) + "tinify"
  } else {
    return "chrome" + Date.now().toString(36) + Math.random().toString(36).substr(2, 6)
  }
}

// 从 URL 获取图片数据
async function fetchImage(url) {
  console.log("正在从 URL 获取图片:", url)
  const response = await axios.get(url, { responseType: "arraybuffer" })
  console.log("图片获取成功")
  return response.data
}

// 修改阿里云图片尺寸
function changeAliyunImageSize(url, multiply) {
  console.log("修改阿里云图片尺寸，倍数:", multiply)
  // 匹配原始宽高
  const pattern = /thumbnail\/(\d+)x(\d+)\//i
  const match = url.match(pattern)

  if (match) {
    const width = parseInt(match[1])
    const height = parseInt(match[2])

    // 计算新宽高
    const newWidth = width * multiply
    const newHeight = height * multiply

    // 替换参数
    const newUrl = url.replace(pattern, `thumbnail/${newWidth}x${newHeight}/`)

    console.log("修改后的 URL:", newUrl)
    return newUrl
  }
  console.log("URL 未修改")
  return url
}

// 初始化 OSS 客户端
let client = new OSS(ossConfig)

// 上传并压缩图片
export async function uploadAndCompressImage(buffer) {
  let retries = keys.length
  console.log("开始上传并压缩图片")
  while (retries > 0) {
    try {
      console.log("尝试压缩，剩余重试次数:", retries)
      const result = await tinify.fromBuffer(buffer)
      const resultData = await result.toBuffer()
      console.log("压缩成功")
      return {
        status: true,
        entry: resultData,
      }
    } catch (error) {
      if (error instanceof tinify.AccountError && retries > 1) {
        // 如果是 API 密钥问题，尝试使用下一个密钥
        console.log("API 密钥错误，切换到下一个密钥")
        retries--
        setNextApiKey()
      } else {
        // 如果不是密钥问题或已经用完所有密钥，返回详细异常
        console.error("压缩失败:", error)
        return {
          status: false,
          entry: null,
          error: {
            message: error.message,
            type: error.constructor.name,
          },
        }
      }
    }
  }
}


export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  console.log("接收到新的请求")

  // 设置 CORS 头
  res.setHeader("Access-Control-Allow-Origin", "*")
  res.setHeader("Access-Control-Allow-Methods", "POST, OPTIONS")
  res.setHeader("Access-Control-Allow-Headers", "Content-Type, Authorization, token")

  // 处理 OPTIONS 请求
  if (req.method === "OPTIONS") {
    console.log("收到 OPTIONS 请求，返回 200")
    res.status(200).end()
    return
  }

  // 检查请求方法
  if (req.method !== "POST") {
    console.log("非法的请求方法:", req.method)
    res.status(405).json({ status: false, message: "Method Not Allowed" })
    return
  }

  // 解析请求体
  const params = req.body
  let { name, compress, imgMultiple, externalImageUrl }: any = params
  let imageDataBuffer
  console.log("请求参数:", params)

  // 使用 JWT 认证
  authenticateJWT(req, res, async (user) => {
    console.log("JWT 认证通过，用户 ID:", user.id)

    // 上传文件到 OSS
    async function uploadFile(filePath, fileContent, mine) {
      console.log("开始上传文件到 OSS, 路径:", filePath)
      try {
        const result = await client.put(filePath, Buffer.from(fileContent), mine ? mine : null)
        console.log("文件上传成功:", result.url)
        return result.url
      } catch (error) {
        console.error("文件上传失败:", error)
        throw error
      }
    }

    // 生成目标 URL
    let targetUrl = `files/flow/${getRandomName()}/${name}`
    console.log("生成的目标 URL:", targetUrl)

    // 处理外部图片 URL
    if (externalImageUrl) {
      console.log("处理外部图片 URL:", externalImageUrl)
      console.log("图片倍数:", imgMultiple)
      if (imgMultiple == "2x") {
        externalImageUrl = changeAliyunImageSize(externalImageUrl, 2)
      }
      imageDataBuffer = await fetchImage(externalImageUrl)
    }

    // 主要处理逻辑
    if (name && externalImageUrl) {
      try {
        let uploadedUrl
        if (compress == true || compress == "true") {
          console.log("开始压缩图片")
          const tinifyRes = await uploadAndCompressImage(imageDataBuffer)
          if (tinifyRes && tinifyRes.status) {
            console.log("压缩成功，准备上传")
            targetUrl = `files/flow/${getRandomName(true)}/${name}`
            uploadedUrl = await uploadFile(targetUrl, tinifyRes.entry, {
              mime: "image/png",
            })
          } else {
            console.error("压缩失败")
            throw new Error("Compression failed")
          }
        } else {
          console.log("不压缩，直接上传")
          uploadedUrl = await uploadFile(targetUrl, imageDataBuffer, null)
        }

        const fullUrl = `https://s.xinc818.com/${targetUrl}`
        console.log("生成的完整 URL:", fullUrl)

        // 在数据库中添加上传记录
        console.log("开始创建数据库记录")
        const assetRecord = await db.asset.create({
          data: {
            url: fullUrl,
            authorId: user.id,
          },
        })

        console.log("数据库记录创建成功:", assetRecord)

        res.status(200).json({ status: true, entry: fullUrl })
      } catch (error) {
        console.error("上传失败:", error)
        res.status(200).json({ status: false, entry: null, error: (error as Error).message })
      }
    } else {
      console.log("参数无效")
      res.status(200).json({ status: false, entry: null, message: "Invalid parameters" })
    }
  })
}
