import { NextApiRequest, NextApiResponse } from "next"
import jwt from "jsonwebtoken"
import axios from "axios"
const { authenticate } = require("ldap-authentication")
import db from "db"
const secret = "tims"
import { authenticateJWT } from "@/lib/auth"

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  res.setHeader("Access-Control-Allow-Origin", "*")
  res.setHeader("Access-Control-Allow-Methods", "GET, OPTIONS")
  res.setHeader("Access-Control-Allow-Headers", "Content-Type, Authorization, token")

  // Handle preflight request
  if (req.method === "OPTIONS") {
    res.status(200).end()
    return
  }
  authenticateJWT(req, res, async (user: any) => {
    const list = await db.asset.findMany({
      where: { authorId: user.id as number },
      select: { url: true, createdAt: true },
      orderBy: [{ createdAt: "desc" }],
      take: 50,
    })
    res.status(200).json({
      status: true,
      entry: list,
    })
  })

}
