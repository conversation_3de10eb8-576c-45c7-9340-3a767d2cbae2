import { NextApiRequest, NextApiResponse } from "next"
import jwt from "jsonwebtoken"
import axios from "axios"
const { authenticate } = require("ldap-authentication")
import db from "db"
const secret = "tims"
import { authenticateJWT } from "@/lib/auth"

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  res.setHeader("Access-Control-Allow-Origin", "*")
  res.setHeader("Access-Control-Allow-Methods", "GET, OPTIONS")
  res.setHeader("Access-Control-Allow-Headers", "Content-Type, Authorization, token")

  // Handle preflight request
  if (req.method === "OPTIONS") {
    res.status(200).end()
    return
  }

  // Ensure the request method is GET
  if (req.method !== "GET") {
    res.status(405).json({ status: false, message: "Method Not Allowed" })
    return
  }
  authenticateJWT(req, res, (user) => {
    res.status(200).json({
      status: true,
      entry: {
        ...user,
        defaultCredentials: {
          dev: {
            username: "test123",
            password: "test123",
          },
          daily: {
            username: "test123",
            password: "Test1234!&Tk",
          },
        },
      },
      message: "Welcome to the protected route",
    })
  })
}
