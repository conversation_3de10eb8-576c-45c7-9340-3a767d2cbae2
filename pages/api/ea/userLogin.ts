import { NextApiRequest, NextApiResponse } from "next"
import jwt from "jsonwebtoken"
import axios from "axios"
const { authenticate } = require("ldap-authentication")
import db from "db"
const secret = "tims"

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  // Set CORS headers to allow all origins
  res.setHeader("Access-Control-Allow-Origin", "*")
  res.setHeader("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS")
  res.setHeader("Access-Control-Allow-Headers", "Content-Type, Authorization")

  // Handle preflight requests
  if (req.method === "OPTIONS") {
    res.status(200).end()
    return
  }

  const params = req.body
  let { userName, password }: any = params

  let options = {
    ldapOpts: {
      url: "ldap://ldaps-inc.xinc818.com:389",
    },
    adminDn: "cn=admin-fe,ou=ops,dc=xinxuan,dc=com",
    adminPassword: "3U4G@zyXRg^RWh3eC#2x",
    userSearchBase: "dc=xinxuan,dc=com",
    usernameAttribute: "cn",
    username: userName,
    userPassword: password,
  }

  let ldapUser = await authenticate(options)
  let token = {}
  let userInfo = {}
  let userInfoRes = {} as any

  if (!ldapUser) {
    console.log("AuthenticationError~")
    res.status(200).json({ status: false, message: "登录验证失败" })
  } else {
    userInfoRes = (await db.user.findFirst({
      where: {
        email: ldapUser.mail,
      },
    })) as any
    userInfo = {
      id: userInfoRes.id,
      userName: userInfoRes.name,
      nickName: userInfoRes.nickName,
      avatar: userInfoRes.avatar,
    }
    token = jwt.sign(
      {
        ...userInfo,
        exp: Math.floor(Date.now() / 1000) + 60 * 60 * 24 * 7 * 2, // 1小时过期
      },
      secret
    )
  }
  if (userInfoRes.role && userInfoRes.role.includes("FE_DEVELOPER")) {
    res.status(200).json({
      status: true,
      entry: {
        token: token,
        userInfo,
      },
    })
  } else {
    res.status(200).json({ status: false, message: "Insufficient permissions" })
  }
}
