import { NextApiRequest, NextApiResponse } from "next"
import jwt from "jsonwebtoken"
import axios from "axios"
const { authenticate } = require("ldap-authentication")
import db from "db"
const secret = "tims"
import { authenticateJWT } from "@/lib/auth"

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  res.setHeader("Access-Control-Allow-Origin", "*")
  res.setHeader("Access-Control-Allow-Methods", "GET, OPTIONS")
  res.setHeader("Access-Control-Allow-Headers", "Content-Type, Authorization, token")

  // Handle preflight request
  if (req.method === "OPTIONS") {
    res.status(200).end()
    return
  }
  authenticateJWT(req, res, async () => {
    // 这里是保护的逻辑
    const result = await db.fENews.findMany({
      orderBy: {
        createdAt: "desc",
      },
      select: {
        id: true,
        content: true,
        createdAt: true,
        updatedAt: true,
        timeStr: true,
        author: {
          select: {
            id: true,
            name: true,
            email: true,
            avatar: true,
          },
        },
      },
      where: {
        isDeleted: false,
      },
    })
    res.status(200).json({
      status: true,
      entry: result,
    })
  })

  // res.status(200).json({ status: false })
}
