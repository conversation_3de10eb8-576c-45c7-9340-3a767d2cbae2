import { NextApiRequest, NextApiResponse } from "next"
import * as OSS from "ali-oss"
import axios from "axios"
import { ossConfig } from "@/constants/config"
import { authenticateJWT } from "@/lib/auth"
import db from "db"

// 生成随机文件名
function getRandomName(isTin?: boolean): string {
  if (isTin) {
    return "chromel" + Date.now().toString(36) + Math.random().toString(36).substr(2, 6) + "tinify"
  } else {
    return "chromel" + Date.now().toString(36) + Math.random().toString(36).substr(2, 6)
  }
}

// 从URL获取图片数据
async function fetchImage(url: string): Promise<ArrayBuffer> {
  const response = await axios.get(url, { responseType: "arraybuffer" })
  return response.data
}

// 初始化OSS客户端
const client = new OSS(ossConfig)

// API配置，设置请求体大小限制
export const config = {
  api: {
    bodyParser: {
      sizeLimit: "20mb", // 修改为您需要的大小限制
    },
  },
}
export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  // 设置CORS头，允许所有来源
  res.setHeader("Access-Control-Allow-Origin", "*")
  res.setHeader("Access-Control-Allow-Methods", "POST, OPTIONS")
  res.setHeader("Access-Control-Allow-Headers", "Content-Type, Authorization, token")

  // 处理预检请求
  if (req.method === "OPTIONS") {
    console.log("OPTIONS request received")
    res.status(200).end()
    return
  }

  // 确保请求方法为POST
  if (req.method !== "POST") {
    console.log("Method not allowed:", req.method)
    res.status(405).json({ status: false, message: "Method Not Allowed" })
    return
  }

  // 确保Content-Type是application/json
  if (req.headers["content-type"] !== "application/json") {
    console.log("Invalid Content-Type:", req.headers["content-type"])
    res.status(400).json({ status: false, message: "Invalid Content-Type" })
    return
  }

  const params = req.body
  let { name, file } = params

  // 兼容客户端发送的数据格式
  if (typeof params === "string") {
    try {
      const parsedParams = JSON.parse(params)
      name = parsedParams.name
      file = parsedParams
    } catch (error) {
      console.error("Error parsing JSON:", error)
      res.status(400).json({ status: false, message: "Invalid JSON format" })
      return
    }
  }

  console.log("Received params:", { name, fileReceived: !!file })

  // 使用JWT认证中间件
  authenticateJWT(req, res, async (user) => {
    console.log("JWT authentication passed") // 添加日志

    // 从请求体中获取参数

    // 上传文件到OSS的函数
    async function uploadFile(filePath: string, fileContent: string): Promise<string> {
      try {
        // 将Base64编码的文件内容转换为Buffer
        //@ts-ignore
        const result = await client.put(filePath, Buffer.from(fileContent.split(",")[1], "base64"))
        console.log("文件上传成功:", result.url)
        return result.url
      } catch (error) {
        console.error("文件上传失败:", error)
        throw error
      }
    }

    // 生成目标URL
    let targetUrl = `files/flow/${getRandomName()}/${name}`

    console.log("Target URL:", targetUrl) // 添加日志

    if (file) {
      try {
        // 尝试上传文件
        const uploadedUrl = await uploadFile(targetUrl, file)
        // 上传成功，返回文件URL
        console.log("File uploaded successfully") // 添加日志

        // 在数据库中添加上传记录
        const assetRecord = await db.asset.create({
          data: {
            url: `https://s.xinc818.com/${targetUrl}`,
            authorId: user.id,
          },
        })

        console.log("Asset record created:", assetRecord)

        res.status(200).json({ status: true, entry: `https://s.xinc818.com/${targetUrl}` })
      } catch (error) {
        // 上传失败，返回错误信息
        console.error("上传失败详细错误:", error)
        res
          .status(200)
          .json({ status: false, message: "File upload failed", error: (error as Error).message })
      }
    } else {
      // 没有提供文件，返回错误信息
      console.log("No file provided") // 添加日志
      res.status(200).json({ status: false, message: "No file provided" })
    }
  })
}
