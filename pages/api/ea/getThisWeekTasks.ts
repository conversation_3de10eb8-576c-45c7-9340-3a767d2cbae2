import { NextApiRequest, NextApiResponse } from "next"
import jwt from "jsonwebtoken"
import axios from "axios"
const { authenticate } = require("ldap-authentication")
import db from "db"
import dayjs from "dayjs" // 引入 dayjs
const secret = "tims"
const startOfWeek = dayjs().startOf("week").format("YYYY-MM-DD")
import { authenticateJWT } from "@/lib/auth"

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  res.setHeader("Access-Control-Allow-Origin", "*")
  res.setHeader("Access-Control-Allow-Methods", "GET, OPTIONS")
  res.setHeader("Access-Control-Allow-Headers", "Content-Type, Authorization, token")

  // Handle preflight request
  if (req.method === "OPTIONS") {
    res.status(200).end()
    return
  }
  const startOfWeek = dayjs().startOf("week").format("YYYY-MM-DD 00:00:00")

  authenticateJWT(req, res, async () => {

    const list = await db.task.findMany({
      where: {
        isDeleted: false,
      },
      orderBy: [{ createdAt: "desc" }],
      take: 60,
    })

    const filteredList = list.filter((task: any) => {
      let _releaseTime = task.data.releaseTime || dayjs("2026-12-31 00:00:00")
      const releaseTime = dayjs(_releaseTime).format("YYYY-MM-DD 00:00:00") as any
      if (releaseTime.valueOf() >= startOfWeek.valueOf()) {
        return true
      }
      return false
    })

    res.status(200).json({
      status: true,
      entry: filteredList,
    })
  })
}
