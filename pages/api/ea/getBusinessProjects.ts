import { NextApiRequest, NextApiResponse } from "next"
import db from "db"
import { authenticateJWT } from "@/lib/auth"

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  res.setHeader("Access-Control-Allow-Origin", "*")
  res.setHeader("Access-Control-Allow-Methods", "GET, OPTIONS")
  res.setHeader("Access-Control-Allow-Headers", "Content-Type, Authorization, token")

  // Handle preflight request
  if (req.method === "OPTIONS") {
    res.status(200).end()
    return
  }

  if (req.method !== "GET") {
    res.status(405).json({ status: false, message: "Method Not Allowed" })
    return
  }

  authenticateJWT(req, res, async (user) => {
    try {
      const userId = user.id;

      const list = await db.projects.findMany({
        where: {
          pid: "1",
          type: {
            in: [1, 2],
          },
        },
        select: {
          id: true,
          title: true,
          repoName: true,
          pic: true,
          type: true,
          pid: true,
          devUrl: true,
          dailyUrl: true,
          grayUrl: true,
          prodUrl: true,
          panguAppId: true,
          userProjectSettings: {
            where: {
              userId: userId,
            },
            select: {
              localPath: true,
            },
          },
        },
      });

      const projectsWithLocalPath = list.map((project:any) => {
        const quickSetUrl = project.repoName.split('/').pop() || '';
        return {
          ...project,
          localPath: project.userProjectSettings[0]?.localPath || null,
          gitUrl: `**********************:${project.repoName}.git`,
          devUrl: project.devUrl || `https://dev.xinc818.net/${quickSetUrl}`,
          dailyUrl: project.dailyUrl || `https://daily.xinc818.net/${quickSetUrl}`,
          grayUrl: project.grayUrl || `https://gray.xinc818.net/${quickSetUrl}`,
          prodUrl: project.prodUrl || `https://web.xinc818.com/${quickSetUrl}`,
        };
      });

      // Check cache for aone data
      const cacheKey = "ea_aone";
      const cachedData = await db.globalCache.findFirst({
        where: { key: cacheKey },
      });

      let aoneDataMap: { [projectId: string]: any[] } = {};
      let isCache = false;

      const now = new Date();
      const oneDayAgo = new Date(now.getTime() - 24 * 60 * 60 * 1000);

      if (cachedData && cachedData.updatedAt && new Date(cachedData.updatedAt) > oneDayAgo) {
        // Use cached data
        aoneDataMap = JSON.parse(cachedData.value || '{}');
        isCache = true;
      } else {
        // Fetch new aone data for each project (limited to 3 items)
        for (const project of projectsWithLocalPath) {
          const aoneData = await db.task.findMany({
            where: {
              isDeleted: false,
              AND: [
                {
                  data: {
                    path: ["project"],
                    array_contains: project.id,
                  },
                },
                {
                  data: {
                    path: ["progressType"],
                    array_contains: 3,
                  },
                },
              ],
            },
            select: {
              id: true,
              data: true,
            },
            take: 3, // Limit to 3 items
          });
          aoneDataMap[project.id] = aoneData.map((task:any) => ({
            id: task.id,
            title: task.data?.title || "最近任务"
          }));
        }

        // Update cache
        await db.globalCache.upsert({
          where: { key: cacheKey },
          update: {
            value: JSON.stringify(aoneDataMap),
            updatedAt: now.toISOString(),
          },
          create: {
            key: cacheKey,
            value: JSON.stringify(aoneDataMap),
            updatedAt: now.toISOString(),
          },
        });
      }

      const projectsWithAone = projectsWithLocalPath.map((project) => ({
        ...project,
        aone: aoneDataMap[project.id] || [],
      }));

      res.status(200).json({ status: true, entry: projectsWithAone, isCache })
    } catch (error) {
      console.error("Error fetching business projects:", error)
      res.status(500).json({ status: false, message: "Internal Server Error" })
    }
  })
}
