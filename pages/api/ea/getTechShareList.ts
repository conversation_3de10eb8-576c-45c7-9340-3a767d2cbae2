import { NextApiRequest, NextApiResponse } from "next"
import jwt from "jsonwebtoken"
import axios from "axios"
const { authenticate } = require("ldap-authentication")
import db from "db"
const secret = "tims"
import { authenticateJWT } from "@/lib/auth"

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  res.setHeader("Access-Control-Allow-Origin", "*")
  res.setHeader("Access-Control-Allow-Methods", "GET, OPTIONS")
  res.setHeader("Access-Control-Allow-Headers", "Content-Type, Authorization, token")

  // Handle preflight request
  if (req.method === "OPTIONS") {
    res.status(200).end()
    return
  }

  authenticateJWT(req, res, async () => {
    // 这里是保护的逻辑
    const result = await db.fEShare.findMany({
      orderBy: {
        createdAt: "desc",
      },
      select: {
        id: true,
        // content: true,
        fe: true,
        title: true,
        pic: true,
      },
      where: {
        isDeleted: false,
        OR: [{ type: 1 }, { type: 2 }, { type: 5 }],
      },
      take: 20,
    })
    res.status(200).json({
      status: true,
      entry: result,
    })
  })
}
