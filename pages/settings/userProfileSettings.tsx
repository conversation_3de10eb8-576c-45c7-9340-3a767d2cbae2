import Layout from "pages/layouts/Layout"
import { useEffect, useState, useRef } from "react"
import { useMutation } from "@blitzjs/rpc"
import SectionHeadings from "app/components/SectionHeadings"
import * as React from "react";
import { upload } from "utils/aliyun"
import getUserInfo from "app/users/mutations/getUserInfo"
import updateUserInfo from "app/users/mutations/updateUserInfo"
import { Button, Upload, Card, message, Avatar, Input, Form, type FormInstance } from 'antd';
import { UploadOutlined, UserOutlined, RedoOutlined } from '@ant-design/icons';

const DEFAULT_AVATAR_URL = "https://static.xinc818.com/vip8/admin/images/8c41f326326547548426f8e5139a52dc/ly5npo5knction.avif";

export default function UserProfileSettingsPage(props) {
  const formRef = useRef<FormInstance>(null);
  const [isLoaded, setIsLoaded] = useState(false);
  const [avatarUrl, setAvatarUrl] = useState(DEFAULT_AVATAR_URL);
  const [getUserInfoMutation] = useMutation(getUserInfo, {})
  const [updateUserInfoMutation] = useMutation(updateUserInfo, {})

  useEffect(() => {
    loadUserInfo()
  }, [])

  const loadUserInfo = async () => {
    const response = await getUserInfoMutation({}) as any;
    if (response?.status) {
      setAvatarUrl(response?.entry.avatar)
      if (formRef.current) {
        formRef.current.setFieldsValue({
          "nickName": response?.entry.nickName
        });
      }
    }
    setIsLoaded(true);
  }


  const beforeUpload = (file) => {
    // const isJpgOrPng = file.type === 'image/jpeg' || file.type === 'image/png';
    // if (!isJpgOrPng) {
    //   message.error('你只能上传 JPG/PNG 文件!');
    // }
    const isLt2M = file.size / 1024 / 1024 < 2;
    if (!isLt2M) {
      message.error('图片必须小于 2MB!');
    }
    return isLt2M;
  };

  const handleAvatarUpload = async (uploadInfo) => {
    console.log('info', uploadInfo);

    const url = await upload(uploadInfo.file) as any;
    setAvatarUrl(url);
  };

  const resetAvatarToDefault = () => {
    setAvatarUrl(DEFAULT_AVATAR_URL);
  };

  const onFinish = async (values) => {
    const response = await updateUserInfoMutation({
      ...values,
      avatar: avatarUrl
    }) as any;
    if (response.status) {
      message.success('保存成功!');
    } else {
      message.error('保存失败!');
    }
  }

  return (
    <Layout {...props}>
      <SectionHeadings name="个人信息设置" />
      <div className="flex justify-center items-center x-space" style={{ height: "calc(100vh - 160px)", display: isLoaded ? 'flex' : 'none' }}>
        <Card
          title="个人信息"
          className="max-w-md w-full mx-auto rounded-none md:rounded-2xl p-4 shadow-lg bg-white overflow-hidden"
          bordered={true}
          style={{ width: 360, margin: '24px auto' }}
        >
          <div className="avatar-container" style={{ marginBottom: 16, textAlign: 'center' }}>
            <Avatar size={128} icon={<UserOutlined />} src={avatarUrl} />
          </div>

          <div className="flex justify-between mx-4 items-center pt-6">
            <Upload
              name="avatar"
              beforeUpload={beforeUpload}
              customRequest={handleAvatarUpload}
              showUploadList={false}
            >
              <Button icon={<UploadOutlined />}>上传</Button>
            </Upload>

            <Button
              style={{ marginTop: 0 }}
              onClick={resetAvatarToDefault}
              icon={<RedoOutlined />}
            >
              重置
            </Button>
          </div>

          <Form className="mt-2 mx-auto" ref={formRef} onFinish={onFinish}>
            <Form.Item
              name="nickName"
              style={{ width: '248px', marginLeft: '16px', marginTop: '20px' }}
              rules={[{ required: false, message: '请输入你的花名' }]}
            >
              <Input placeholder="输入你的花名" maxLength={12} />
            </Form.Item>

            <Form.Item className="text-center">
              <Button type="primary" htmlType="submit">保存设置</Button>
            </Form.Item>
          </Form>
        </Card>
      </div>
    </Layout >
  )
}
