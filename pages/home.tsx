import React from "react";
import { BlitzPage } from "@blitzjs/next";
import Layout from "./layouts/Layout";
import TaskListSection from "@/components/homePage/TaskListSection";
import FeedSection from "@/components/homePage/FeedSection";

const HomePage: BlitzPage = ({ ...props }) => {
  return (
    <Layout title="home" {...props} full fixed pageName="home">
      <main className="h-full bg-gray-900" id="home">
        <TaskListSection />
        <FeedSection />
      </main>
      <iframe></iframe>
    </Layout>
  );
};

export default HomePage;
