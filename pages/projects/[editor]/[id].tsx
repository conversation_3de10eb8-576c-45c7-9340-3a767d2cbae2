/* eslint-disable react/no-unknown-property */
import Layout from "pages/layouts/Layout"
import { Timeline, Button, Select, Input } from "antd"
import { useEffect, useState, useRef } from "react"
import { BlitzPage } from "@blitzjs/next"
import SectionHeadings from "app/components/SectionHeadings"
import { useMutation } from "@blitzjs/rpc"
import updateProject from "app/project/mutations/updateProject"
import queryProject from "app/project/mutations/queryProject"
import queryProjectHistory from "app/project/mutations/queryProjectHistory"
import { showNotification } from "@mantine/notifications"
import Editor from "md-editor-rt"
import { useRouter } from "next/router"
import "md-editor-rt/lib/style.css"
import { Fragment } from "react"
import { onUploadImg } from "utils/aliyun"
import { Space } from "antd"
import dayjs from "dayjs"
import "@ant-design/flowchart/dist/index.css"
import React from "react"
import { Tab } from "@headlessui/react"

const tabs = [
  {
    name: "基本信息",
  },
  {
    name: "项目介绍&文档",
  },
  {
    name: "仓库文档(README.MD)",
  },
] as any

function classNames(...classes) {
  return classes.filter(Boolean).join(" ")
}

import axios from "axios"

export async function getReadmeContent(REPO_ID) {
  try {
    const response = await axios.get(
      `${process.env.GITLAB_API_BASE_URL}/projects/${encodeURIComponent(
        REPO_ID
      )}/repository/files/README%2Emd/raw`,
      {
        headers: {
          "PRIVATE-TOKEN": process.env.GITLAB_ACCESS_TOKEN_SEARCH,
        },
        params: {
          ref: "master",
        },
      }
    )
    return response.data
  } catch (error) {
    console.error("Error fetching README content:", error)
    return ""
  }
}

const HomePage: BlitzPage = (props) => {
  const [data, setData] = useState("# title")
  const [detail, setDetail] = useState({}) as any
  const [timeline, setTimeline] = useState([])
  const [queryProjectMutation] = useMutation(queryProject, {})
  const [updateProjectMutation] = useMutation(updateProject, {})
  const [queryProjectHistoryMutation] = useMutation(queryProjectHistory, {})
  const [readmeContent, setReadmeContent] = useState("")
  const [quickSetUrl, setQuickSetUrl] = useState("")
  const router = useRouter()
  const { id, editor } = router.query

  const editorRef = useRef() as any

  function sortDates(arr: any) {
    //@ts-ignore
    return arr.sort((a: any, b: any) => new Date(b.date) - new Date(a.date))
  }

  const getTableData = async () => {
    const res = await queryProjectMutation({
      id: id,
    })
    if (res.entry) {
      setData(res.entry.content)
      setDetail(res.entry)
    }
    const content = await getReadmeContent(res.entry.repoName)
    setReadmeContent(content)

    let historyResult = (await queryProjectHistoryMutation({ id: id })) as any
    if (historyResult.status)
      historyResult = historyResult.entry.map((item) => {
        return {
          date: dayjs(item.data.releaseTime).format("YYYY-MM-DD"),
          children: (
            <div
              className="cursor-pointer"
              onClick={() => {
                void router.push({
                  pathname: "/task/task_editor",
                  query: { from: "EXTERNAL_SOURCE", id: item.id },
                })
              }}
            >
              {item.data.title} (上线时间: {dayjs(item.data.releaseTime).format("YYYY-MM-DD")})
            </div>
          ),
        }
      })
    setTimeline(sortDates(historyResult))
  }

  const saveData = async (val) => {
    const res = await updateProjectMutation({
      id: id,
      content: val,
    })
    if (res.status) {
      showNotification({
        title: "成功",
        message: "保存成功",
        color: "green",
      })
    }
  }

  const saveBasicInfo = async (values) => {
    const res = await updateProjectMutation({
      id: id,
      ...values,
    })
    if (res.status) {
      showNotification({
        title: "成功",
        message: "保存成功",
        color: "green",
      })
    }
  }

  useEffect(() => {
    void getTableData()
  }, [id, editor])

  const quickSetAllUrls = () => {
    if (quickSetUrl) {
      setDetail({
        ...detail,
        devUrl: `https://dev.xinc818.net/${quickSetUrl}`,
        dailyUrl: `https://daily.xinc818.net/${quickSetUrl}`,
        grayUrl: `https://gray.xinc818.net/${quickSetUrl}`,
        prodUrl: `https://web.xinc818.com/${quickSetUrl}`,
      });
    }
  };

  return (
    <Layout title="projects" {...props}>
      {editor === "view" ? (
        <SectionHeadings
          name={"查看文档"}
          btnText="编辑文档"
          onClick={() => {
            void router.push({
              pathname: `/projects/edit/${id}`,
            })
          }}
        />
      ) : (
        <SectionHeadings
          name={"编辑文档"}
          btnText="返回查看"
          onClick={() => {
            void router.push({
              pathname: `/projects/view/${id}`,
            })
          }}
        />
      )}

      <div className="bg-white">
        <section
          aria-labelledby="features-heading"
          className="max-w-9xl mx-auto py-8 sm:px-2 lg:px-8"
        >
          <div className="mx-auto max-w-2xl px-4 lg:max-w-none lg:px-0">
            <div className="max-w-3xl">
              <div className="flex items-center">
                <img
                  src={detail.pic}
                  alt="logo"
                  className="mr-4 h-36 w-36"
                />
                <div>
                  <h2
                    id="features-heading"
                    className="text-3xl font-bold tracking-tight text-gray-900 sm:text-4xl"
                  >
                    {detail.title}
                  </h2>
                  <p className="mt-4 text-gray-500">{detail.desc}</p>
                </div>
              </div>
            </div>

            <Tab.Group as="div" className="mt-4">
              <div className="-mx-4 flex overflow-x-auto sm:mx-0">
                <div className="flex-auto border-b border-gray-200 px-4 sm:px-0">
                  <Tab.List className="-mb-px flex space-x-10">
                    {tabs.map((tab) => (
                      <Tab
                        key={tab.name}
                        className={({ selected }) =>
                          classNames(
                            selected
                              ? "border-indigo-500 text-indigo-600"
                              : "border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700",
                            "whitespace-nowrap border-b-2 py-6 text-sm font-medium"
                          )
                        }
                      >
                        {tab.name}
                      </Tab>
                    ))}
                  </Tab.List>
                </div>
              </div>

              <Tab.Panels as={Fragment}>
                <Tab.Panel key={tabs[0].name} className="space-y-16 pt-2 lg:pt-2">
                  <div className="mt-0 border-gray-100">
                    <dl className="divide-y divide-gray-100">
                      <div className="px-4 py-6 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-0">
                        <dt className="text-sm font-medium leading-6 text-gray-900">仓库地址</dt>
                        <dd className="mt-1 text-sm leading-6 text-gray-700 sm:col-span-2 sm:mt-0">
                          {editor === "view" ? (
                            <a href={`https://gitlab.xinc818.com/${detail.repoName}`} target="_blank" rel="noreferrer" className="flow-shine-link text-indigo-600 hover:text-indigo-500">
                              {detail.repoName}
                            </a>
                          ) : (
                            <Input value={detail.repoName} onChange={(e) => setDetail({ ...detail, repoName: e.target.value })} />
                          )}
                        </dd>
                      </div>
                      <div className="px-4 py-6 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-0">
                        <dt className="text-sm font-medium leading-6 text-gray-900">项目类型</dt>
                        <dd className="mt-1 text-sm leading-6 text-gray-700 sm:col-span-2 sm:mt-0">
                          {editor === "view" ? (
                            detail.type === 1 ? "中后台" :
                              detail.type === 2 ? "小程序" :
                                detail.type === 3 ? "App" :
                                  detail.type === 4 ? "官网" : ""
                          ) : (
                            <Select value={detail.type} onChange={(value) => setDetail({ ...detail, type: value })}>
                              <Select.Option value={1}>中后台</Select.Option>
                              <Select.Option value={2}>小程序</Select.Option>
                              <Select.Option value={3}>App</Select.Option>
                              <Select.Option value={4}>官网</Select.Option>
                            </Select>
                          )}
                        </dd>
                      </div>
                      <div className="px-4 py-6 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-0">
                        <dt className="text-sm font-medium leading-6 text-gray-900">开发环境URL</dt>
                        <dd className="mt-1 text-sm leading-6 text-gray-700 sm:col-span-2 sm:mt-0">
                          {editor === "view" ? detail.devUrl : <Input value={detail.devUrl} onChange={(e) => setDetail({ ...detail, devUrl: e.target.value })} />}
                        </dd>
                      </div>
                      <div className="px-4 py-6 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-0">
                        <dt className="text-sm font-medium leading-6 text-gray-900">日常环境URL</dt>
                        <dd className="mt-1 text-sm leading-6 text-gray-700 sm:col-span-2 sm:mt-0">
                          {editor === "view" ? detail.dailyUrl : <Input value={detail.dailyUrl} onChange={(e) => setDetail({ ...detail, dailyUrl: e.target.value })} />}
                        </dd>
                      </div>
                      <div className="px-4 py-6 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-0">
                        <dt className="text-sm font-medium leading-6 text-gray-900">灰度环境URL</dt>
                        <dd className="mt-1 text-sm leading-6 text-gray-700 sm:col-span-2 sm:mt-0">
                          {editor === "view" ? detail.grayUrl : <Input value={detail.grayUrl} onChange={(e) => setDetail({ ...detail, grayUrl: e.target.value })} />}
                        </dd>
                      </div>
                      <div className="px-4 py-6 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-0">
                        <dt className="text-sm font-medium leading-6 text-gray-900">生产环境URL</dt>
                        <dd className="mt-1 text-sm leading-6 text-gray-700 sm:col-span-2 sm:mt-0">
                          {editor === "view" ? detail.prodUrl : <Input value={detail.prodUrl} onChange={(e) => setDetail({ ...detail, prodUrl: e.target.value })} />}
                        </dd>
                      </div>
                      {editor !== "view" && (
                        <div className="px-4 py-6 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-0">
                          <dt className="text-sm font-medium leading-6 text-gray-900">快速设置URL</dt>
                          <dd className="mt-1 text-sm leading-6 text-gray-700 sm:col-span-2 sm:mt-0">
                            <Space>
                              <Input
                                placeholder="输入URL路径"
                                value={quickSetUrl}
                                onChange={(e) => setQuickSetUrl(e.target.value)}
                                style={{ width: '200px' }}
                              />
                              <Button onClick={quickSetAllUrls}>一键设置所有URL</Button>
                            </Space>
                          </dd>
                        </div>
                      )}
                      <div className="px-4 py-6 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-0">
                        <dt className="text-sm font-medium leading-6 text-gray-900">盘古AppID</dt>
                        <dd className="mt-1 text-sm leading-6 text-gray-700 sm:col-span-2 sm:mt-0">
                          {editor === "view" ? detail.panguAppId : <Input value={detail.panguAppId} onChange={(e) => setDetail({ ...detail, panguAppId: e.target.value })} />}
                        </dd>
                      </div>
                      {/* <div className="px-4 py-6 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-0">
                        <dt className="text-sm font-medium leading-6 text-gray-900">Git URL</dt>
                        <dd className="mt-1 text-sm leading-6 text-gray-700 sm:col-span-2 sm:mt-0">
                          {editor === "view" ? detail.gitUrl : <Input value={detail.gitUrl} onChange={(e) => setDetail({...detail, gitUrl: e.target.value})} />}
                        </dd>
                      </div> */}
                      <div className="px-4 py-6 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-0">
                        <dt className="text-sm font-medium leading-6 text-gray-900">您本地项目路径(为您一键打开)</dt>
                        <dd className="mt-1 text-sm leading-6 text-gray-700 sm:col-span-2 sm:mt-0">
                          {editor === "view" ? detail.localPath : <Input value={detail.localPath} onChange={(e) => setDetail({ ...detail, localPath: e.target.value })} />}
                        </dd>
                      </div>
                      <div className="px-4 py-6 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-0">
                        <dt className="text-sm font-medium leading-6 text-gray-900">项目版本日志</dt>
                        <dd className="mt-1 text-sm leading-6 text-gray-700 sm:col-span-2 sm:mt-0">
                          <Timeline items={timeline} />
                        </dd>
                      </div>
                    </dl>
                    {editor !== "view" && (
                      <div className="mt-6 flex items-center justify-end gap-x-6">
                        <Button type="primary" onClick={() => saveBasicInfo(detail)}>
                          保存
                        </Button>
                      </div>
                    )}
                  </div>
                </Tab.Panel>

                <Tab.Panel key={tabs[1].name} className="space-y-8 pt-2 lg:pt-2">
                  <Editor
                    editorId="article-content"
                    theme="light"
                    ref={editorRef}
                    onSave={async (v, h) => {
                      await saveData(v)
                    }}
                    onChange={setData}
                    onUploadImg={onUploadImg}
                    modelValue={data}
                    preview={true}
                    key={"view-" + editor + "-" + id}
                    previewOnly={editor == "view" ? true : false}
                  />
                  {editor != "view" && (
                    <button
                      onClick={() => {
                        console.log("editorRef", editorRef)
                        editorRef.current?.triggerSave()
                      }}
                      type="button"
                      className="my-0 inline-flex items-center rounded-md bg-indigo-600 px-3 py-2 text-sm font-semibold text-white shadow-sm hover:bg-indigo-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2"
                    >
                      保存
                    </button>
                  )}
                </Tab.Panel>
                <Tab.Panel key={tabs[2].name} className="space-y-16 pt-6 lg:pt-6">
                  <Editor
                    theme="light"
                    modelValue={readmeContent}
                    preview={true}
                    previewOnly={true}
                  />
                </Tab.Panel>
              </Tab.Panels>
            </Tab.Group>
          </div>
        </section>
      </div>
    </Layout>
  )
}

export default HomePage
