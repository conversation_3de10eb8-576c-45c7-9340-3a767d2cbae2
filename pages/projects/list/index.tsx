/* eslint-disable react/no-unknown-property */
import Layout from "pages/layouts/Layout"
import { Tag } from "antd"
import { useEffect, useState } from "react"
import { BlitzPage } from "@blitzjs/next"
import SectionHeadings from "app/components/SectionHeadings"
import { useMutation } from "@blitzjs/rpc"
import queryProject from "app/project/mutations/queryProject"
import { useRouter } from "next/router"
import React from "react"
import { MagnifyingGlassIcon } from "@heroicons/react/24/outline"

const HomePage: BlitzPage = (props) => {
  const [searchTerm, setSearchTerm] = useState("")
  const [selectedType, setSelectedType] = useState<number | null>(null)

  const [data, setData] = useState<any[]>([])
  const [queryProjectMutation] = useMutation(queryProject)
  const router = useRouter()

  const getTableData = async () => {
    try {
      const res = await queryProjectMutation({
        pid: '1',
      })
      if (res && res.entry) {
        setData(res.entry)
      }
    } catch (error) {
      console.error("Error fetching project data:", error)
    }
  }

  useEffect(() => {
    void getTableData()
  }, [])

  const iconList = {
    githubIcon: (
      <svg
        className="h-4 w-4 fill-current"
        viewBox="0 0 24 24"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path d="M12.026 2C7.13295 1.99937 2.96183 5.54799 2.17842 10.3779C1.395 15.2079 4.23061 19.893 8.87302 21.439C9.37302 21.529 9.55202 21.222 9.55202 20.958C9.55202 20.721 9.54402 20.093 9.54102 19.258C6.76602 19.858 6.18002 17.92 6.18002 17.92C5.99733 17.317 5.60459 16.7993 5.07302 16.461C4.17302 15.842 5.14202 15.856 5.14202 15.856C5.78269 15.9438 6.34657 16.3235 6.66902 16.884C6.94195 17.3803 7.40177 17.747 7.94632 17.9026C8.49087 18.0583 9.07503 17.99 9.56902 17.713C9.61544 17.207 9.84055 16.7341 10.204 16.379C7.99002 16.128 5.66202 15.272 5.66202 11.449C5.64973 10.4602 6.01691 9.5043 6.68802 8.778C6.38437 7.91731 6.42013 6.97325 6.78802 6.138C6.78802 6.138 7.62502 5.869 9.53002 7.159C11.1639 6.71101 12.8882 6.71101 14.522 7.159C16.428 5.868 17.264 6.138 17.264 6.138C17.6336 6.97286 17.6694 7.91757 17.364 8.778C18.0376 9.50423 18.4045 10.4626 18.388 11.453C18.388 15.286 16.058 16.128 13.836 16.375C14.3153 16.8651 14.5612 17.5373 14.511 18.221C14.511 19.555 14.499 20.631 14.499 20.958C14.499 21.225 14.677 21.535 15.186 21.437C19.8265 19.8884 22.6591 15.203 21.874 10.3743C21.089 5.54565 16.9181 1.99888 12.026 2Z"></path>
      </svg>
    ),

    edit: (
      <svg
        xmlns="http://www.w3.org/2000/svg"
        fill="none"
        viewBox="0 0 24 24"
        strokeWidth="1.5"
        stroke="currentColor"
        className="h-4 w-4"
      >
        <path
          strokeLinecap="round"
          strokeLinejoin="round"
          d="M16.862 4.487l1.687-1.688a1.875 1.875 0 112.652 2.652L10.582 16.07a4.5 4.5 0 01-1.897 1.13L6 18l.8-2.685a4.5 4.5 0 011.13-1.897l8.932-8.931zm0 0L19.5 7.125M18 14v4.75A2.25 2.25 0 0115.75 21H5.25A2.25 2.25 0 013 18.75V8.25A2.25 2.25 0 015.25 6H10"
        />
      </svg>
    ),
  }

  function getLastSegment(s: string) {
    const index = s.lastIndexOf("/")
    return index < 0 ? s : s.substring(index + 1)
  }

  const getTypeTag = (type: number) => {
    switch (type) {
      case 1:
        return <Tag color="blue">中后台</Tag>
      case 2:
        return <Tag color="green">小程序</Tag>
      case 4:
        return <Tag color="orange">APP</Tag>
      case 6:
        return <Tag color="purple">官网</Tag>
      default:
        return null
    }
  }

  const filteredData = data.filter(
    (item) =>
      (selectedType === null || item.type === selectedType) &&
      (item.title.toLowerCase().includes(searchTerm.trim().toLowerCase()) ||
        item.repoName.toLowerCase().includes(searchTerm.trim().toLowerCase()) ||
        getTypeTag(item.type)?.props.children.toLowerCase().includes(searchTerm.trim().toLowerCase()))
  )

  const groupedData = filteredData.reduce((acc, item) => {
    if (!acc[item.type]) {
      acc[item.type] = []
    }
    acc[item.type].push(item)
    return acc
  }, {} as Record<number, typeof data>)

  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchTerm(e.target.value)
  }

  const handleSearchBlur = () => {
    setSearchTerm(searchTerm.trim())
  }

  return (
    <Layout title="projects" {...props}>
      <div className="min-h-screen bg-white py-4">
        <div className="container mx-auto px-4">
          <SectionHeadings name="项目列表" />
          <div className="mb-8">
            <div className="relative w-full max-w-2xl mx-auto mb-4">
              <input
                type="text"
                placeholder="搜索项目..."
                value={searchTerm}
                onChange={handleSearchChange}
                onBlur={handleSearchBlur}
                className="w-full rounded-full border border-gray-300 bg-white py-2 pl-4 pr-10 focus:border-blue-500 focus:outline-none"
              />
              <MagnifyingGlassIcon className="absolute right-3 top-2.5 h-5 w-5 text-gray-400" />
            </div>
            <div className="flex flex-wrap justify-center gap-2 mt-4">
              <button
                onClick={() => setSelectedType(null)}
                className={`px-4 py-2 rounded-full text-sm font-medium transition-colors duration-200 ease-in-out ${selectedType === null
                  ? 'bg-blue-500 text-white'
                  : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
                  }`}
              >
                全部
              </button>
              {[1, 2, 4, 6].map((type) => (
                <button
                  key={type}
                  onClick={() => setSelectedType(type)}
                  className={`px-4 py-2 rounded-full text-sm font-medium transition-colors duration-200 ease-in-out ${selectedType === type
                    ? 'bg-blue-500 text-white'
                    : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
                    }`}
                >
                  {getTypeTag(type)?.props.children}
                </button>
              ))}
            </div>
          </div>
          {Object.entries(groupedData).map(([type, items]) => (
            <div key={type} className="mb-8">
              <h3 className="text-xl font-semibold mb-4">{getTypeTag(Number(type))?.props.children}</h3>
              <div className="grid gap-4 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4">
                {(items as typeof data).map((item, idx) => (
                  <div
                    key={idx}
                    onClick={() => {
                      void router.push({
                        pathname: `/projects/view/${item.id}`,
                      })
                    }}
                    className="group cursor-pointer overflow-hidden rounded-lg bg-white shadow transition-all duration-300 hover:shadow-md"
                  >
                    <div className="p-4">
                      <div className="mb-3 flex items-center justify-between">
                        <div className="flex items-center">
                          <img
                            className="mr-3 h-10 w-10 rounded-full object-cover"
                            src={item.pic || "https://s.xinc818.com/assets/gaea-sso/icon/supply.png?t=2"}
                            alt=""
                          />
                          <h2 className="text-base font-semibold text-gray-800 transition-colors duration-300 group-hover:text-blue-600 truncate max-w-[150px]">
                            {item.title}
                          </h2>
                        </div>
                        {getTypeTag(item.type)}
                      </div>
                      <p className="mb-3 text-xs text-gray-600">{getLastSegment(item.repoName)}</p>
                      <p
                        className="mb-4 text-sm text-gray-700"
                        style={{
                          display: "-webkit-box",
                          WebkitBoxOrient: "vertical",
                          WebkitLineClamp: 2,
                          overflow: "hidden",
                          textOverflow: "ellipsis",
                          minHeight: "3em",
                        }}
                      >
                        {item.desc}
                      </p>
                      <div className="flex justify-between text-xs">
                        <a
                          href={`https://gitlab.xinc818.com/${item.repoName}`}
                          target="_blank"
                          onClick={(e) => e.stopPropagation()}
                          className="flex items-center text-blue-600 hover:text-blue-800"
                          aria-label="Repository"
                          rel="noreferrer"
                        >
                          {iconList.githubIcon}
                          <span className="ml-1">Repository</span>
                        </a>
                        <button
                          onClick={(e) => {
                            e.stopPropagation()
                            void router.push({
                              pathname: `/projects/edit/${item.id}`,
                            })
                          }}
                          className="flex items-center text-gray-600 hover:text-gray-800"
                          aria-label="Edit"
                        >
                          {iconList.edit}
                          <span className="ml-1">Edit</span>
                        </button>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          ))}
        </div>
      </div>
    </Layout>
  )
}

export default HomePage
