import Head from "next/head"
import { ErrorComponent } from "@blitzjs/next"
import Layout from "pages/layouts/Layout"

// ------------------------------------------------------
// This page is rendered if a route match is not found
// ------------------------------------------------------
export default function WebCi(props) {
  return (
    <>
      <Layout title="webci" full {...props}>
        <iframe src="https://pangu.xinc818.com" width={"100%"} height={"100%"} frameBorder="0" />
      </Layout>
    </>
  )
}
