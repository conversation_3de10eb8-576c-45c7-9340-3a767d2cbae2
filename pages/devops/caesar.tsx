import React from "react"
import Layout from "pages/layouts/Layout"

interface CaesarPageProps {
  title?: string;
  full?: boolean;
  [key: string]: any;
}

const CaesarPage: React.FC<CaesarPageProps> = (props) => {
  return (
    <Layout title="webci" full {...props}>
      <iframe
        src="https://caesar.xinc818.com"
        width="100%"
        height="100%"
        frameBorder="0"
      />
    </Layout>
  )
}

export default CaesarPage
