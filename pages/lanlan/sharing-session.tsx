/**
 * @file ea.tsx
 * @description 经验沉淀页面
 * @lastModified 2024-07-23
**/

/* eslint-disable react/no-unknown-property */
// @ts-nocheck
import React, { useEffect, useState, useCallback } from "react"
import { useRouter } from "next/router"
import { useMutation } from "@blitzjs/rpc"
import { Form, Input, Button, Image } from "antd"
import Layout from "pages/layouts/Layout"
import SectionHeadings from "app/components/SectionHeadings"
import querySharingSession from "app/share/mutations/querySharingSession"
import { BlitzPage } from "@blitzjs/next"

interface SharingSession {
  id: string
  title: string
  pic: string
  fe: string
}

interface HomePageProps {
  // 如果有其他props，请在这里定义
}

const HomePage: BlitzPage<HomePageProps> = (props) => {
  const [form] = Form.useForm<{ title: string }>()
  const [data, setData] = useState<SharingSession[]>([])
  const [isLoading, setIsLoading] = useState(false)
  const [querySharingSessionMutation] = useMutation(querySharingSession)
  const router = useRouter()

  const getTableData = useCallback(async () => {
    setIsLoading(true)
    try {
      const res = await querySharingSessionMutation({ type: "ea" })
      if ('entry' in res && Array.isArray(res.entry)) {
        setData(res.entry)
      }
    } catch (error) {
      console.error("获取数据失败:", error)
      // 这里可以添加错误处理逻辑，比如显示一个错误通知
    } finally {
      setIsLoading(false)
    }
  }, [querySharingSessionMutation])

  useEffect(() => {
    void getTableData()
  }, [getTableData])

  const onFinish = async (values: { title: string }) => {
    setIsLoading(true)
    try {
      const result = await querySharingSessionMutation({
        type: "ea",
        title: values.title,
      })
      if ('entry' in result && Array.isArray(result.entry)) {
        setData(result.entry)
      }
    } catch (error) {
      console.error("查询失败:", error)
      // 这里可以添加错误处理逻辑，比如显示一个错误通知
    } finally {
      setIsLoading(false)
    }
  }

  const onReset = async () => {
    form.resetFields()
    await getTableData()
  }

  return (
    <Layout title="projects" {...props}>
      <SectionHeadings
        name="经验分享"
        btnText="新增分享"
        onClick={() => {
          void router.push({
            pathname: "/lanlan/sharing-session-editor",
            query: {
              type: 2,
            },
          })
        }}
      />
      <Form
        layout="inline"
        className="mb-5"
        form={form}
        name="control-hooks"
        onFinish={onFinish}
      >
        <Form.Item name="title" label="标题">
          <Input />
        </Form.Item>
        <Form.Item>
          <Button type="primary" htmlType="submit" style={{ marginRight: "10px" }} loading={isLoading}>
            查询
          </Button>
          <Button htmlType="button" onClick={onReset} style={{ marginRight: "10px" }} loading={isLoading}>
            重置
          </Button>
        </Form.Item>
      </Form>
      <section className="bg-white">
        <div className="container mx-auto px-4 py-4">
          <div className="mt-3 grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-3 xl:mt-3 xl:gap-6">
            {data &&
              data.length > 0 &&
              data.map((item, index) => {
                return (
                  <div
                    key={index}
                    className="group relative h-0 cursor-pointer overflow-hidden rounded-lg pb-[100%]"
                    onClick={() => {
                      void router.push({
                        pathname: "/lanlan/sharing-session-editor",
                        query: {
                          id: item.id,
                          mode: "view",
                        },
                      })
                    }}
                  >
                    <div
                      className="absolute inset-0 bg-cover bg-center transition-transform duration-300 ease-in-out group-hover:scale-105"
                      style={{
                        backgroundImage: `url(${item.pic})`,
                      }}
                    />
                    <div className="absolute bottom-0 left-0 right-0 w-full overflow-hidden bg-white/60 px-6 py-3 backdrop-blur-sm transition-all duration-300 ease-in-out group-hover:bg-white/80 dark:bg-gray-800/60 dark:group-hover:bg-gray-800/80">
                      <h2 className="mt-2 text-lg font-semibold capitalize text-gray-800 dark:text-white">
                        《{item.title}》
                      </h2>
                      <p className="mt-1 text-base uppercase tracking-wider text-blue-500 dark:text-blue-400">
                        作者：{item.fe}
                      </p>
                    </div>
                  </div>
                )
              })}
          </div>
        </div>
      </section>
    </Layout>
  )
}

export default HomePage
