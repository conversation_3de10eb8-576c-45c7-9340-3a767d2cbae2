import Layout from "pages/layouts/Layout"
import { Image, Input, Select, Button, Form, Radio, Space, Card } from "antd"
import { Suspense, useEffect, useState, useRef } from "react"
import getToken from "app/users/queries/getToken"
import { useCurrentAssets } from "app/users/hooks/useCurrentAssets"
import { Routes, BlitzPage } from "@blitzjs/next"
import { showNotification } from "@mantine/notifications"
import { CopyButton } from "@mantine/core"
import { getEmployeesByTeam } from "@/constants/employees"
import SectionHeadings from "app/components/SectionHeadings"
import { useMutation, useQuery } from "@blitzjs/rpc"
import updateSharingsession from "app/share/mutations/updateSharingSession"
import querySharingSession from "app/share/mutations/querySharingSession"
import "md-editor-rt/lib/style.css"
import { Router, useRouter } from "next/router"
import React from "react"
import Editor from "md-editor-rt"
import { onUploadImg } from "utils/aliyun"


const { Option } = Select;

const SharingSessionForm = ({ form, onFinish, mode, initialData }) => {
  const router = useRouter();
  const [selectedType, setSelectedType] = useState<any>(1);
  const [selectedTeamPlanType, setSelectedTeamPlanType] = useState<any>(null);

  const typeOptions = [
    { label: '周会分享', value: 1 },
    { label: '经验沉淀', value: 2 },
    { label: '团队共创', value: 4 },
    { label: '技术夜谈', value: 5 },
    { label: '前端规范', value: 6 },
  ];

  const teamPlanOptions = [
    { label: '物料建设', value: 1 },
    { label: '平台/服务', value: 2 },
    { label: '体验/性能', value: 3 },
    { label: '流程改进', value: 4 },
    { label: '团队建设', value: 5 },
    { label: '问题跟进', value: 6 },
    { label: '知识星球', value: 7 },
    { label: 'PC端组件(@xlion)', value: 8 },
    { label: '小程序组件(lion-mini)', value: 9 },
    { label: '其他', value: 99 },
  ];

  useEffect(() => {
    const { type, id } = router.query;

    if (id && initialData) {
      // 如果是编辑模式，使用初始数据设置表单
      setSelectedType(initialData.type);
      setSelectedTeamPlanType(initialData.teamPlanType);
      form.setFieldsValue(initialData);
    } else if (type) {
      // 如果是新建模式且URL中有type参数
      const typeValue = parseInt(type as string, 10);
      setSelectedType(typeValue);
      form.setFieldsValue({ type: typeValue });

      // 如果是团队共创类型，设置默认的teamPlanType
      if (typeValue === 4) {
        const defaultTeamPlanType = 1; // 设置默认值，比如1表示"物料建设"
        setSelectedTeamPlanType(defaultTeamPlanType);
        form.setFieldsValue({ teamPlanType: defaultTeamPlanType });
      }
    } else {
      // 默认值
      form.setFieldsValue({ type: 1 });
    }
  }, [router.query, form, initialData]);

  const handleTypeChange = (e) => {
    const newType = e.target.value;
    setSelectedType(newType);
    if (newType !== 4) {
      form.setFieldsValue({ teamPlanType: undefined });
      setSelectedTeamPlanType(null);
    } else if (selectedTeamPlanType === null) {
      const defaultTeamPlanType = 1;
      setSelectedTeamPlanType(defaultTeamPlanType);
      form.setFieldsValue({ teamPlanType: defaultTeamPlanType });
    }
  };

  const handleTeamPlanTypeChange = (e) => {
    setSelectedTeamPlanType(e.target.value);
  };

  if (mode === 'view') return null;

  const team = getEmployeesByTeam("3") as any;

  return (
    <Form
      layout="vertical"
      className="mb-5"
      form={form}
      name="control-hooks"
      onFinish={onFinish}
      initialValues={{
        fe: [],
        title: '',
        pic: '',
        type: 1,
        progressType: 99,
      }}
    >
      <Form.Item name="type" rules={[{ required: true }]}>
        <Radio.Group onChange={handleTypeChange}>
          <Space >
            {typeOptions.map(option => (
              <Radio key={option.value} value={option.value}>
                {option.label}
              </Radio>
            ))}
          </Space>
        </Radio.Group>
      </Form.Item>

      {selectedType === 4 && (
        <Form.Item name="teamPlanType" label="团队共创类型" rules={[{ required: true }]}>
          <Radio.Group onChange={handleTeamPlanTypeChange}>
            <Space >
              {teamPlanOptions.map(option => (
                <Radio key={option.value} value={option.value}>
                  {option.label}
                </Radio>
              ))}
            </Space>
          </Radio.Group>
        </Form.Item>
      )}

      <div style={{ display: 'flex', flexWrap: 'wrap', gap: '20px' }}>
        <Form.Item name="title" label="标题" rules={[{ required: true }]} style={{ width: '300px' }}>
          <Input />
        </Form.Item>

        <Form.Item name="fe" label="选择前端" rules={[{ required: false }]} style={{ width: '300px' }}>
          <Select
            placeholder="选择"
            mode="multiple"
            options={team.concat([
              { label: "群议", value: "群议" },
              { label: "终端业务组", value: "终端业务组" },
              { label: "中台业务组", value: "中台业务组" },
              { label: "全员", value: "全员" },
            ])}
            allowClear
          />
        </Form.Item>

        {[1, 2, 5, 6].includes(selectedType) && (
          <Form.Item name="pic" label="封面图" rules={[{ required: false }]} style={{ width: '300px' }}>
            <Input />
          </Form.Item>
        )}

        {selectedType === 4 && (
          <Form.Item name="progressType" label="进度" rules={[{ required: true }]} style={{ width: '200px' }}>
            <Select>
              <Option value={1}>待开始</Option>
              <Option value={2}>进行中</Option>
              <Option value={3}>已完结</Option>
              <Option value={99}>无须进度</Option>
            </Select>
          </Form.Item>
        )}
      </div>

      <Form.Item>
        <Button type="primary" htmlType="submit" style={{ marginRight: '10px' }}>
          保存
        </Button>
      </Form.Item>
    </Form>
  );
};

const HomePage: BlitzPage = (props) => {
  const router = useRouter()
  const [formData, setFormData] = useState({}) as any
  const [data, setData] = useState("# title")
  const [loaded, setLoaded] = useState(false)
  const [form] = Form.useForm()
  const [initialData, setInitialData] = useState(null);

  const [updateSharingsessionMutation] = useMutation(updateSharingsession, {})
  const [querySharingSessionMutation] = useMutation(querySharingSession, {})
  const { id, editor, mode } = router.query

  const editorRef = useRef() as any

  const getTableData = async () => {
    if (id) {
      const res = await querySharingSessionMutation({
        id: id,
      })
      if (res.entry) {
        setLoaded(true)
        setInitialData(res.entry);
        form.setFieldsValue({
          title: res.entry.title,
          pic: res.entry.pic,
          fe: res.entry.fe,
          type: res.entry.type,
          progressType: res.entry.progressType,
          teamPlanType: res.entry.teamPlanType,
        })
        setData(res.entry.content)
      }
    } else {
      setLoaded(true)
    }
  }

  useEffect(() => {
    void getTableData()
  }, [])

  const saveData = async (val) => {
    const res = await updateSharingsessionMutation({
      id: id,
      fe: formData.fe,
      title: formData.title,
      pic: formData.pic,
      type: formData.type,
      progressType: formData.progressType,
      teamPlanType: formData.teamPlanType,
      content: val,
    })
    if (res.status) {
      showNotification({
        title: "成功",
        message: "保存成功",
        color: "green",
      })
    }
  }

  const onFinish = (values) => {
    console.log("valuesvalues", values)
    setFormData(values)
    showNotification({
      title: "提示",
      message: "正在保存中...",
      color: "green",
    })
    setTimeout(() => {
      editorRef.current?.triggerSave()
    }, 1000)
  }

  return (
    <Layout title="projects" {...props}>
      {mode == "view" ? (
        <SectionHeadings
          name="查看"
          onClick={() => {
            void router.push({
              pathname: "/lanlan/sharing-session-editor",
              query: {
                id: id,
                mode: "edit",
              },
            })
          }}
          btnText="编辑"
        />
      ) : (
        <SectionHeadings
          name="编辑"
          onClick={() => {
            void router.push({
              pathname: "/lanlan/sharing-session-editor",
              query: {
                id: id,
                mode: "view",
              },
            })
          }}
          btnText="返回查看"
        />
      )}

      <div>
        <SharingSessionForm
          form={form}
          onFinish={onFinish}
          mode={mode}
          initialData={initialData}
        />

        {loaded && (
          <Editor
            editorId="article-content"
            theme="light"
            onUploadImg={onUploadImg}
            ref={editorRef}
            onSave={async (v, h) => {
              await saveData(v)
            }}
            onChange={setData}
            modelValue={data}
            preview={true}
            key={"view-" + mode + "-" + id}
            previewOnly={mode == "view" ? true : false}
          />
        )}
      </div>
    </Layout>
  )
}

export default HomePage
