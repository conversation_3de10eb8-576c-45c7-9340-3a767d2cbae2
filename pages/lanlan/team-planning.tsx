/* eslint-disable react/no-unknown-property */
// @ts-nocheck
import Layout from "pages/layouts/Layout"
import { Text, Flex } from "@mantine/core"
import { Table, Modal, Input } from "antd"
import { Badge, Group, ActionIcon } from "@mantine/core"
import { Tag, Tabs, Button, Select, Form, Switch, Radio, Image } from "antd"
import { Suspense, useEffect, useState, useRef } from "react"
import getToken from "app/users/queries/getToken"
import { useCurrentAssets } from "app/users/hooks/useCurrentAssets"
import { Routes, BlitzPage } from "@blitzjs/next"
import { CopyButton } from "@mantine/core"
import SectionHeadings from "app/components/SectionHeadings"
import { useMutation, useQuery } from "@blitzjs/rpc"
import querySharingSession from "app/share/mutations/queryTeamPlanning"
import { motion, AnimatePresence } from "framer-motion";
import { Router, useRouter } from "next/router"
import React from "react"
import dayjs from "dayjs"

import { PencilSquareIcon } from "@heroicons/react/24/outline"

const TeamPlanningPage: BlitzPage = (props) => {
  const [form] = Form.useForm()
  const [isClient, setIsClient] = useState(false)

  // Wait until after client-side hydration to show
  useEffect(() => {
    setIsClient(true)
  }, [])

  const [data, setData] = useState([])
  const [querySharingSessionMutation] = useMutation(querySharingSession, {})
  const router = useRouter()

  const getTableData = async () => {
    const res = await querySharingSessionMutation({
      type: 4,
    })
    let arr = [] as any
    if (res.entry) {
      setData(res.entry || [])
    }
  }

  useEffect(() => {
    void getTableData()
  }, [])

  const columns: ColumnsType<any> = [
    {
      title: "标题",
      width: "220px",
      dataIndex: "title",
    },

    {
      title: "类型",
      width: "140px",
      dataIndex: "teamPlanType",
      render(value) {
        const teamPlanType = {
          1: "物料建设",
          2: "平台/服务",
          3: "体验/性能",
          4: "流程改进",
          5: "团队建设",
          6: "问题跟进",
          7: "知识星球",
          8: "PC端组件",
          9: "小程序组件",
          99: "-",
        }

        return teamPlanType[value]
      },
    },
    {
      title: "进度",
      width: "140px",
      dataIndex: "progressType",
      render(value) {
        const progressType = {
          1: "待开始",
          2: "进行中",
          3: "已完结",
          99: "-",
        }
        if (value == 1) {
          return (
            <Badge variant="gradient" gradient={{ from: "gray", to: "gray" }}>
              {progressType[value]}
            </Badge>
          )
        }
        if (value == 2) {
          return (
            <Badge variant="gradient" gradient={{ from: "yellow", to: "orange", deg: 105 }}>
              {progressType[value]}
            </Badge>
          )
        }
        if (value == 3) {
          return (
            <Badge variant="gradient" gradient={{ from: "teal", to: "lime", deg: 105 }}>
              {progressType[value]}
            </Badge>
          )
        }
        return progressType[value]
      },
    },
    {
      title: "参与人",
      width: "220px",
      dataIndex: "fe",
    },
    {
      title: "创建时间",
      width: "220px",
      dataIndex: "createdAt",
      render(value, record, index) {
        // eslint-disable-next-line jsx-a11y/alt-text
        return <span>{dayjs(record.createdAt).format("YYYY-MM-DD")}</span>
      }
    },
    {
      title: "操作",
      dataIndex: "options",
      width: "100px",
      render(value, record, index) {
        return (
          <>
            <Button
              type="link"
              onClick={() => {
                void router.push({
                  pathname: "/lanlan/sharing-session-editor",
                  query: {
                    id: record.id,
                    mode: "view",
                  },
                })
              }}
            >
              查看
            </Button>
          </>
        )
      },
    },
  ]

  const onFinish = async (values: any) => {
    console.log(values)
    const result = await querySharingSessionMutation({
      type: "co",
      title: values.title,
      // commerce: values.commerce,
    })
    setData(result.entry)
  }

  const onReset = async () => {
    form.resetFields()
    const result = await querySharingSessionMutation({
      type: "co",
    })
    setData(result.entry)
  }

  console.log(
    "data",
    data,
    data.filter((item) => item.teamPlanType === 1)
  )

  const tabsMap = [
    {
      label: "全部",
      key: 0,
      data: data,
    },
    {
      label: "物料建设",
      key: 1,
      data: data.filter((item) => item.teamPlanType === 1),
    },
    {
      label: "平台服务",
      key: 2,
      data: data.filter((item) => item.teamPlanType === 2),
    },
    {
      label: "性能体验",
      key: 3,
      data: data.filter((item) => item.teamPlanType === 3),
    },
    {
      label: "流程改进",
      key: 4,
      data: data.filter((item) => item.teamPlanType === 4),
    },
    {
      label: "团队建设",
      key: 5,
      data: data.filter((item) => item.teamPlanType === 5),
    },
    {
      label: "问题跟进",
      key: 6,
      data: data.filter((item) => item.teamPlanType === 6),
    },
    {
      label: "知识星球",
      key: 7,
      data: data.filter((item) => item.teamPlanType === 7),
    },
    {
      label: "PC端组件",
      key: 7,
      data: data.filter((item) => item.teamPlanType === 8),
    },
    {
      label: "小程序组件",
      key: 7,
      data: data.filter((item) => item.teamPlanType === 9),
    },
    {
      label: "其他",
      key: 99,
      data: data.filter((item) => item.teamPlanType === 99),
    },
  ]

  return (
    <Layout title="projects" {...props}>
      <SectionHeadings
        name="团队共创"
        btnText="新增"
        onClick={() => {
          void router.push({
            pathname: "/lanlan/sharing-session-editor",
            query: {
              type: 4,
            },
          })
        }}
      />

      <Tabs
        // onChange={onChange}
        type="card"
        items={tabsMap.map((_, i) => {
          const id = String(i + 1)
          return {
            label: _.label,
            key: id,
            children: (
              <Table
                columns={columns}
                pagination={{ pageSize: 50 }}
                dataSource={_.data || []}
                scroll={{ x: "max-content" }}
              />
            ),
          }
        })}
      />


    </Layout>
  )
}

export default TeamPlanningPage
