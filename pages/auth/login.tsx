// AuthenticationImage.tsx
/**
 * This component renders an authentication page with a background image.
 * It includes a login form with email and password inputs, and a submission
 * button. Notifications are displayed based on the login attempt's success or failure.
 */

import React from "react";
import { BlitzPage } from "@blitzjs/next";
import { useRouter } from "next/router";
import { useForm } from "@mantine/form";
import { useMutation } from "@blitzjs/rpc";
import { showNotification } from "@mantine/notifications";
import login from "app/auth/mutations/login";
import {
  Paper,
  createStyles,
  TextInput,
  PasswordInput,
  Button,
  Title,
  Text,
  Anchor,
  HoverCard,
  Group,
} from "@mantine/core";

// Styles
const useStyles = createStyles((theme) => ({
  wrapper: {
    minHeight: "100vh",
    backgroundSize: "cover",
    overflow: "hidden",
    backgroundImage: "url(https://s.xinc818.com/files/webcilyjtsv0mwi80ro/flow_login_bg.jpg)",
  },
  formContainer: {
    borderRight: `1px solid ${theme.colorScheme === "dark" ? theme.colors.dark[7] : theme.colors.gray[3]}`,
    minHeight: "100vh",
    maxWidth: 400,
    padding: theme.spacing.xl,
    [`@media (max-width: ${theme.breakpoints.sm}px)`]: {
      maxWidth: "100%",
    },
  },
  header: {
    color: theme.colorScheme === "dark" ? theme.white : theme.black,
    fontFamily: `Greycliff CF, ${theme.fontFamily}`,
  },
  logo: {
    color: theme.colorScheme === "dark" ? theme.white : theme.black,
    width: 120,
    display: "block",
    marginLeft: "auto",
    marginRight: "auto",
  },
}));

// Authentication Image Component
const AuthenticationImage: BlitzPage = () => {
  const { classes } = useStyles();
  const router = useRouter();
  const [loginMutation] = useMutation(login);

  const form = useForm({
    initialValues: {
      email: "",
      password: "",
    },
  });

  const handleLogin = async (values: { email: string; password: string }) => {
    showNotification({
      title: "Notification",
      message: "正在验证ing! 🤥",
    });
    try {
      const user = await loginMutation(values);
      showNotification({
        title: "Notification",
        message: user ? "登录啦! 🤥" : "登录失败! 🤥",
      });
      if (user) {
        setTimeout(() => {
          void router.push("/home");
        }, 300);
      }
    } catch (error) {
      showNotification({
        title: "Notification",
        message: "登录异常! 🤥",
      });
    }
  };

  return (
    <div className={classes.wrapper}>
      <Paper className={classes.formContainer} radius={0} p={30}>
        <Title order={2} className={classes.header} align="center" mt="md" mb={50}>
          Welcome back to Flow!
        </Title>

        <form onSubmit={form.onSubmit(handleLogin)}>
          <TextInput
            label="账户名"
            placeholder="username"
            size="md"
            {...form.getInputProps("email")}
          />
          <PasswordInput
            label="密码"
            placeholder="Your password"
            mt="md"
            size="md"
            {...form.getInputProps("password")}
          />
          <Group position="right" mt="md">
            <Button type="submit" fullWidth mt="xl" size="md">
              登录
            </Button>
          </Group>
        </form>

        <Text align="center" mt="md">
          假如您忘记了企业内网账号?{" "}
          <HoverCard width={280} shadow="md">
            <HoverCard.Target>
              <Anchor<"a"> href="#" weight={700} onClick={(event) => event.preventDefault()}>
                找回
              </Anchor>
            </HoverCard.Target>
            <HoverCard.Dropdown>
              <Text size="sm">登录账号同内网Wiki账号，如没有请联系技术部运维申请。</Text>
            </HoverCard.Dropdown>
          </HoverCard>
        </Text>
      </Paper>
    </div>
  );
};

export default AuthenticationImage;
