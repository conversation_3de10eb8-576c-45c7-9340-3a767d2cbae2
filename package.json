{"name": "blitz-upload", "version": "1.0.0", "scripts": {"dev": "DISABLE_CSRF_PROTECTION=true blitz dev -p 3003", "build": "blitz build", "mv": "rm -rf .next && mv .next_build .next", "start": "blitz start --port 7770", "prepare": "husky install", "lint": "eslint --ignore-path .gitignore --ext .js,.ts,.tsx .", "test": "jest", "test:watch": "jest --watch", "pm2:start": "pm2 start npm --name blitzFlow -- run start", "pm2:stop": "pm2 stop blitzFlow && pm2 delete blitzFlow"}, "prisma": {"schema": "db/schema.prisma"}, "prettier": {"semi": false, "printWidth": 100}, "lint-staged": {"*.{js,ts,tsx}": ["eslint --fix"]}, "dependencies": {"@ant-design/charts": "1.4.2", "@ant-design/icons": "4.7.0", "@babel/core": "^7.20.7", "@babel/generator": "^7.20.7", "@babel/parser": "^7.20.7", "@blitzjs/auth": "2.0.0-beta.11", "@blitzjs/next": "2.0.0-beta.11", "@blitzjs/rpc": "2.0.0-beta.11", "@codemirror/legacy-modes": "^6.3.2", "@fortawesome/fontawesome-svg-core": "6.4.0", "@fortawesome/free-solid-svg-icons": "6.4.0", "@fortawesome/react-fontawesome": "0.2.0", "@headlessui/react": "1.7.19", "@heroicons/react": "2.0.17", "@mantine/core": "5.8.2", "@mantine/dropzone": "5.10.0", "@mantine/ds": "5.5.4", "@mantine/form": "5.6.2", "@mantine/hooks": "5.8.2", "@mantine/modals": "5.6.0", "@mantine/notifications": "5.6.0", "@mantine/prism": "5.6.0", "@mantine/tiptap": "5.8.2", "@mdx-js/loader": "^2.3.0", "@mdx-js/react": "^2.3.0", "@next/mdx": "13.3.1", "@prisma/client": "4.0.0", "@tabler/icons": "1.112.0", "@tailwindcss/aspect-ratio": "0.4.2", "@tiptap/extension-image": "2.0.0-beta.207", "@tiptap/extension-link": "2.0.0-beta.207", "@tiptap/pm": "2.0.3", "@tiptap/react": "2.0.0-beta.207", "@tiptap/starter-kit": "2.0.0-beta.207", "@types/mocha": "^10.0.1", "@uiw/react-codemirror": "^4.19.11", "Blob": "0.10.0", "ahooks": "3.7.8", "ali-oss": "6.17.1", "antd": "5.4.2", "axios": "1.1.3", "axios-cookiejar-support": "5.0.2", "blitz": "2.0.0-beta.11", "clsx": "2.1.1", "codemirror": "5.65.13", "crypto-js": "4.2.0", "dayjs": "1.11.7", "driver.js": "1.2.1", "es7-shim": "^6.0.0", "fast-glob": "^3.2.12", "feed": "4.2.2", "fetch-blob": "4.0.0", "final-form": "4.20.7", "focus-visible": "^5.2.0", "framer-motion": "10.15.1", "fs": "0.0.1-security", "get-tsconfig": "^4.5.0", "js-to-interface": "0.0.6", "js-yaml": "^4.1.0", "jsdom": "24.1.0", "jsencrypt-node": "1.0.4", "jsonwebtoken": "9.0.2", "ldap-authentication": "2.3.1", "ldapjs": "2.3.3", "less": "4.1.3", "md-editor-rt": "3.0.0", "mdast-util-to-string": "^3.2.0", "mdx-annotations": "^0.1.3", "motion": "10.15.5", "multer": "1.4.5-lts.1", "next": "12.2.5", "next-plugin-antd-less": "1.8.0", "next-remove-imports": "1.0.11", "next-themes": "0.2.1", "next-with-less": "2.0.5", "node-rsa": "1.1.1", "oocode": "0.0.9", "postcss-focus-visible": "8.0.2", "prisma": "4.0.0", "prom-client": "14.2.0", "re-resizable": "6.9.17", "react": "18.2.0", "react-dom": "18.2.0", "react-final-form": "6.5.9", "recma-import-images": "^0.0.2", "rehype-autolink-headings": "^6.1.1", "rehype-slug": "^5.1.0", "remark-gfm": "^3.0.1", "remark-rehype-wrap": "^0.0.2", "remark-unwrap-images": "^3.0.1", "shiki": "^0.14.1", "tinify": "1.7.1", "tough-cookie": "4.1.4", "zod": "3.17.3"}, "devDependencies": {"@next/bundle-analyzer": "12.0.8", "@tailwindcss/typography": "^0.5.9", "@testing-library/jest-dom": "5.16.3", "@testing-library/react": "13.4.0", "@testing-library/react-hooks": "8.0.1", "@types/jest": "27.4.1", "@types/node": "17.0.16", "@types/preview-email": "2.0.1", "@types/react": "18.2.0", "@types/react-dom": "18.2.0", "@typescript-eslint/eslint-plugin": "5.30.5", "autoprefixer": "^10.4.14", "babel-plugin-import": "1.13.5", "depcheck": "1.4.7", "eslint": "7.32.0", "eslint-config-next": "12.2.0", "eslint-config-prettier": "8.5.0", "husky": "7.0.4", "jest": "27.5.1", "lint-staged": "12.1.7", "postcss": "^8.4.21", "prettier": "2.8.1", "prettier-plugin-prisma": "3.8.0", "prettier-plugin-tailwindcss": "^0.2.5", "pretty-quick": "3.1.3", "preview-email": "3.0.7", "tailwindcss": "^3.2.7", "ts-jest": "28.0.7", "typescript": "^4.5.3"}, "resolutions": {"@types/react": "18.2.0", "@types/react-dom": "18.2.0", "prosemirror-model": "1.19.0", "dayjs": "1.11.7"}, "private": true}