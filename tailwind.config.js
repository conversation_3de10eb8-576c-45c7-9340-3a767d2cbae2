const colors = require("tailwindcss/colors")
const defaultTheme = require("tailwindcss/defaultTheme")

/** @type {import('tailwindcss').Config} */
module.exports = {
  mode: 'aot',
  darkMode: "class",
  content: [
    "./app/**/*.{js,ts,jsx,tsx,mdx}",
    "./pages/**/*.{js,ts,jsx,tsx,mdx}",
    "./components/**/*.{js,ts,jsx,tsx,mdx}",
    "./src/**/*.{js,ts,jsx,tsx,mdx}",
  ],
  purge: {
    content: [
      "./app/**/*.{js,ts,jsx,tsx,mdx}",
      "./pages/**/*.{js,ts,jsx,tsx,mdx}",
      "./components/**/*.{js,ts,jsx,tsx,mdx}",
      "./src/**/*.{js,ts,jsx,tsx,mdx}",
      './node_modules/oocode/dist/*.{js,jsx}'
    ],
  },
  theme: {
    zIndex: {
      "-1": "-1",
      0: "0",
      10: "10",
      20: "20",
      30: "30",
      40: "40",
      50: "50",
      999: "999",
      1111: "1111",
      auto: "auto",
    },
    extend: {
      borderWidth: {
        '0.5': '0.5px', // 添加自定义的边框宽度
      },
      animation: {
        "spin-around": "spin-around calc(var(--speed) * 2) infinite linear",
        slide: "slide var(--speed) ease-in-out infinite alternate",
      },
      keyframes: {
        "spin-around": {
          "0%": {
            transform: "translateZ(0) rotate(0)",
          },
          "15%, 35%": {
            transform: "translateZ(0) rotate(90deg)",
          },
          "65%, 85%": {
            transform: "translateZ(0) rotate(270deg)",
          },
          "100%": {
            transform: "translateZ(0) rotate(360deg)",
          },
        },
        slide: {
          to: {
            transform: "translate(calc(100cqw - 100%), 0)",
          },
        },
      },
      fontSize: {
        "2xs": ".6875rem",
      },
      spacing: {
        '1.5': '0.47rem', // 这里可以根据需要调整
      },
      fontFamily: {
        sans: ["Inter", ...defaultTheme.fontFamily.sans],
        display: ["Mona Sans", ...defaultTheme.fontFamily.sans],
      },
      textOverflow: {
        ellipsis: 'ellipsis',
      },
      opacity: {
        2.5: "0.025",
        7.5: "0.075",
        15: "0.15",
      },
      backgroundColor: {
        background: "var(--background)",
      },
      webkitScrollbar: {
        base: {
          "&::-webkit-scrollbar": {
            width: "0.4em",
            backgroundColor: "red",
          },
          "&::-webkit-scrollbar-thumb": {
            borderRadius: "0.5rem",
            backgroundColor: "red-500",
          },
        },
        hide: {
          "&::-webkit-scrollbar": {
            display: "none",
          },
        },
      },
      borderColor: {
        "blue-200": "var(--blue-200)",
        "blue-300": "var(--blue-300)",
        "blue-700": "var(--blue-700)",
      },
      maxWidth: {
        container: "1160px", // 对应 .max-w-container 类
      },
      // padding: {
      //   24: "6rem",
      // },
      textColor: {
        "white-40": "rgba(255, 255, 255, 0.4)",
      },
      colors: {
        primary: colors.fuchsia,
        neutral: {
          1000: "#0E0E0E",
          1100: "#050505",
        },
      },
      typography: {
        DEFAULT: {
          css: {
            "code::before": {
              content: '""',
            },
            "code::after": {
              content: '""',
            },
          },
        },
      },
    },
  },
  plugins: [require("@tailwindcss/typography"), require("@tailwindcss/aspect-ratio")],
}
