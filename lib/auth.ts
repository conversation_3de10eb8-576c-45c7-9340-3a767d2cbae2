// lib/auth.js
import jwt from "jsonwebtoken"

const secret = "tims"

export const authenticateJWT = (req, res, next) => {
  const token = req.headers["token"] || "" // 从Authorization头获取Token

  if (!token) {
    return res.status(403).json({ message: "Access denied, no token provided" })
  }

  jwt.verify(token, secret, (err, user) => {
    if (err) {
      return res.status(401).json({ message: "Invalid token" })
    }
    req.user = user // 将用户信息附加到请求对象
    next(user) // 继续执行后续中间件或路由处理
  })
}
