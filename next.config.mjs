// @ts-check

import { withBlitz, setupBlitzServer } from "@blitzjs/next"


import nextMDX from "@next/mdx"
import { remarkPlugins } from "./mdx/remark.mjs"
import { rehypePlugins } from "./mdx/rehype.mjs"
import { recmaPlugins } from "./mdx/recma.mjs"


const withMDX = nextMDX({
  extension: /\.mdx?$/,
  options: {
    remarkPlugins,
    // @ts-ignore
    rehypePlugins,
    recmaPlugins,
    providerImportSource: "@mdx-js/react",
  },
})

/**
 * @type {import('@blitzjs/next').BlitzConfig}
 **/
let config = {
  async rewrites() {
    return [
      {
        source: '/metrics',
        destination: '/api/metrics',
      }
    ]
  },
  pageExtensions: ["js", "jsx", "ts", "tsx", "mdx"],
}

export default withBlitz(withMDX(config))
