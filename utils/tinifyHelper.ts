const tinify = require("tinify")

let currentKeyIndex = 0
const tinyPNGKeys = [
  "Vrxp9frJqZk9HwFr5ly0KQgyH9rQgV4t", //lbq
  // 添加更多备用密钥
]
function updateApiKey() {
  if (currentKeyIndex < tinyPNGKeys.length) {
    tinify.key = tinyPNGKeys[currentKeyIndex]
    currentKeyIndex += 1
  } else {
    console.error("已用完所有 TinyPNG API 密钥")
  }
}

async function compressImage(inputPath, outputPath) {
  try {
    await tinify.fromFile(inputPath).toFile(outputPath)
  } catch (error) {
    if (error instanceof tinify.AccountError) {
      console.error("当前 TinyPNG API 密钥已达使用限制，尝试切换密钥")
      updateApiKey()
      await compressImage(inputPath, outputPath)
    } else {
      console.error(`图片压缩失败：${error.message}`)
    }
  }
}

export default compressImage
