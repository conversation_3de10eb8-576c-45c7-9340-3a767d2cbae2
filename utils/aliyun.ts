import axios from "axios"

// 生成随机名
const randomFileName = (fileName: string) => {
  const name = Date.now().toString(36) + Math.random.toString().substr(2, 6)
  return `${name + fileName.substr(fileName.lastIndexOf("."))}`
}

class AliYunWebForm {
  private getOssToken: (fileName: string) => Promise<any>

  constructor(config: { getOssToken: (fileName: string) => Promise<any> }) {
    this.getOssToken = config.getOssToken
  }

  /**
   * 上传文件
   *
   * @param {File} file
   * @returns
   * @memberof AliYun
   */
  async upload(file: File, offset: any) {
    const {
      data: { entry },
    } = await this.getOssToken(randomFileName(file.name))

    return new Promise((resolve, reject) => {
      let ossResultUrl = ""
      if (file.type.includes("image/")) {
        if (offset && offset.w) {
          ossResultUrl =
            encodeURI(`https://static.xinc818.com/${entry.key}`) +
            `?x-oss-process=image/resize,w_${offset.w * offset.scale},h_${
              offset.h * offset.scale
            }/quality,q_100/sharpen,100/format,png`
        } else {
          ossResultUrl = encodeURI(`https://static.xinc818.com/${entry.key}`)
        }
      } else {
        ossResultUrl = encodeURI(`https://static.xinc818.com/${entry.key}`)
      }

      const form = new FormData()
      form.append("key", entry.key)
      form.append("policy", entry.policy)
      form.append("OSSAccessKeyId", entry.accessId)
      form.append("success_action_status", "200")
      form.append("Signature", entry.signature)
      form.append("file", file)

      fetch(entry.host, { method: "post", body: form, mode: "cors" })
        .then(() => resolve(ossResultUrl))
        .catch((error) => {
          reject(error)
        })
    })
  }
}

const aliYunWebForm = new AliYunWebForm({
  getOssToken: async (fileName: string) => {
    let rssHref = "https://api.xinc818.com"
    return await axios.post(`${rssHref}/rss/public/signature/getSignature`, {
      typeCode: "VIP8_ADMIN_IMAGE_UPLOAD",
      resourceName: fileName,
      createTask: false,
      operator: 123,
    })
  },
})

async function upload(file: File, offset?: any) {
  try {
    const res = await aliYunWebForm.upload(file, offset)
    return res
  } catch (e: any) {
    console.log("阿里云上传失败", e.message)
    return Promise.reject(e)
  }
}

const onUploadImg = async (files, callback) => {
  const res = await Promise.all(
    files.map((file) => {
      return new Promise((rev, rej) => {
        const form = new FormData()
        form.append("file", file)

        upload(file)
          .then((res) => rev(res))
          .catch((error) => rej(error))
      })
    })
  )

  callback(
    res.map((item) => {
      return item
    })
  )
}

export { upload, onUploadImg }
