{"compilerOptions": {"target": "es5", "lib": ["dom", "dom.iterable", "esnext"], "baseUrl": ".", "allowJs": true, "skipLibCheck": true, "strict": false, "strictNullChecks": true, "forceConsistentCasingInFileNames": true, "noEmit": true, "noUncheckedIndexedAccess": true, "esModuleInterop": true, "module": "esnext", "moduleResolution": "node", "resolveJsonModule": true, "isolatedModules": true, "jsx": "preserve", "incremental": true, "tsBuildInfoFile": ".tsbuildinfo", "paths": {"@/*": ["./*"]}}, "exclude": ["node_modules", "**/*.e2e.ts", "cypress"], "include": ["next-env.d.ts", "**/*.ts", "**/*.tsx", "**/*.js", "**/*.jsx", "types", "pages/cms/xinxuan/social/upload.js"]}