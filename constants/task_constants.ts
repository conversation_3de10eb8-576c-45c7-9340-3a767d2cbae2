const taskTypes = {
  type: {
    1: "项目",
    2: "日常",
  },
  category: {
    1: "供应链中台",
    2: "设计师平台",
    3: "集团需求支撑",
    99: "其他",
  },
  classification: {
    1: "业务驱动",
    2: "技术驱动",
  },
  team: {
    "1": "后端开发部",
    "2": "质量保障组",
    "3": "前端组",
    "4": "UED",
    "5": "架构部",
  },
  progressType: {
    1: "未开始",
    2: "进行中",
    3: "已上线",
    4: "已暂停",
  },
  quality: {
    1: "暂无",
    2: "差",
    3: "一般",
    4: "较好",
    5: "好",
  },
}

const getGradient = (value) => {
  switch (value) {
    case 1:
      return { from: "gray", to: "gray" }
    case 2:
      return { from: "yellow", to: "orange", deg: 105 }
    case 3:
      return { from: "teal", to: "lime", deg: 105 }
    case 4:
      return { from: "red", to: "red", deg: 105 }
  }
}

export { taskTypes, getGradient }
