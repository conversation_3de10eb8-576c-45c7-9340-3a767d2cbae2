// todo: 建议改成动态获取员工列表

export type Employee = {
  name: string
  nickname?: string
  team: string
}

// 1: 产品
//
const employees: Employee[] = [
  {
    name: "刘诗言",
    team: "1",
  },
  {
    name: "韩益飞",
    team: "1",
  },
  {
    name: "郁小辉",
    team: "1",
  },
  {
    name: "李冠男",
    team: "1",
  },
  {
    name: "曾飞飞",
    team: "1",
  },
  {
    name: "施韵婕",
    team: "1",
  },
  {
    name: "张倩",
    team: "1",
  },
  {
    name: "童绍乐",
    team: "1",
  },
  {
    name: "王文举",
    team: "1",
  },
  {
    name: "许文培",
    team: "1",
  },
  {
    name: "徐文涛",
    team: "1",
  },
  {
    name: "程大器",
    team: "1",
  },
  {
    name: "张启慧",
    team: "1",
  },
  {
    name: "王海荣",
    team: "1",
  },
  {
    name: "赵昀炜",
    team: "1",
  },

  // 设计师
  {
    name: "刘铭",
    team: "2",
  },
  {
    name: "潘怡伊",
    team: "2",
  },
  {
    name: "杨静",
    team: "2",
  },
  {
    name: "王丽丽",
    team: "2",
  },
  {
    name: "薛娜娜",
    team: "2",
  },
  {
    name: "吴文静",
    team: "2",
  },

  // 前端组
  {
    name: "蒋磊",
    team: "3",
  },
  {
    name: "张傲",
    team: "3",
  },
  {
    name: "李斌权",
    team: "3",
  },
  {
    name: "崔源",
    team: "3",
  },
  {
    name: "王世民",
    team: "3",
  },
  {
    name: "石金杰",
    team: "3",
  },
  {
    name: "文滔",
    team: "3",
  },
  {
    name: "党朝阳",
    team: "3",
  },
  {
    name: "高俊峰",
    team: "3",
  },
  {
    name: "顾孝标",
    team: "3",
  },
  {
    name: "宋行",
    team: "3",
  },
  {
    name: "徐慧圣",
    team: "3",
  },
  {
    name: "徐祥",
    team: "3",
  },
  // 测试
  {
    name: "童红平",
    team: "4",
  },
  {
    name: "陈霞",
    team: "4",
  },
  {
    name: "蒋文亚",
    team: "4",
  },
  {
    name: "李龙",
    team: "4",
  },
  {
    name: "朱建利",
    team: "4",
  },
  {
    name: "曹曙光",
    team: "4",
  },
  {
    name: "汪朝国",
    team: "4",
  },
  {
    name: "冯昌",
    team: "4",
  },
  {
    name: "胡冬冬",
    team: "4",
  },
  {
    name: "尚金霞",
    team: "4",
  },
  {
    name: "孙善明",
    team: "4",
  },
  {
    name: "张广会",
    team: "5",
  },
  {
    name: "陈飞龙",
    team: "5",
  },
  {
    name: "何景辉",
    team: "5",
  },
  {
    name: "李运",
    team: "5",
  },
  {
    name: "王海波",
    team: "5",
  },
  {
    name: "吴强",
    team: "5",
  },
  {
    name: "原浩",
    team: "5",
  },

  // 后端 - 业务中台 (10)
  {
    name: "张久新",
    team: "5",
  },
  {
    name: "陈潜",
    team: "5",
  },
  {
    name: "丁宝",
    team: "5",
  },
  {
    name: "李海超",
    team: "5",
  },
  {
    name: "邵昆昆",
    team: "5",
  },
  {
    name: "苏前坤",
    team: "5",
  },
  {
    name: "王宇",
    team: "5",
  },
  {
    name: "万子聪",
    team: "5",
  },
  {
    name: "张江浩",
    team: "5",
  },
  {
    name: "朱文杰",
    team: "5",
  },

  //后端 - 创新业务 (10)
  {
    name: "张建爽",
    team: "5",
  },
  {
    name: "陈赤",
    team: "5",
  },
  {
    name: "程江波",
    team: "5",
  },
  {
    name: "陈云彬",
    team: "5",
  },
  {
    name: "韩冰",
    team: "5",
  },
  {
    name: "秦金洋",
    team: "5",
  },
  {
    name: "石炳楠",
    team: "5",
  },
  {
    name: "徐文守",
    team: "5",
  },
  {
    name: "张鑫",
    team: "5",
  },
  {
    name: "周中原",
    team: "5",
  },

  //架构部
  {
    name: "孟伟",
    team: "6",
  },
  {
    name: "蔡泽锋",
    team: "6",
  },
  {
    name: "陈振航", // 实习生
    team: "6",
  },
  {
    name: "丁颜杰",
    team: "6",
  },
  {
    name: "董金琳",
    team: "6",
  },
  {
    name: "何阳", //实习生
    team: "6",
  },
  {
    name: "王成华",
    team: "6",
  },
  {
    name: "王攀磊",
    team: "6",
  },
  {
    name: "吴校伟",
    team: "6",
  },
  {
    name: "徐砚鹏",
    team: "6",
  },
  {
    name: "杨冰", 
    team: "6",
  },
  {
    name: "尤家华", 
    team: "6",
  },
  {
    name: "余赞", 
    team: "6",
  },
  {
    name: "周慈航",
    team: "6",
  },
  {
    name: "周梓武",
    team: "6",
  },
  {
    name: "祝建新", 
    team: "6",
  },
]

export function getEmployeesByTeam(
  teamNumber: string
): { label: string; value: string; key: number }[] {
  const employeesByTeam: { label: string; value: string; key: number }[] = []
  employees.forEach((employee, idx) => {
    if (employee.team == teamNumber) {
      employeesByTeam.push({
        label: employee.name,
        value: employee.name,
        key: idx,
      })
    }
  })
  return employeesByTeam
}

