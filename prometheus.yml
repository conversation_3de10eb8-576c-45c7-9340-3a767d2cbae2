global:
  scrape_interval: 15s
  evaluation_interval: 15s

scrape_configs:
  - job_name: prometheus
    static_configs:
      #本地服务器加端口
      - targets: ["localhost:9090"]
        labels:
          instance: prometheus

  - job_name: localhost-node-exporter
    static_configs:
      #监控本地服务器ip+端口，因为是本地docker启动，所以ip使用host.docker.internal
      - targets: ["host.docker.internal:3003"]
        labels:
          instance: localhost-node-exporter
