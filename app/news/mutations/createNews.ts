import { SecurePassword } from "@blitzjs/auth"
import { resolver } from "@blitzjs/rpc"
import db from "db"
import dayjs from "dayjs"

export default async function createFeNews(_: any, ctx: any) {
  let result = await db.fENews.create({
    data: {
      content: _.content,
      authorId: ctx.session.userId,
      timeStr: dayjs(new Date()).format("YYYY-MM-DD HH:mm:ss"),
    },
  })

  return {
    status: true,
    entry: result,
  }
}
