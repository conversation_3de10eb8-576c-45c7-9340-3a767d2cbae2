import { SecurePassword } from "@blitzjs/auth"
import { resolver } from "@blitzjs/rpc"
import db from "db"

export default async function queryAllNews(_: any, ctx: any) {
  const role = ctx.session.role || ""

  const result = await db.fENews.findMany({
    orderBy: {
      createdAt: "desc",
    },
    select: {
      id: true,
      content: true,
      createdAt: true,
      updatedAt: true,
      timeStr: true,
      author: {
        select: {
          id: true,
          name: true,
          email: true,
          avatar: true,
        },
      },
    },
    where: {
      isDeleted: false,
    },
  })

  const result2 = (await db.fEShare.findMany({
    orderBy: {
      createdAt: "desc",
    },
    select: {
      id: true,
      title: true,
      createdAt: true,
      updatedAt: true,
      timeStr: true,
      fe: true,
      author: {
        select: {
          id: true,
          name: true,
          email: true,
          avatar: true,
        },
      },
    },
    where: {
      // pid: _.pid,
      teamPlanType: 7,
      progressType: 3,
      isDeleted: false,
    },
  })) as any

  result.forEach((item: any) => {
    if (ctx.session.userId == item.author?.id) {
      item.showDeleteBtn = true
    }
  })

  let combinedNews = result
  console.log("rolerole~~~", role)
  if (role.includes("FE_DEVELOPER")) {
    combinedNews = result.concat(result2).sort((a: any, b: any) => b.createdAt - a.createdAt)
  }

  return {
    status: true,
    entry: combinedNews,
  }
}
