import { SecurePassword } from "@blitzjs/auth"
import { resolver } from "@blitzjs/rpc"
import { AuthenticationError } from "blitz"
const { authenticate } = require("ldap-authentication")
import db from "db"
import { Role } from "types"
import { Login } from "../validations"

export const authenticateUser = async (rawEmail: string, rawPassword: string) => {
  const { email, password } = Login.parse({ email: rawEmail, password: rawPassword })

  let options = {
    ldapOpts: {
      url: "ldap://ldaps-inc.xinc818.com:389",
    },
    adminDn: "cn=admin-fe,ou=ops,dc=xinxuan,dc=com",
    adminPassword: "3U4G@zyXRg^RWh3eC#2x",
    userSearchBase: "dc=xinxuan,dc=com",
    // ldapOpts: {
    //   url: "ldap://ldap.xinc818.com:1389",
    // },
    // adminDn: "cn=admin-fe,ou=Users,dc=xincheng,dc=org",
    // adminPassword: "3U4G@zyXRg^RWh3eC#2x",
    // userSearchBase: "ou=Users,dc=xincheng,dc=org",
    usernameAttribute: "cn",
    username: email,
    userPassword: password,
  }

  let ldapUser = await authenticate(options)
  // console.log("options", options, ldapUser)

  if (!ldapUser) {
    throw new AuthenticationError()
  }

  console.log("ldapUser", ldapUser)

  const user = await db.user.findFirst({ where: { name: ldapUser.displayName } })
  if (!user) {
    const hashedPassword = await SecurePassword.hash(password.trim())
    const _user = await db.user.create({
      data: {
        name: ldapUser.displayName,
        email: ldapUser.mail,
        hashedPassword,
        role: "USER",
      },
      select: { id: true, name: true, email: true, role: true },
    })

    return _user
  } else {
    let result
    try {
      result = await SecurePassword.verify(user.hashedPassword, password)
      // console.log("222222", user.hashedPassword, password)
      // console.log("auth", result)
    } catch (e) {
      // console.log("result", result)
      // console.log("ee", e, result)
    }

    if (result === SecurePassword.VALID_NEEDS_REHASH) {
      // Upgrade hashed password with a more secure hash
      const improvedHash = await SecurePassword.hash(password)
      // console.log("improvedHash", improvedHash)
      await db.user.update({ where: { id: user.id }, data: { hashedPassword: improvedHash } })
    }

    const { hashedPassword, ...rest } = user
    return rest
  }
}

export default resolver.pipe(resolver.zod(Login), async ({ email, password }, ctx) => {
  // This throws an error if credentials are invalid
  const user = await authenticateUser(email, password)

  await ctx.session.$create({ userId: user.id, role: user.role as Role })

  return user
})
