import { Ctx } from "blitz"

/**
 * 获取当前用户的认证状态和角色信息
 * @param _ 参数占位符，无实际作用
 * @param ctx 上下文对象，包含了当前用户的 Session 信息
 * @returns {Promise<{ status: boolean, entry: string }>} 包含认证状态和角色信息的对象
 */
export default async function getAuthStatus(_ = null, { session }: Ctx) {
  // 通过检查 Session 中的 userId 是否存在来判断用户是否已经认证
  const isAuthenticated = !!session.userId
  // 获取 Session 中存储的角色信息，如果不存在则返回空字符串
  const entry = session.role ?? ""
  return { status: isAuthenticated, entry }
}
