import { SecurePassword } from "@blitzjs/auth"
import { resolver } from "@blitzjs/rpc"
import db from "db"
import { Role } from "types"
import { ImageUrl } from "../validations"

export default resolver.pipe(resolver.zod(ImageUrl), async ({ url }, ctx) => {
  // return null
  // const hashedPassword = await SecurePassword.hash(password.trim())
  const user = await db.asset.create({
    data: { url: url, authorId: ctx.session.userId },
    select: { id: true, url: true },
  })

  // await ctx.session.$create({ userId: user.id, role: user.role as Role })
  return user
})
