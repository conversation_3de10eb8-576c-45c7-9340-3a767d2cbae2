import { SecurePassword } from "@blitzjs/auth"
import { resolver } from "@blitzjs/rpc"
import db from "db"
import { Role } from "types"
import { ImageUrl } from "../validations"
import { gSP } from "../../blitz-server"
import { z } from "zod"



export default resolver.pipe(
  resolver.authorize(),
  // resolver.authorize('admin'),

  async (input, ctx) => {
    let auth = ctx.session.$isAuthorized()
    const data = await db.articleSocial.findMany({})
    return {
      status: true,
      entry: data,
    }
    // stuff
  }
)
