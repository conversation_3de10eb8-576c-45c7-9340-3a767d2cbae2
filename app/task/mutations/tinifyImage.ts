const tinify = require("tinify")

// const keys = ["vXb6B2skJsL0dc80vtysncHJ8dqx9F3P", "vXb6B2skJsL0dc80vtysncHJ8dqx9F3P"] // 你的 API 密钥列表
const keys = [
  "vXb6B2skJsL0dc80vtysncHJ8dqx9F3P", // 我的
  "Vrxp9frJqZk9HwFr5ly0KQgyH9rQgV4t", //lbq
  "qjHvPM0lgxk9TySgcJ7Vc9PqnwB6SQ99", // xhs
]
let currentKeyIndex = 0

const setNextApiKey = () => {
  currentKeyIndex = (currentKeyIndex + 1) % keys.length
  console.log("keys[currentKeyIndex]", keys[currentKeyIndex])
  tinify.key = keys[currentKeyIndex]
}

setNextApiKey() // 设置第一个密钥

export default async function uploadAndCompressImage(_: any, ctx: any) {
  const imageUrl = _.url

  const source = tinify.fromUrl(imageUrl)
  console.log("source", source)
  // source.toFile("optimizedXXXX.jpg")

  const resultData = await source.toBuffer()

  // // 将压缩后的图像数据转换为 base64 编码
  const base64Data = resultData.toString("base64")
  console.log("base64Data", resultData)

  return { status: true, entry: base64Data }
  // try {
  //   const imageUrl = _.url

  //   const source = tinify.fromUrl(imageUrl)
  //   const resultData = await source.toBuffer()

  //   // 将压缩后的图像数据转换为 base64 编码
  //   const base64Data = resultData.toString("base64")
  //   console.log("base64Data", base64Data)

  //   return { status: true, entry: base64Data }
  // } catch (error) {
  //   console.error(error)
  //   return { status: false, message: "图像压缩失败" }
  //   // res.status(500).send({ status: false, message: "图像压缩失败" })
  // }
}
