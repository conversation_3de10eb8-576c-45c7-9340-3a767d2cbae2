import { SecurePassword } from "@blitzjs/auth"
import { resolver } from "@blitzjs/rpc"
import db from "db"
import { Role } from "types"

import dayjs from "dayjs"
// import weekOfYear from 'dayjs/plugin/weekOfYear';
import isoWeek from "dayjs/plugin/isoWeek"

// dayjs.extend(weekOfYear);
dayjs.extend(isoWeek)

function getMondayOfCurrentWeek() {
  return dayjs().startOf("isoWeek")
}

export default async function getTasks(_: any, ctx: any) {
  let arr = [] as any
  let whereObj = {
    data: {},
  } as any
  // if (_.id) {
  //   whereObj.id = parseInt(_.id)
  // }
  if (_.title) {
    // whereObj.data.title = _.title
    arr.push({
      data: {
        path: ["title"],
        string_contains: _.title,
      },
    })
  }
  if (_.category) {
    // whereObj.data.category = _.category
    arr.push({
      data: {
        path: ["category"],
        array_contains: _.category,
      },
    })
  }
  if (_.progressType) {
    if (_.progressType == 99) {
      arr.push({
        data: {
          path: ["progressType"],
          gte: 1,
          lte: 3,
        },
      })
    } else {
      arr.push({
        data: {
          path: ["progressType"],
          gte: 4,
          lte: 4,
        },
      })
    }
  }
  if (_.classification) {
    arr.push({
      data: {
        path: ["classification"],
        array_contains: _.classification,
      },
    })
  }
  if (_.fe) {
    // whereObj.data.fe = {
    //   has: _.fe,
    // }
    arr.push({
      data: {
        path: ["fe"],
        array_contains: _.fe,
      },
    })
  }

  // console.log('projectFilter',_.projectFilter)
  // if (_.projectFilter && _.projectFilter == 2) {
  //   const monday = getMondayOfCurrentWeek();
  //   console.log('monday', monday);
  //   whereObj.data = {
  //     releaseTime: {
  //       gte: monday,
  //     },
  //   };
  // }

  whereObj.isDeleted = false

  console.log("arr", arr)

  let data = await db.task.findMany({
    where: {
      isDeleted: false,
      AND: arr,
    },
    orderBy: {
      createdAt: "desc",
    },
    // where: whereObj,
  })

  // if (_.projectFilter && _.projectFilter == 2) {
  //   data = data.filter((task: any) => {
  //     if (_.projectFilter === 1) {
  //       return true
  //     }
  //     if (_.projectFilter === 2) {
  //       if (!task.data.releaseTime) {
  //         return true
  //       }
  //       const releaseTime = dayjs(task.data.releaseTime).valueOf() as any
  //       console.log(
  //         "zzz",
  //         task.data.title,
  //         dayjs(task.data.releaseTime).format("MM-DD"),
  //         dayjs(getMondayOfCurrentWeek()).format("MM-DD")
  //       )
  //       return releaseTime >= getMondayOfCurrentWeek().valueOf()
  //     }
  //     return false
  //   })
  // }

  if (_.date) {
    data = data.filter((task: any) => {
      let _releaseTime = task.data.releaseTime || dayjs("2026-12-31 00:00:00")
      const releaseTime = dayjs(_releaseTime).format("YYYY-MM-DD 00:00:00") as any
      const nowTime = dayjs(_.date).format("YYYY-MM-DD 00:00:00")
      // console.log(
      //   "task.data.releaseTime",
      //   dayjs(_releaseTime).format("YYYY-MM-DD 00:00:00"),
      //   dayjs(_.date).format("YYYY-MM-DD 00:00:00")
      // )
      if (releaseTime.valueOf() >= nowTime.valueOf()) {
        return true
      }
      return false
    })
  }
  return {
    status: true,
    userName: ctx.session?.user?.name,
    hasAuth: ctx.session.role.indexOf("FE_MASTER") != -1,
    entry: data,
  }
}
