import { SecurePassword } from "@blitzjs/auth"
import { resolver } from "@blitzjs/rpc"
import db from "db"

export default async function queryReport(_: any, ctx: any) {
  // 假设所有团队成员的 ID 是已知的
  const isAdmin = ctx.session.userId == 3

  const teamMembers = [20, 21, 16, 34, 22, 19, 6, 14, 9]

  if (!isAdmin) {
    return {
      status: false,
      message: "权限不足",
    }
  }

  try {
    const reports = await db.teamWeeklyReport.createMany({
      data: teamMembers.map((memberId) => ({
        title: _.title,
        authorId: memberId,
        createdAt: new Date(),
        updatedAt: new Date(),
        isDeleted: false,
      })),
    })

    return {
      status: true,
      entry: reports,
    }
  } catch (error) {
    return {
      status: false,
    }
  }
}
