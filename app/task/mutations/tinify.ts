const tinify = require("tinify")

// const keys = ["vXb6B2skJsL0dc80vtysncHJ8dqx9F3P", "vXb6B2skJsL0dc80vtysncHJ8dqx9F3P"] // 你的 API 密钥列表
const keys = [
  "vXb6B2skJsL0dc80vtysncHJ8dqx9F3P", // 我的
  "Vrxp9frJqZk9HwFr5ly0KQgyH9rQgV4t", //lbq
  "qjHvPM0lgxk9TySgcJ7Vc9PqnwB6SQ99", // xhs

  // "vXb6B2skJsL0dc80vtysncHJ8dqx9F3P", // 我的
  // "qjHvPM0lgxk9TySgcJ7Vc9PqnwB6SQ99", // xhs
  // "Vrxp9frJqZk9HwFr5ly0KQgyH9rQgV4t", //lbq
  // "XXIRu48sw8x3SMA4cA0NixJgib573DPX",
  // "V3Lt8Tm4a8fBcmvyajTxbak5S_bWsi20",
  // "KckuU929qtv_nPK_czL6HKfcAJO9FCKm",
  // "vWIxohgr_6Yte92ee3fB8QJb7K9iH8Ro",
  // "n03RCY69hnW3yGyrz2Sx1dvNNp4NsnVl",
  // "j9b8kB2m2Zx28kk1sN9KFlDHnpR9Mtz6",
  // "wyCb1FTD1bDY8Qj3Bl11CDMqGPCKg0L1",
  // "TrDd55gRdc1RH7K32HYHPvlbx5crb0MT",
  // "VzjzXnvLyZncGtY4HzQFKlXst26mP68G",
]
let currentKeyIndex = 0

const setNextApiKey = () => {
  currentKeyIndex = (currentKeyIndex + 1) % keys.length
  console.log("keys[currentKeyIndex]", keys[currentKeyIndex])
  tinify.key = keys[currentKeyIndex]
}

setNextApiKey() // 设置第一个密钥

export default async function uploadAndCompressImage(_: any, ctx: any) {
  let retries = keys.length
  console.log("uploadAndCompressImage")
  while (retries > 0) {
    try {
      console.log("retries", retries)
      const buffer = Buffer.from(_.image.split(",")[1], "base64")
      const result = await tinify.fromBuffer(buffer)
      const resultData = await result.toBuffer()
      const base64Data = resultData.toString("base64")
      return {
        status: true,
        entry: base64Data,
      }
    } catch (error) {
      console.log("error", error)
      if (error instanceof tinify.AccountError && retries > 1) {
        // 如果是 API 密钥问题，尝试使用下一个密钥
        retries--
        setNextApiKey()
      } else {
        console.log("error222", error)

        // 如果不是密钥问题或已经用完所有密钥，返回详细异常
        return {
          status: false,
          entry: null,
          error: {
            message: error.message,
            type: error.constructor.name,
          },
        }
      }
    }
  }
}
