import { SecurePassword } from "@blitzjs/auth"
import { resolver } from "@blitzjs/rpc"
import db from "db"
import { Role } from "types"

export default async function submitTask(_: any, ctx: any) {

  const role = ctx.session.role || ""
  if (!role.includes("FE_DEVELOPER")) {
    return {
      status: false,
      entry: null,
    }
  }

  if (_.id) {
    const data = await db.task.update({
      data: {
        data: _.data || {},
      },
      where: {
        id: parseInt(_.id),
      },
    })
    return {
      status: true,
      entry: data,
    }
  } else {
    const data = await db.task.create({
      data: {
        data: _.data || {},
      },
    })
    return {
      status: true,
      entry: data,
    }
  }
}
