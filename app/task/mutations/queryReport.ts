import { SecurePassword } from "@blitzjs/auth"
import { resolver } from "@blitzjs/rpc"
import db from "db"
import dayjs from "dayjs"

export default async function queryReport(_: any, ctx: any) {
  const data = await db.teamWeeklyReport.findMany({
    select: {
      id: true,
      title: true,
      createdAt: true,
      content: true,
      author: {
        select: {
          name: true,
          avatar: true,
        },
      },
    },
    orderBy: {
      createdAt: "desc",
    },
  })

  const groupedData = data.reduce((acc, report) => {
    const isCompleted = report.content !== null && report.content.trim() !== ""
    const reportWithCompletion = { ...report, isCompleted, content: "" }

    if (!acc[report.title]) {
      acc[report.title] = []
    }
    acc[report.title].push(reportWithCompletion)
    return acc
  }, {})

  return {
    status: true,
    entry: groupedData,
  }
}
