import { SecurePassword } from "@blitzjs/auth"
import { resolver } from "@blitzjs/rpc"
import db from "db"

export default async function updateReport(_: any, ctx: any) {
  let result
  if (_.id) {
    result = await db.teamWeeklyReport.update({
      data: {
        title: _.title,
        content: _.content,
        weeklyTaskSnapshot: _.weeklyTaskSnapshot || [],
      },
      where: {
        id: parseInt(_.id),
      },
    })
  } else {
    return {
      status: false,
    }
  }

  return {
    status: true,
    entry: result,
  }
}
