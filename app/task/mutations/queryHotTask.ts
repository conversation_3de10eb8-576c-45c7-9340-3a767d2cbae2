import { SecurePassword } from "@blitzjs/auth"
import { resolver } from "@blitzjs/rpc"
import db from "db"
import { Role } from "types"

import dayjs from "dayjs"
// import weekOfYear from 'dayjs/plugin/weekOfYear';
import isoWeek from "dayjs/plugin/isoWeek"

// dayjs.extend(weekOfYear);
dayjs.extend(isoWeek)

function getMondayOfCurrentWeek() {
  return dayjs().startOf("isoWeek")
}

export default async function getTasks(_: any, ctx: any) {
  let arr = [] as any
  let whereObj = {
    data: {},
  } as any

  whereObj.isDeleted = false

  console.log("arr", arr)

  let data = await db.task.findMany({
    where: {
      isDeleted: false,
      AND: arr,
    },
    take: 30,
    orderBy: {
      createdAt: "desc",
    },
    // where: whereObj,
  })

  if (_.date) {
    data = data.filter((task: any) => {
      let _releaseTime = task.data.releaseTime || dayjs("2026-12-31 00:00:00")
      const releaseTime = dayjs(_releaseTime).format("YYYY-MM-DD 00:00:00") as any
      const nowTime = dayjs(_.date).format("YYYY-MM-DD 00:00:00")
      // console.log(
      //   "task.data.releaseTime",
      //   dayjs(_releaseTime).format("YYYY-MM-DD 00:00:00"),
      //   dayjs(_.date).format("YYYY-MM-DD 00:00:00")
      // )
      if (releaseTime.valueOf() >= nowTime.valueOf()) {
        return true
      }
      return false
    })
  }
  return {
    status: true,
    entry: data,
  }
}
