import db from "db"

export default async function updateTask(_: any, ctx: any) {
  // 从请求体中获取 apiPath 和新的 state 值
  const { id, apiPath, apiName, newState } = _

  console.log("apiPathapiPathapiPath", apiName)

  // 通过 ID 查找任务，假设 id 是通过请求的 URL 传递的
  const task = await db.task.findUnique({
    where: { id: parseInt(_.id) },
    select: { data: true }, // 仅选择 data 字段
  })

  // console.log("task.data", task.data)

  // 如果任务不存在，返回错误
  if (!task) {
    return {
      status: false,
    }
  }

  // 解析 JSON 字段以获取当前的 aoneList
  const currentData = task.data as any
  const aoneList = currentData.aoneList

  // console.log("currentData", currentData, aoneList)

  // 更新 aoneList 中相应的条目
  const updatedAoneList = aoneList.map((item) =>
    item.apiPath === apiPath && item.apiName === apiName
      ? { ...item, isIntegrated: newState }
      : item
  )

  // 将更新后的 aoneList 写回 JSON 对象
  const updatedData = JSON.stringify({ ...currentData, aoneList: updatedAoneList })

  console.log("updatedDataupdatedData", updatedData)

  // 将更新后的 JSON 保存回数据库
  const updatedTask = await db.task.update({
    where: { id: parseInt(_.id) },
    data: { data: JSON.parse(updatedData) },
  })

  // 返回更新后的任务
  return {
    status: true,
    entry: updatedTask,
  }
}
