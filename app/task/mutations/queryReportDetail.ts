import { SecurePassword } from "@blitzjs/auth"
import { resolver } from "@blitzjs/rpc"
import db from "db"

export default async function queryGptstores(_: any, ctx: any) {
  const data = await db.teamWeeklyReport.findFirst({
    select: {
      id: true,
      title: true,
      content: true,
      weeklyTaskSnapshot: true,
      author: {
        select: {
          name: true,
        },
      },
    },
    where: {
      id: parseInt(_.id),
      // isDeleted: false,
    },
  })

  return {
    status: true,
    entry: data,
  }
}
