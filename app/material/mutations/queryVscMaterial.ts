import { SecurePassword } from "@blitzjs/auth"
import { resolver } from "@blitzjs/rpc"
import db from "db"

export default async function queryMobileWebsites(_: any, ctx: any) {
  let result
  let typeStr
  let whereObj = {} as any

  if (_.title) {
    whereObj.title = {
      contains: _.title,
    }
  }

  whereObj.isDeleted = false

  if (_.id) {
    result = await db.vscMaterial.findFirst({
      where: {
        id: parseInt(_.id),
        // isDeleted: false,
      },
    })
  } else {
    result = await db.vscMaterial.findMany({
      orderBy: {
        createdAt: "desc",
      },
      include: {
        author: true, // 包含关联的JsonConfigGroup数据
      },
    })
  }

  return {
    status: true,
    entry: result,
  }
}
