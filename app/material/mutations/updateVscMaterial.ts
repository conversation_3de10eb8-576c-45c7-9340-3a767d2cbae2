import { SecurePassword } from "@blitzjs/auth"
import { resolver } from "@blitzjs/rpc"
import db from "db"

export default async function updateSharingSession(_: any, ctx: any) {
  let result
  if (_.id) {
    const first = await db.vscMaterial.findFirst({
      where: {
        id: parseInt(_.id),
      },
    })
    if (first && first.authorId != ctx.session.userId && ctx.session.userId != 3) {
      return {
        status: false,
        message: "请联系创建者或管理员，权限不足",
        entry: null,
      }
    }
    result = await db.vscMaterial.update({
      data: {
        name: _.name,
        category: _.category,
        description: _.description,
        cover: _.cover,
        css: _.css,
        content: _.content,
        isXC: _.isXC,
        supportsPreview: _.supportsPreview,
      },
      where: {
        id: parseInt(_.id),
      },
    })
  } else {
    result = await db.vscMaterial.create({
      data: {
        name: _.name,
        category: _.category,
        description: _.description,
        content: _.content,
        cover: _.cover,
        css: _.css,
        isXC: _.isXC,
        supportsPreview: _.supportsPreview,
        authorId: ctx.session.userId,
      },
    })
  }

  return {
    status: true,
    entry: result,
  }
}
