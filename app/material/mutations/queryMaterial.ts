import { SecurePassword } from "@blitzjs/auth"
import { resolver } from "@blitzjs/rpc"
import db from "db"

export default async function querySharingSession(_: any, ctx: any) {
  let result
  let typeStr
  let whereObj = {} as any

  if (_.title) {
    whereObj.title = {
      contains: _.title,
    }
  }

  whereObj.isDeleted = false

  if (_.id) {
    result = await db.material.findFirst({
      where: {
        id: parseInt(_.id),
      },
    })
  } else {
    result = await db.material.findMany({
      where: whereObj,
    })
  }

  return {
    status: true,
    entry: result,
  }
}
