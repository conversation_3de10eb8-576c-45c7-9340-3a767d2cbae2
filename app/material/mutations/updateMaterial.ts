import { SecurePassword } from "@blitzjs/auth"
import { resolver } from "@blitzjs/rpc"
import db from "db"

export default async function updateSharingSession(_: any, ctx: any) {
  let result
  if (_.id) {
    result = await db.material.update({
      data: {
        fe: _.fe,
        name: _.name,
        description: _.description,
        version: _.version,
        category: _.category,
        platforms: _.platforms,
        githubUrl: _.githubUrl,
        npmUrl: _.npmUrl,
        screenshotUrl: _.screenshotUrl,
        content: _.content,
      },
      where: {
        id: parseInt(_.id),
        // isDeleted: false,
      },
    })
  } else {
    result = await db.material.create({
      data: {
        fe: _.fe,
        name: _.name,
        description: _.description,
        version: _.version,
        category: _.category,
        platforms: _.platforms,
        githubUrl: _.githubUrl,
        npmUrl: _.npmUrl,
        screenshotUrl: _.screenshotUrl,
        content: _.content,
      },
    })
  }

  return {
    status: true,
    entry: result,
  }
}
