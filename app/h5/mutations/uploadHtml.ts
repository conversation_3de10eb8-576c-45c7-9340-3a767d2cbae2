import { SecurePassword } from "@blitzjs/auth"
import { resolver } from "@blitzjs/rpc"
import dayjs from "dayjs"
import db from "db"
import { Role } from "types"
import * as OSS from "ali-oss"

export default async function submitJsonConfig(_: any, ctx: any) {
  let client = new OSS({
    dirPath: "assets/flow-h5/", //
    region: "oss-cn-hangzhou",
    accessKeyId: process.env.OSS_ACCESS_KEY_ID || "",
    accessKeySecret: process.env.OSS_ACCESS_KEY_SECRET || "",
    bucket: "public-xincheng-assets",
    maxSize: 50 * 1024 * 1024, //
  })
  async function uploadFile(filePath, fileContent) {
    console.log("uploadFile", uploadFile)
    try {
      const result = await client.put(filePath, Buffer.from(fileContent))
      console.log("文件上传成功:", result)
    } catch (error) {
      console.error("文件上传失败:", error)
    }
  }

  const result = (await db.h5.findFirst({
    where: {
      id: parseInt(_.id),
    },
  })) as any

  if (result && result.authorId != ctx.session.userId && ctx.session.userId != 3) {
    return {
      status: false,
      message: "请联系创建者或管理员，权限不足",
      entry: null,
    }
  }

  // await db.jsonConfig.update({
  //   data: {
  //     publishTime: dayjs(new Date()).format("YYYY-MM-DD HH:mm:ss"),
  //   },
  //   where: { id: parseInt(_.id) },
  // })

  // const jsonConfig = await db.jsonConfig.findFirst({
  //   where: { id: parseInt(_.id) },
  //   include: { JsonConfigGroup: { select: { appName: true } } },
  // })

  // 将 JSON 对象转换为字符串
  const jsonString = `
  <html>
    <body>${result?.html || ""}</body>
    <style>${result?.css || ""}</style>
    <script>${result?.js || ""}</script>
  </html>
`

  if (!result.html || result.html.length < 10) {
    return null
  }
  let targetUrl = `flow-h5/${_.type || "preview"}/${result?.id}/${result.uuid}.html`
  let targetUrlHistory = `flow-h5/history/${result?.id}/${dayjs().format("YYYYMMDD-HHmmss")}.html`

  console.log("targetUrl", targetUrl)

  // 调用上传文件
  try {
    const res = await uploadFile(targetUrl, jsonString)
    if (_.type == "prod") {
      await uploadFile(targetUrlHistory, jsonString)
      await db.h5History.create({
        data: {
          h5Id: parseInt(_.id),
          url: "https://s.xinc818.com/" + targetUrlHistory,
        },
      })
    }
    // console.log("res", res, jsonString)
    return {
      status: true,
      entry: {
        res: res,
        url: `https://s.xinc818.com/flow-h5/${_.type || "preview"}/${result.id}/${
          result.uuid
        }.html`,
      },
    }
  } catch (e) {
    return {
      status: false,
      entry: null,
    }
  }
}
