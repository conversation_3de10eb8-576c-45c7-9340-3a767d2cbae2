import { SecurePassword } from "@blitzjs/auth"
import { resolver } from "@blitzjs/rpc"
import db from "db"

export default async function updateSharingSession(_: any, ctx: any) {
  let result
  if (_.id) {
    const first = await db.h5.findFirst({
      where: {
        id: parseInt(_.id),
      },
    })
    if (first && first.authorId != ctx.session.userId && ctx.session.userId != 3) {
      return {
        status: false,
        message: "请联系创建者或管理员，权限不足",
        entry: null,
      }
    }
    result = await db.h5.update({
      data: {
        title: _.title,
        description: _.description,
        html: _.html,
        css: _.css,
        js: _.js,
        // uuid: _.uuid,
      },
      where: {
        id: parseInt(_.id),
        // isDeleted: false,
      },
    })
  } else {
    result = await db.h5.create({
      data: {
        title: _.title,
        description: _.description,
        html: _.html,
        css: _.css,
        js: _.js,
        uuid: _.uuid,
        authorId: ctx.session.userId,
      },
    })
  }

  return {
    status: true,
    entry: result,
  }
}
