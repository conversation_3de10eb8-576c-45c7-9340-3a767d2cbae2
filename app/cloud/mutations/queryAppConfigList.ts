import { SecurePassword } from "@blitzjs/auth"
import { resolver } from "@blitzjs/rpc"
import db from "db"

export default async function queryAppConfigList(_: any, ctx: any) {
  let result = await db.jsonConfig.findMany({
    where: {
      jsonConfigGroupId: parseInt(_.id),
      isDeleted: false,
    },
    include: {
      JsonConfigGroup: true, // 包含关联的JsonConfigGroup数据
    },
  })

  return {
    status: true,
    entry: result,
  }
}
