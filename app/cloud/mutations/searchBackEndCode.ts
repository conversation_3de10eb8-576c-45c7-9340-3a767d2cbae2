import { SecurePassword } from "@blitzjs/auth"
import { resolver } from "@blitzjs/rpc"
import db from "db"

import axios from "axios"

const GITLAB_API_BASE_URL = "https://gitlab.xinc818.com/api/v4"
const GITLAB_ACCESS_TOKEN = process.env.GITLAB_ACCESS_TOKEN_SEARCH || ""

const PROJECT_MAP = {
  "784": {
    name: "item(物料中心)",
    link: "http://gitlab.xinc818.com/basic-framework/item/-/tree/master/",
  },
  "774": {
    name: "designweb(设计师平台)" /** 已优化 **/,
    link: "http://gitlab.xinc818.com/basic-framework/designweb/-/tree/master/",
  },
  "687": {
    name: "workorderes(售后单系统)" /** 已优化 **/,
    link: "http://gitlab.xinc818.com/customer/workorderes/-/tree/master/",
  },
  "769": {
    name: "labelcenter(标签中心)",
    link: "https://gitlab.xinc818.com/item/labelcenter/-/tree/master/",
  },
  "372": {
    name: "basic-itemcenter(基础商品中心)" /** 已优化 **/,
    link: "https://gitlab.xinc818.com/item/basic-itemcenter/-/tree/master/",
  },
  "797": {
    name: "kunlun-gateway(困仑网关)",
    link: "https://gitlab.xinc818.com/basic-framework/kunlun-gateway/-/tree/master/",
  },
  "848": {
    name: "dump(基础信息录入)",
    link: "http://gitlab.xinc818.com/basic-framework/dump/-/tree/master/",
  },
  "817": {
    name: "fuxi(服务治理平台)",
    link: "https://gitlab.xinc818.com/basic-framework/fuxi/-/tree/master/",
  },
  "287": {
    name: "account(财务系统)",
    link: "https://gitlab.xinc818.com/finance/account/-/tree/master/",
  },
  "80": {
    name: "aibox(AI盒子)",
    link: "https://gitlab.xinc818.com/iot/aibox/-/tree/master/",
  },
  "189": {
    name: "antifraud-content(反舞弊内容)",
    link: "https://gitlab.xinc818.com/antifraud/antifraud-content/-/tree/master/",
  },
  "230": {
    name: "antifraud-data(反舞弊数据)",
    link: "https://gitlab.xinc818.com/antifraud/antifraud-data/-/tree/master/",
  },
  "231": {
    name: "antifraud-dump(反舞弊基础数据)",
    link: "https://gitlab.xinc818.com/antifraud/antifraud-dump/-/tree/master/",
  },
  "198": {
    name: "antifraud-gateway(反舞弊网关)",
    link: "https://gitlab.xinc818.com/antifraud/antifraud-gateway/-/tree/master/",
  },
  "273": {
    name: "atlas(图谱系统)",
    link: "https://gitlab.xinc818.com/item/atlas/-/tree/master/",
  },
  "74": {
    name: "caesar(CAESAR持续交付平台)",
    link: "https://gitlab.xinc818.com/ops/caesar-web/-/tree/master/",
  },
  "147": {
    name: "canal-client-service(canal客户端服务)",
    link: "https://gitlab.xinc818.com/trade/canal-client-service/-/tree/master/",
  },
  "560": {
    name: "community(社区系统)",
    link: "https://gitlab.xinc818.com/community/community/-/tree/master/",
  },
  "19": {
    name: "coms(贸易系统)",
    link: "https://gitlab.xinc818.com/supplierChain/coms/-/tree/master/",
  },
  "272": {
    name: "cps-itemcenter(CPS物料中心)",
    link: "https://gitlab.xinc818.com/item/cps-itemcenter/-/tree/master/",
  },
  "266": {
    name: "customer-manage(客户管理)",
    link: "https://gitlab.xinc818.com/customer/customer-manage/-/tree/master/",
  },
  "685": {
    name: "data-warehouse(数据仓库)",
    link: "https://gitlab.xinc818.com/bigdata/data-warehouse/-/tree/master/",
  },
  "258": {
    name: "dingtalk(钉钉集成)",
    link: "https://gitlab.xinc818.com/base-service/dingtalk-center/-/tree/master/",
  },
  "839": {
    name: "dubbo-keeper(dubbo管理台)",
    link: "https://gitlab.xinc818.com/basic-framework/dubbo-keeper/-/tree/master/",
  },
  "312": {
    name: "export-center(对外接口)",
    link: "https://gitlab.xinc818.com/supplierChain/export-center/-/tree/master/",
  },
  "564": {
    name: "flyflow(飞流系统)",
    link: "https://gitlab.xinc818.com/flyflow/flyflow/-/tree/master/",
  },
  "283": {
    name: "fms-manage(文件服务)",
    link: "https://gitlab.xinc818.com/base-service/fms-manage/-/tree/master/",
  },
  "690": {
    name: "fpreview(采购预览系统)",
    link: "https://gitlab.xinc818.com/purchase/fpreview/-/tree/master/",
  },
  "78": {
    name: "games(游戏平台)",
    link: "https://gitlab.xinc818.com/marketing/games/-/tree/master/",
  },
  "568": {
    name: "gift-group-purchase(礼品团购)",
    link: "https://gitlab.xinc818.com/item/gift-group-purchase/-/tree/master/",
  },
  "683": {
    name: "heavenly-eye(产品天眼)",
    link: "https://gitlab.xinc818.com/item/heavenly-eye/-/tree/master/",
  },
  "257": {
    name: "iam(身份服务)",
    link: "https://gitlab.xinc818.com/base-service/iam/-/tree/master/",
  },
  "281": {
    name: "inventory(库存系统)",
    link: "https://gitlab.xinc818.com/stock/inventory/-/tree/master/",
  },
  "263": {
    name: "investment-center(投资系统)",
    link: "https://gitlab.xinc818.com/investment/investment-center/-/tree/master/",
  },
  "202": {
    name: "item-category(商品类目)",
    link: "https://gitlab.xinc818.com/item/category/-/tree/master/",
  },
  "201": {
    name: "item-comment(商品评论)",
    link: "https://gitlab.xinc818.com/item/comment/-/tree/master/",
  },
  "274": {
    name: "itemcenter(商品中心)",
    link: "https://gitlab.xinc818.com/item/itemcenter/-/tree/master/",
  },
  "176": {
    name: "marketing-business-center(营销业务中心)",
    link: "https://gitlab.xinc818.com/marketing/marketing-business-center/-/tree/master/",
  },
  "142": {
    name: "marketing-center(营销中心)",
    link: "https://gitlab.xinc818.com/marketing/marketing-center/-/tree/master/",
  },
  "807": {
    name: "mid-xxl-job(分布式任务调度平台)",
    link: "https://gitlab.xinc818.com/ops/xxl-job/-/tree/master/",
  },
  "256": {
    name: "msgcenter(消息中心)",
    link: "https://gitlab.xinc818.com/base-service/msgcenter/-/tree/master/",
  },
  "697": {
    name: "msp(多租户管理)",
    link: "https://gitlab.xinc818.com/base-service/msp/-/tree/master/",
  },
  "42": {
    name: "open-wms(开放式WMS)",
    link: "https://gitlab.xinc818.com/supplierChain/open-wms/-/tree/master/",
  },
  "409": {
    name: "opscloud4(运维平台)",
    link: "https://gitlab.xinc818.com/ops/opscloud4-web/-/tree/master/",
  },
  "841": {
    name: "order-fulfilment(订单履约)",
    link: "https://gitlab.xinc818.com/basic-framework/order-fulfilment/-/tree/master/",
  },
  "127": {
    name: "outway(outway)",
    link: "https://gitlab.xinc818.com/outway/outway/-/tree/master/",
  },
  "246": {
    name: "outway-download(outway插件下载)",
    link: "https://gitlab.xinc818.com/outway/outway-download/-/tree/master/",
  },
  "502": {
    name: "outway-jst(outway-JST)",
    link: "https://gitlab.xinc818.com/outway/outway-jst/-/tree/master/",
  },
  "124": {
    name: "payment-center(支付中心)",
    link: "https://gitlab.xinc818.com/payment-center/payment-center/-/tree/master/",
  },
  "259": {
    name: "permission-center(权限中心)",
    link: "https://gitlab.xinc818.com/base-service/permission-center/-/tree/master/",
  },
  "678": {
    name: "pricemanage(价格体系)",
    link: "https://gitlab.xinc818.com/supplierChain/pricemanage/-/tree/master/",
  },
  "264": {
    name: "purchase-center(采购中心)",
    link: "https://gitlab.xinc818.com/purchase/purchase-center/-/tree/master/",
  },
  "75": {
    name: "risk(risk)",
    link: "https://gitlab.xinc818.com/antifraud/risk/-/tree/master/",
  },
  "225": {
    name: "risk-admin(risk后台)",
    link: "https://gitlab.xinc818.com/antifraud/risk-admin/-/tree/master/",
  },
  "115": {
    name: "rms(rms)",
    link: "https://gitlab.xinc818.com/rms/rms/-/tree/master/",
  },
  "159": {
    name: "rss(rss)",
    link: "https://gitlab.xinc818.com/supplierChain/rss/-/tree/master/",
  },
  "275": {
    name: "sale-itemcenter(销售商品中心)",
    link: "https://gitlab.xinc818.com/item/sale-itemcenter/-/tree/master/",
  },
  "295": {
    name: "sample(样品管理)",
    link: "https://gitlab.xinc818.com/supplierChain/sample/-/tree/master/",
  },
  "278": {
    name: "settlement(业务结算)",
    link: "https://gitlab.xinc818.com/base-service/settlement/-/tree/master/",
  },
  "143": {
    name: "shoppingcart(购物车)",
    link: "https://gitlab.xinc818.com/trade/shoppingcart/-/tree/master/",
  },
  "528": {
    name: "single-promotion(单品促销)",
    link: "https://gitlab.xinc818.com/supplierChain/single-promotion/-/tree/master/",
  },
  "282": {
    name: "stockcenter(库存中心)",
    link: "https://gitlab.xinc818.com/stock/stockcenter/-/tree/master/",
  },
  "302": {
    name: "strongbox(strongbox)",
    link: "https://gitlab.xinc818.com/supplierChain/strongbox/-/tree/master/",
  },
  "68": {
    name: "timeout-center(timeout-center)",
    link: "https://gitlab.xinc818.com/trade/timeout-center/-/tree/master/",
  },
  "165": {
    name: "trade(交易系统)",
    link: "https://gitlab.xinc818.com/trade/trade/-/tree/master/",
  },
  "434": {
    name: "trade-dump(trade-dump)",
    link: "https://gitlab.xinc818.com/trade/trade-dump/-/tree/master/",
  },
  "137": {
    name: "trade-order(交易订单)",
    link: "https://gitlab.xinc818.com/trade/trade-order/-/tree/master/",
  },
  "149": {
    name: "trade-order-admin(交易订单管理后台)",
    link: "https://gitlab.xinc818.com/trade/trade-order-admin/-/tree/master/",
  },
  "228": {
    name: "trade-refund-center(交易退款中心)",
    link: "https://gitlab.xinc818.com/trade/trade-refund-center/-/tree/master/",
  },
  "118": {
    name: "trade-search(交易搜索)",
    link: "https://gitlab.xinc818.com/trade/trade-search/-/tree/master/",
  },
  "254": {
    name: "usercenter(用户中心)",
    link: "https://gitlab.xinc818.com/base-service/usercenter/-/tree/master/",
  },
  "261": {
    name: "usercenter-backend(用户中心后端)",
    link: "https://gitlab.xinc818.com/base-service/usercenter-backend/-/tree/master/",
  },
  "689": {
    name: "voc(VOC系统)",
    link: "https://gitlab.xinc818.com/voc/voc/-/tree/master/",
  },
  "40": {
    name: "wms(WMS)",
    link: "https://gitlab.xinc818.com/supplierChain/wms/-/tree/master/",
  },
  "816": {
    name: "xuanyuan(轩辕系统)",
    link: "https://gitlab.xinc818.com/haotian/xuanyuan/-/tree/master/",
  },
  "814": {
    name: "pangu(盘古系统)",
    link: "https://gitlab.xinc818.com/basic-framework/pangu/-/tree/master/",
  },
  "795": {
    name: "timeout(超时处理)",
    link: "https://gitlab.xinc818.com/basic-framework/timeout/-/tree/master/",
  },
  "834": {
    name: "openapi(开放平台)",
    link: "https://gitlab.xinc818.com/basic-framework/openapi/-/tree/master/",
  },
  "865": {
    name: "sentinel(哨兵)",
    link: "https://gitlab.xinc818.com/basic-framework/sentinel-18x/-/tree/master/",
  },
  "853": {
    name: "suanni(suanni)",
    link: "https://gitlab.xinc818.com/supplierChain/suanni/-/tree/master/",
  },
  "876": {
    name: "data-analysis(数据分析)",
    link: "https://gitlab.xinc818.com/basic-framework/data-analysis/-/tree/master/",
  },
  "815": {
    name: "pangu-front(盘古前端)",
    link: "https://gitlab.xinc818.com/basic-framework/pangu-front/-/tree/master/",
  },
  "820": {
    name: "fuxi-front(伏羲前端)",
    link: "https://gitlab.xinc818.com/basic-framework/fuxi-front/-/tree/master/",
  },
  "892": {
    name: "kunlun-vue-front(困仑Vue前端)",
    link: "https://gitlab.xinc818.com/basic-framework/kunlun-vue3-front/-/tree/master/",
  },
  "878": {
    name: "image-center(图片中心)",
    link: "http://gitlab.xinc818.com/supplierChain/image-center/-/tree/master/",
  },
  "881": {
    name: "data-titan(数据仓库)",
    link: "http://gitlab.xinc818.com/bigdata/data-titan/-/tree/master/",
  },
  "67": {
    name: "opscloud(运维平台)",
    link: "http://gitlab.xinc818.com/ops/opscloud/-/tree/master/",
  },
  "844": {
    name: "magician-dispatch(魔方调度中心)",
    link: "http://gitlab.xinc818.com/bigdata/magician-dispatch/-/tree/master/",
  },
  "105": {
    name: "magician(魔方)",
    link: "http://gitlab.xinc818.com/bigdata/magician/-/tree/master/",
  },
  "538": {
    name: "magician-urc(魔方用户画像)",
    link: "http://gitlab.xinc818.com/bigdata/magician-urc/-/tree/master/",
  },
  "106": {
    name: "crawler(爬虫服务)",
    link: "http://gitlab.xinc818.cn/bigdata/crawler/-/tree/master/",
  },
  "835": {
    name: "crawler-danmaku(弹幕爬虫)",
    link: "http://gitlab.xinc818.com/bigdata/crawler-danmaku/-/tree/master/",
  },
  "555": {
    name: "data-carrier(数据运输)",
    link: "http://gitlab.xinc818.com/bigdata/data-carrier/-/tree/master/",
  },
  "215": {
    name: "performance(性能测试)",
    link: "http://gitlab.xinc818.cn/bigdata/performance/-/tree/master/",
  },
  "237": {
    name: "crawler-tools(爬虫工具)",
    link: "http://gitlab.xinc818.cn/bigdata/crawler-tools/-/tree/master/",
  },
  "720": {
    name: "data-platform(数据中台)",
    link: "http://gitlab.xinc818.com/bigdata/data-platform/-/tree/master/",
  },
  "103": {
    name: "data-center(数据中心)",
    link: "http://gitlab.xinc818.com/bigdata/data-center/-/tree/master/",
  },
  "825": {
    name: "data-brain(数据脑)",
    link: "http://gitlab.xinc818.com/bigdata/data-brain/-/tree/master/",
  },
  "529": {
    name: "xxl-job(分布式任务调度平台)",
    link: "http://gitlab.xinc818.com/bigdata/xxl-job-2.2.0/-/tree/master/",
  },
  "187": {
    name: "rmp(任务管理平台)",
    link: "http://gitlab.xinc818.cn/bigdata/rmp/-/tree/master/",
  },
  "292": {
    name: "data-urc(用户画像)",
    link: "http://gitlab.xinc818.com/bigdata/data-urc/-/tree/master/",
  },
  "854": {
    name: "data-link(数据链)",
    link: "http://gitlab.xinc818.com/bigdata/data-link/-/tree/master/",
  },
  "158": {
    name: "sekiro(数据隔离)",
    link: "http://gitlab.xinc818.com/bigdata/sekiro/-/tree/master/",
  },
  "831": {
    name: "xuanyuan-front(轩辕前端)",
    link: "http://gitlab.xinc818.com/haotian/xuanyuan-front/-/tree/master/",
  },
}

const basic_framework_group = [
  "784",
  "774",
  "797",
  "848",
  "817",
  "839",
  "841",
  "834",
  "865",
  "815",
  "820",
  "892",
]

const bigdata_group = [
  "158",
  "854",
  "844",
  "105",
  "538",
  "106",
  "835",
  "555",
  "215",
  "237",
  "720",
  "103",
  "825",
  "529",
  "187",
  "292",
]

const supplierChain_group = ["19", "42", "159", "312", "528", "302", "678", "853", "878"]
const base_service_group = ["258", "256", "283", "259", "278", "254", "261", "697"]

const item_group = ["784", "769", "372", "273", "272", "274", "275", "568", "683"]
const trade_group = ["147", "165", "434", "137", "149", "228", "118", "68"]
const antifraud_group = ["189", "230", "231", "198", "75", "225"]

const customer_group = ["687", "266"]
const marketing_group = ["78", "176", "142"]
const ops_group = ["74", "409", "807", "67"]
const iot_group = ["80"]
const finance_group = ["287"]
const investment_group = ["263"]
const purchase_group = ["690", "264"]
const stock_group = ["281", "282"]
const flyflow_group = ["564"]
const community_group = ["560"]
const haotian_group = ["816", "831"]
const outway_group = ["127", "246", "502"]
const voc_group = ["689"]

export async function getCommitLogs(projectIds, queryStr) {
  const requests = projectIds.map((id) => {
    return axios.get(
      `${GITLAB_API_BASE_URL}/projects/${id}/search?scope=blobs&search=${queryStr}&per_page=30`,
      {
        headers: {
          "PRIVATE-TOKEN": GITLAB_ACCESS_TOKEN,
        },
      }
    )
  })

  const results = await Promise.all(requests)

  return results.map((result, index) => {
    return {
      projectId: projectIds[index],
      projectName: PROJECT_MAP[projectIds[index]].name,
      projectLink: PROJECT_MAP[projectIds[index]].link,
      content: result.data,
    }
  })
}
interface RequestParams {
  group?: string
  search?: string
}

export default async function queryAppList(_: any, ctx: any) {
  const params = _
  let { group, search }: any = params

  // let projectIdsArr = projectIds.split(",") as any
  // console.log("projectIds", projectIds)
  let projectIds = [] as any
  if (group == "basic_framework") {
    projectIds = basic_framework_group.concat(supplierChain_group, base_service_group)
  } else if (group == "item") {
    projectIds = item_group.concat(trade_group, antifraud_group)
  } else if (group == "other") {
    projectIds = customer_group.concat(
      marketing_group,
      ops_group,
      iot_group,
      finance_group,
      investment_group,
      purchase_group,
      stock_group,
      flyflow_group,
      community_group,
      haotian_group,
      outway_group,
      voc_group
    )
  } else if (group == "bidata") {
    projectIds = bigdata_group
  }

  const results = await getCommitLogs(projectIds, search)

  const mappedResults = results.map((result) => {
    return {
      projectId: result.projectId,
      projectName: result.projectName,
      projectLink: result.projectLink,
      content: result.content,
    }
  })

  return {
    status: true,
    entry: mappedResults,
  }
}
