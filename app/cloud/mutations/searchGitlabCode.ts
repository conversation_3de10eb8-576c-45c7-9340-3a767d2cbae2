import { SecurePassword } from "@blitzjs/auth"
import { resolver } from "@blitzjs/rpc"
import db from "db"

import axios from "axios"

const GITLAB_API_BASE_URL = "https://gitlab.xinc818.com/api/v4"
const GITLAB_ACCESS_TOKEN = process.env.GITLAB_ACCESS_TOKEN_SEARCH || ""

const PROJECT_MAP = {
  "768": {
    name: "SSO(供应链登录系统)",
    link: "https://gitlab.xinc818.com/supply-middle/sso/-/tree/master/",
  },
  "456": {
    name: "sso-system(老-供应链登录系统)",
    link: "https://gitlab.xinc818.com/supply-middle/sso-system/-/tree/master/",
  },
  "761": {
    name: "micro-basic(子应用基础架构)",
    link: "https://gitlab.xinc818.com/supply-middle/micro-basic/-/tree/master/",
  },
  "757": {
    name: "supply-new(供应链系统)",
    link: "https://gitlab.xinc818.com/supply-middle/supply-new/-/tree/master/",
  },
  "721": {
    name: "Business Center Pro New(供应商系统)",
    link: "https://gitlab.xinc818.com/web/business-center-pro-new/-/tree/master/",
  },
  "453": {
    name: "customer-service-system(客服系统)",
    link: "https://gitlab.xinc818.com/web/customer-service-system/-/tree/master/",
  },
  "979": {
    name: "service-providers(服务提供商系统)",
    link: "https://gitlab.xinc818.com/web/service-providers/-/tree/master/",
  },
  "708": {
    name: "flyflow-audit(审批中心)",
    link: "https://gitlab.xinc818.com/web/flyflow-audit/-/tree/master/",
  },
  "563": {
    name: "flyflow(飞流审批流)",
    link: "https://gitlab.xinc818.com/web/flyflow/-/tree/master/",
  },
  "688": {
    name: "voc(voc系统)",
    link: "https://gitlab.xinc818.com/web/voc/-/tree/master/",
  },
  "457": {
    name: "apollo(财务中心)",
    link: "https://gitlab.xinc818.com/web/apollo/-/tree/master/",
  },
  "698": {
    name: "partner-supervision(合作商监管系统)",
    link: "https://gitlab.xinc818.com/web/partner-supervision/-/tree/master/",
  },
  "765": {
    name: "purchase-new(采购系统)",
    link: "https://gitlab.xinc818.com/web/purchase-new/-/tree/master/",
  },
  "446": {
    name: "wms(wms)",
    link: "https://gitlab.xinc818.com/web/wms/-/tree/master/",
  },
  "462": {
    name: "wto(wto)",
    link: "https://gitlab.xinc818.com/web/wto/-/tree/master/",
  },
  "442": {
    name: "oms(oms)",
    link: "https://gitlab.xinc818.com/web/oms/-/tree/master/",
  },
  "454": {
    name: "hrm(hrm)",
    link: "https://gitlab.xinc818.com/web/hrm/-/tree/master/",
  },
  "787": {
    name: "jf-sso(设计师-登录sso)",
    link: "https://gitlab.xinc818.com/jf/jf-sso/-/tree/master/",
  },
  "384": {
    name: "export(导出中心)",
    link: "http://gitlab.xinc818.com/web/export/-/tree/master/",
  },
  "561": {
    name: "community-manage(辛喜社区后台)",
    link: "http://gitlab.xinc818.com/community/community-manage/-/tree/master/",
  },
  "880": {
    name: "xselect-operations(辛选运营平台)",
    link: "http://gitlab.xinc818.com/jf/xselect-operations/-/tree/master/",
  },
  "988": {
    name: "healthy-system(健康项目)",
    link: "https://gitlab.xinc818.com/web/healthy-system/-/tree/master/",
  },
  "484": {
    name: "message-center(消息中心)",
    link: "https://gitlab.xinc818.com/web/message-center/-/tree/master/",
  },
  "959": {
    name: "intelligent-office(智能办公)",
    link: "https://gitlab.xinc818.com/web/intelligent-office/-/tree/master/",
  },
  "973": {
    name: "testingVendor-manager(检测商管理系统)",
    link: "https://gitlab.xinc818.com/web/testingVendor-manager/-/tree/master/",
  },
  "975": {
    name: "selection-system(选品后台)",
    link: "https://gitlab.xinc818.com/supply-middle/selection-system/-/tree/master/",
  },
  "1026": {
    name: "agriculture-system(农业系统)",
    link: "https://gitlab.xinc818.com/web/agriculture-system/-/tree/master/",
  },
  "966": {
    name: "service-assistant(客服插件)",
    link: "https://gitlab.xinc818.com/web/extensions/service-assistant/-/tree/master/",
  },
  "843": {
    name: "xlion-monitor(埋点SDK)",
    link: "https://gitlab.xinc818.com/web/sdk/xlion-monitor/-/tree/master/",
  },
  //小程序
  "557": {
    name: "supply-mini(供应链小程序)",
    link: "https://gitlab.xinc818.com/web/supply-mini/-/tree/master/",
  },
  "1024": {
    name: "xlion-mini-agriculture(辛选农业小程序)",
    link: "https://gitlab.xinc818.com/web/xlion-mini-agriculture/-/tree/master/",
  },
  "772": {
    name: "selection-mini(磐石小程序)",
    link: "https://gitlab.xinc818.com/web/selection-miniw/-/tree/master/",
  },
  "974": {
    name: "lion-selection-mini(选品小程序)",
    link: "https://gitlab.xinc818.com/web/lion-selection-mini/-/tree/master/",
  },
  "562": {
    name: "community-webapp(辛喜社区小程序)",
    link: "https://gitlab.xinc818.com/community/community-webapp/-/tree/master/",
  },
  "450": {
    name: "internal-buy-mini(辛选内购小程序)",
    link: "https://gitlab.xinc818.com/web/weapp/internal-buy-mini/-/tree/master/",
  },
  "968": {
    name: "xlion-mini-weightWatchMate(辛选体重秤小程序)",
    link: "https://gitlab.xinc818.com/web/xlion-mini-weightWatchMate/-/tree/master/",
  },
}

const SUPPLY_APP = [
  "768",
  "456",
  "761",
  "757",
  "721",
  "453",
  "979",
  "708",
  "563",
  "688",
  "457",
  "698",
  "765",
  "446",
  "462",
  "442",
  "454",
  "787",
  "384",
  "561",
  "880",
  "988",
  "484",
  "959",
  "973",
  "975",
  "1026",
  "966",
  "843",
]

const JF_APP = ["557", "1024", "772", "974", "562", "450", "968"]

export async function getCommitLogs(projectIds, queryStr) {
  const requests = projectIds.map((id) => {
    return axios.get(
      `${GITLAB_API_BASE_URL}/projects/${id}/search?scope=blobs&search=${queryStr}&per_page=20`,
      {
        headers: {
          "PRIVATE-TOKEN": GITLAB_ACCESS_TOKEN,
        },
      }
    )
  })

  const results = await Promise.all(requests)

  return results.map((result, index) => {
    return {
      projectId: projectIds[index],
      projectName: PROJECT_MAP[projectIds[index]].name,
      projectLink: PROJECT_MAP[projectIds[index]].link,
      content: result.data,
    }
  })
}
interface RequestParams {
  group?: string
  search?: string
}

export default async function queryAppList(_: any, ctx: any) {
  const params = _
  let { group, search }: any = params

  // let projectIdsArr = projectIds.split(",") as any
  // console.log("projectIds", projectIds)
  let projectIds = [] as any
  if (group == "supply") {
    projectIds = SUPPLY_APP
  } else if (group == "jf") {
    projectIds = JF_APP
  }

  const results = await getCommitLogs(projectIds, search)

  const mappedResults = results.map((result) => {
    return {
      projectId: result.projectId,
      projectName: result.projectName,
      projectLink: result.projectLink,
      content: result.content,
    }
  })

  return {
    status: true,
    entry: mappedResults,
  }
}
