import { SecurePassword } from "@blitzjs/auth"
import { resolver } from "@blitzjs/rpc"
import dayjs from "dayjs"
import db from "db"
import { Role } from "types"

export default async function addConfig(_: any, ctx: any) {
  if (_.id && _.name) {
    const fitst = await db.jsonConfig.findFirst({
      where: {
        env: _.name,
        jsonConfigGroupId: parseInt(_.id),
      },
    })
    if (fitst) {
      return {
        status: false,
        entry: null,
        message: "已经有重复的环境配置了",
      }
    }
    const data = await db.jsonConfig.create({
      data: {
        jsonConfigGroupId: parseInt(_.id),
        content: {},
        env: _.name,
      },
    })
    return {
      status: true,
      entry: data,
    }
  }
  return {
    status: false,
    entry: null,
  }
}
