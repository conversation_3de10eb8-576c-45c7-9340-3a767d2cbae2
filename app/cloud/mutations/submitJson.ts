import { SecurePassword } from "@blitzjs/auth"
import { resolver } from "@blitzjs/rpc"
import dayjs from "dayjs"
import db from "db"
import { Role } from "types"

export default async function submitJsonConfig(_: any, ctx: any) {
  const role = ctx.session.role || ""
  const jsonConfigId = parseInt(_.id)
  if (!role.includes("FE_DEVELOPER") && jsonConfigId != 24) {
    return {
      status: false,
      entry: null,
    }
  }
  if (_.id && _.content) {
    const data = await db.jsonConfig.update({
      data: {
        updateTime: dayjs(new Date()).format("YYYY-MM-DD HH:mm:ss"),
        content: _.content || {},
      },
      where: {
        id: jsonConfigId,
      },
    })
    return {
      status: true,
      entry: data,
    }
  }
  return {
    status: false,
    entry: null,
  }
}
