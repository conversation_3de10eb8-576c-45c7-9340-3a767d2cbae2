import { SecurePassword } from "@blitzjs/auth"
import { resolver } from "@blitzjs/rpc"
import dayjs from "dayjs"
import db from "db"
import { Role } from "types"
import * as OSS from "ali-oss"

export default async function submitJsonConfig(_: any, ctx: any) {
  const role = ctx.session.role || ""
  const jsonConfigId = parseInt(_.id)
  if (!role.includes("FE_DEVELOPER") && jsonConfigId != 24) {
    return {
      status: false,
      entry: null,
    }
  }
  let client = new OSS({
    dirPath: "assets/flow-app-config/", //
    region: "oss-cn-hangzhou",
    accessKeyId: process.env.OSS_ACCESS_KEY_ID || "",
    accessKeySecret: process.env.OSS_ACCESS_KEY_SECRET || "",
    bucket: "public-xincheng-assets",
    maxSize: 50 * 1024 * 1024, //
  })
  async function uploadFile(filePath, fileContent) {
    try {
      const result = await client.put(filePath, Buffer.from(fileContent))
      console.log("文件上传成功:", result.url)
    } catch (error) {
      console.error("文件上传失败:", error)
    }
  }

  await db.jsonConfig.update({
    data: {
      publishTime: dayjs(new Date()).format("YYYY-MM-DD HH:mm:ss"),
    },
    where: { id: parseInt(_.id) },
  })

  const jsonConfig = await db.jsonConfig.findFirst({
    where: { id: parseInt(_.id) },
    include: { JsonConfigGroup: { select: { appName: true } } },
  })

  // 将 JSON 对象转换为字符串
  const jsonString = JSON.stringify(_.content)

  if (!jsonConfig) {
    return null
  }
  let targetUrl = `flow-app-config/${jsonConfig.JsonConfigGroup?.appName}/${jsonConfig.env}.json`
  let historyUrl = `flow-app-config/${jsonConfig.JsonConfigGroup?.appName}/history-${
    _.id
  }-${dayjs().format("YYYYMMDD-HHmmss")}.json`

  console.log("targetUrl", targetUrl)

  // 调用上传文件

  console.log("jsonStringjsonString", targetUrl, jsonString)
  try {
    const res = await uploadFile(targetUrl, jsonString)
    console.log("jsonConfig.env", jsonConfig.env, historyUrl)
    if (jsonConfig.env != "dev" && jsonConfig.env != "daily" && jsonConfig.env != "gray") {
      await uploadFile(historyUrl, jsonString)
      await db.jsonHistory.create({
        data: {
          jsonId: parseInt(_.id),
          url: "https://s.xinc818.com/" + historyUrl,
        },
      })
    }

    return {
      status: true,
      entry: {
        res: res,
        url: `https://s.xinc818.com/flow-app-config/${jsonConfig.JsonConfigGroup?.appName}/${jsonConfig.env}.json`,
      },
    }
  } catch (e) {
    return {
      status: false,
      entry: null,
    }
  }
}
