import { SecurePassword } from "@blitzjs/auth"
import { resolver } from "@blitzjs/rpc"
import db from "db"

export default async function queryAppList(_: any, ctx: any) {
  const role = ctx.session.role || ""
  let whereObj = {
    isDeleted: false,
  } as any

  let result = await db.jsonConfigGroup.findMany({
    where: whereObj,
    include: {
      jsonConfigs: {
        where: {
          isDeleted: false,
        },
      },
    },
  })

  return {
    status: true,
    entry: result,
  }
}
