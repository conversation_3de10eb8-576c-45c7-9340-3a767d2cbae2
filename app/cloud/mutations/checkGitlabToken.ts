import { resolver } from "@blitzjs/rpc"
import { 
  validateGitlabToken, 
  testSearchPermission, 
  getUserTokens,
  checkEnvironmentToken 
} from "../utils/tokenValidator"

interface CheckTokenParams {
  action?: 'validate' | 'test-search' | 'list-tokens' | 'check-env'
  projectId?: string
}

export default resolver.pipe(
  async (params: CheckTokenParams = {}, ctx) => {
    const { action = 'validate', projectId = '784' } = params
    
    try {
      // 检查环境变量
      const envCheck = checkEnvironmentToken()
      
      if (!envCheck.hasToken) {
        return {
          status: false,
          message: '环境变量中没有配置GITLAB_ACCESS_TOKEN_SEARCH',
          data: {
            environment: envCheck
          }
        }
      }
      
      const token = process.env.GITLAB_ACCESS_TOKEN_SEARCH!
      
      switch (action) {
        case 'validate':
          const tokenInfo = await validateGitlabToken(token)
          return {
            status: tokenInfo.isValid,
            message: tokenInfo.isValid ? 'Token有效' : tokenInfo.error,
            data: {
              environment: envCheck,
              tokenInfo
            }
          }
          
        case 'test-search':
          const searchTest = await testSearchPermission(token, projectId)
          return {
            status: searchTest.hasPermission,
            message: searchTest.hasPermission ? '搜索权限正常' : `搜索权限测试失败: ${searchTest.error}`,
            data: {
              environment: envCheck,
              searchTest
            }
          }
          
        case 'list-tokens':
          const tokensResult = await getUserTokens(token)
          return {
            status: tokensResult.success,
            message: tokensResult.success ? '获取Token列表成功' : tokensResult.error,
            data: {
              environment: envCheck,
              tokens: tokensResult.tokens
            }
          }
          
        case 'check-env':
          return {
            status: true,
            message: '环境变量检查完成',
            data: {
              environment: envCheck
            }
          }
          
        default:
          return {
            status: false,
            message: '未知的检查类型',
            data: null
          }
      }
    } catch (error: any) {
      console.error('Token检查失败:', error)
      return {
        status: false,
        message: `检查失败: ${error.message}`,
        data: {
          error: error.message
        }
      }
    }
  }
)
