// 搜索配置文件
export const SEARCH_CONFIG = {
  // 并发限制
  MAX_CONCURRENT_REQUESTS: 5,
  
  // 缓存设置
  CACHE_DURATION: 5 * 60 * 1000, // 5分钟
  
  // 请求设置
  REQUEST_TIMEOUT: 10000, // 10秒
  
  // 频率限制
  MIN_SEARCH_INTERVAL: 3000, // 3秒
  MIN_SEARCH_LENGTH: 2, // 最少搜索字符数
  
  // 分页设置
  PER_PAGE: 30,
  
  // 通知设置
  NOTIFICATION_DURATION: {
    SUCCESS: 3000,
    ERROR: 5000,
    WARNING: 2000,
    SEARCHING: 15000,
  }
}

// 项目组配置
export const PROJECT_GROUPS = {
  basic_framework: {
    name: "基础框架组",
    description: "basic_framework / supplierChain / base_service",
    estimatedProjects: 29
  },
  item: {
    name: "商品组",
    description: "item / trade / antifraud",
    estimatedProjects: 23
  },
  bigdata: {
    name: "大数据组", 
    description: "bigdata",
    estimatedProjects: 16
  },
  other: {
    name: "其他组",
    description: "customer / marketing / purchase / outway 等",
    estimatedProjects: 30
  }
}

// 错误消息
export const ERROR_MESSAGES = {
  FREQUENT_SEARCH: "请稍等片刻再搜索 ⏰",
  SHORT_QUERY: "搜索内容至少需要2个字符 📝",
  NETWORK_ERROR: "网络请求失败，请稍后重试 ❌",
  NO_RESULTS: "未找到相关结果 😔",
  SEARCH_FAILED: "搜索失败，请检查网络连接"
}

// 成功消息
export const SUCCESS_MESSAGES = {
  SEARCH_COMPLETE: (count: number) => `找到 ${count} 个结果 ✅`,
  CACHE_HIT: "返回缓存结果 ⚡",
  SEARCH_STARTED: "系统搜索中.. 🐟 (限制并发请求，请耐心等待)"
}
