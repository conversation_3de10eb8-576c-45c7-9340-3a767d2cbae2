import axios from 'axios'

const GITLAB_API_BASE_URL = "https://gitlab.xinc818.com/api/v4"

export interface TokenInfo {
  isValid: boolean
  tokenId?: string
  name?: string
  scopes?: string[]
  expiresAt?: string
  createdAt?: string
  lastUsedAt?: string
  active?: boolean
  error?: string
}

/**
 * 验证GitLab Token是否有效
 * @param token GitLab访问令牌
 * @returns Token信息
 */
export async function validateGitlabToken(token: string): Promise<TokenInfo> {
  if (!token) {
    return {
      isValid: false,
      error: 'Token为空'
    }
  }

  try {
    // 获取当前token信息
    const response = await axios.get(`${GITLAB_API_BASE_URL}/personal_access_tokens/self`, {
      headers: {
        'PRIVATE-TOKEN': token,
      },
      timeout: 10000,
    })

    const tokenData = response.data
    
    return {
      isValid: true,
      tokenId: tokenData.id,
      name: tokenData.name,
      scopes: tokenData.scopes,
      expiresAt: tokenData.expires_at,
      createdAt: tokenData.created_at,
      lastUsedAt: tokenData.last_used_at,
      active: tokenData.active,
    }
  } catch (error: any) {
    console.error('Token验证失败:', error.response?.data || error.message)
    
    if (error.response?.status === 401) {
      return {
        isValid: false,
        error: 'Token无效或已过期'
      }
    }
    
    if (error.response?.status === 403) {
      return {
        isValid: false,
        error: 'Token权限不足'
      }
    }
    
    return {
      isValid: false,
      error: `验证失败: ${error.message}`
    }
  }
}

/**
 * 测试Token的搜索权限
 * @param token GitLab访问令牌
 * @param projectId 测试项目ID
 * @returns 是否有搜索权限
 */
export async function testSearchPermission(token: string, projectId: string = '784'): Promise<{
  hasPermission: boolean
  error?: string
}> {
  try {
    const response = await axios.get(
      `${GITLAB_API_BASE_URL}/projects/${projectId}/search?scope=blobs&search=test&per_page=1`,
      {
        headers: {
          'PRIVATE-TOKEN': token,
        },
        timeout: 10000,
      }
    )
    
    return {
      hasPermission: true
    }
  } catch (error: any) {
    console.error('搜索权限测试失败:', error.response?.data || error.message)
    
    return {
      hasPermission: false,
      error: error.response?.data?.message || error.message
    }
  }
}

/**
 * 获取用户的所有Personal Access Tokens
 * @param token GitLab访问令牌
 * @returns Token列表
 */
export async function getUserTokens(token: string): Promise<{
  success: boolean
  tokens?: any[]
  error?: string
}> {
  try {
    // 先获取当前用户信息
    const userResponse = await axios.get(`${GITLAB_API_BASE_URL}/user`, {
      headers: {
        'PRIVATE-TOKEN': token,
      },
      timeout: 10000,
    })
    
    const userId = userResponse.data.id
    
    // 获取用户的tokens（需要admin权限或者是自己的tokens）
    const tokensResponse = await axios.get(`${GITLAB_API_BASE_URL}/users/${userId}/personal_access_tokens`, {
      headers: {
        'PRIVATE-TOKEN': token,
      },
      timeout: 10000,
    })
    
    return {
      success: true,
      tokens: tokensResponse.data
    }
  } catch (error: any) {
    console.error('获取Token列表失败:', error.response?.data || error.message)
    
    return {
      success: false,
      error: error.response?.data?.message || error.message
    }
  }
}

/**
 * 检查环境变量中的token
 * @returns 环境变量token信息
 */
export function checkEnvironmentToken(): {
  hasToken: boolean
  tokenPreview?: string
  source: string
} {
  const token = process.env.GITLAB_ACCESS_TOKEN_SEARCH
  
  if (!token) {
    return {
      hasToken: false,
      source: 'GITLAB_ACCESS_TOKEN_SEARCH环境变量'
    }
  }
  
  // 只显示token的前4位和后4位
  const tokenPreview = token.length > 8 
    ? `${token.substring(0, 4)}...${token.substring(token.length - 4)}`
    : '****'
  
  return {
    hasToken: true,
    tokenPreview,
    source: 'GITLAB_ACCESS_TOKEN_SEARCH环境变量'
  }
}
