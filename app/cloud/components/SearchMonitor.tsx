import React from 'react'
import { Card, Statistic, Progress, Tag, Space, Tooltip } from 'antd'
import { ClockCircleOutlined, DatabaseOutlined, SearchOutlined, WarningOutlined } from '@ant-design/icons'
import { PROJECT_GROUPS } from '../config/searchConfig'

interface SearchMonitorProps {
  loading: boolean
  selectedGroup: string
  searchResults?: any[]
  lastSearchTime: number
}

const SearchMonitor: React.FC<SearchMonitorProps> = ({
  loading,
  selectedGroup,
  searchResults = [],
  lastSearchTime
}) => {
  const groupInfo = PROJECT_GROUPS[selectedGroup] || PROJECT_GROUPS.basic_framework
  const totalResults = searchResults.reduce((sum, item) => sum + (item.content?.length || 0), 0)
  const errorCount = searchResults.filter(item => item.error).length
  const successCount = searchResults.length - errorCount

  const getStatusColor = () => {
    if (loading) return 'processing'
    if (errorCount > 0) return 'warning'
    if (totalResults > 0) return 'success'
    return 'default'
  }

  const formatLastSearchTime = () => {
    if (!lastSearchTime) return '未搜索'
    const now = Date.now()
    const diff = now - lastSearchTime
    if (diff < 60000) return `${Math.floor(diff / 1000)}秒前`
    if (diff < 3600000) return `${Math.floor(diff / 60000)}分钟前`
    return `${Math.floor(diff / 3600000)}小时前`
  }

  return (
    <Card 
      size="small" 
      title={
        <Space>
          <SearchOutlined />
          搜索状态监控
        </Space>
      }
      style={{ marginBottom: 16 }}
    >
      <div style={{ display: 'flex', gap: 16, flexWrap: 'wrap' }}>
        <Statistic
          title="当前项目组"
          value={groupInfo.name}
          prefix={<DatabaseOutlined />}
          valueStyle={{ fontSize: 14 }}
        />
        
        <Statistic
          title="预计项目数"
          value={groupInfo.estimatedProjects}
          suffix="个"
          valueStyle={{ fontSize: 14 }}
        />
        
        <Statistic
          title="搜索结果"
          value={totalResults}
          suffix="条"
          valueStyle={{ 
            fontSize: 14,
            color: totalResults > 0 ? '#52c41a' : '#8c8c8c'
          }}
        />
        
        <Statistic
          title="上次搜索"
          value={formatLastSearchTime()}
          prefix={<ClockCircleOutlined />}
          valueStyle={{ fontSize: 14 }}
        />
      </div>

      {searchResults.length > 0 && (
        <div style={{ marginTop: 16 }}>
          <Space>
            <Tag color="green">成功: {successCount}</Tag>
            {errorCount > 0 && (
              <Tooltip title="部分项目请求失败，可能是网络问题或项目不存在">
                <Tag color="orange" icon={<WarningOutlined />}>
                  失败: {errorCount}
                </Tag>
              </Tooltip>
            )}
            <Tag color={getStatusColor()}>
              状态: {loading ? '搜索中' : '完成'}
            </Tag>
          </Space>
          
          {loading && (
            <div style={{ marginTop: 8 }}>
              <Progress 
                percent={100} 
                status="active" 
                showInfo={false}
                size="small"
              />
              <div style={{ fontSize: 12, color: '#666', marginTop: 4 }}>
                正在搜索 {groupInfo.estimatedProjects} 个项目，限制并发请求中...
              </div>
            </div>
          )}
        </div>
      )}
    </Card>
  )
}

export default SearchMonitor
