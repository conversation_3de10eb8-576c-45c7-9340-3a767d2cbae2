import { SecurePassword } from "@blitzjs/auth"
import { resolver } from "@blitzjs/rpc"
import db from "db"

export default async function updateSharingSession(_: any, ctx: any) {
  let result
  if (_.id) {
    result = await db.fEShare.update({
      data: {
        fe: Array.isArray(_.fe) ? _.fe.join("、") : _.fe,
        pic: _.pic,
        title: _.title,
        content: _.content,
        type: _.type,
        progressType: _.progressType,
        teamPlanType: _.teamPlanType,
        authorId: ctx.session.userId,
      },
      where: {
        id: parseInt(_.id),
        // isDeleted: false,
      },
    })
  } else {
    result = await db.fEShare.create({
      data: {
        fe: Array.isArray(_.fe) ? _.fe.join("、") : _.fe,
        pic: _.pic,
        title: _.title,
        content: _.content,
        progressType: _.progressType,
        teamPlanType: _.teamPlanType,
        type: _.type,
        authorId: ctx.session.userId,
      },
    })
  }

  return {
    status: true,
    entry: result,
  }
}
