import { SecurePassword } from "@blitzjs/auth"
import { resolver } from "@blitzjs/rpc"
import db from "db"

export default async function querySharingSession(_: any, ctx: any) {
  let result
  let typeStr
  let whereObj = {} as any

  if (_.title) {
    whereObj.title = {
      contains: _.title,
    }
  }

  // whereObj.type = 4;
  // whereObj.isDeleted = false

  if (_.id) {
    result = await db.fEShare.findFirst({
      where: {
        id: parseInt(_.id),
        // isDeleted: false,
      },
    })
  } else {
    let user = (await db.user.findFirst({
      where: { id: ctx.session.userId },
    })) as any
    if (user.role === "USER,DESIGNER") {
      result = await db.fEShare.findMany({
        orderBy: [
          {
            progressType: "asc",
          },
          {
            createdAt: "desc",
          },
        ],
        select: {
          id: true,
          createdAt: true,
          title: true,
          isDeleted: true,
          fe: true,
          progressType: true,
          teamPlanType: true,
        },
        where: {
          teamPlanType: { in: [8, 9] },
          type: 4,
          isDeleted: false,
        },
      })
      console.log("result", result)
    } else {
      result = await db.fEShare.findMany({
        orderBy: [
          {
            progressType: "asc",
          },
          {
            createdAt: "desc",
          },
        ],
        select: {
          id: true,
          createdAt: true,
          title: true,
          isDeleted: true,
          fe: true,
          progressType: true,
          teamPlanType: true,
        },
        where: {
          type: 4,
          isDeleted: false,
        },
      })
    }
  }

  return {
    status: true,
    entry: result,
  }
}
