import { SecurePassword } from "@blitzjs/auth"
import { resolver } from "@blitzjs/rpc"
import db from "db"

export default async function querySharingSession(_: any, ctx: any) {
  let result
  let typeStr
  let whereObj = {} as any

  if (_.title) {
    whereObj.title = {
      contains: _.title,
    }
  }

  // whereObj.type = 1
  whereObj.OR = [{ type: 1 }, { type: 2 }, { type: 3 }, { type: 5 }, { type: 6 }]

  whereObj.isDeleted = false

  if (_.id) {
    result = await db.fEShare.findFirst({
      where: {
        id: parseInt(_.id),
        // isDeleted: false,
      },
    })
  } else {
    result = await db.fEShare.findMany({
      orderBy: {
        createdAt: "desc",
      },
      where: whereObj,
    })
  }

  return {
    status: true,
    entry: result,
  }
}
