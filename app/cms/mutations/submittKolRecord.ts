import { SecurePassword } from "@blitzjs/auth"
import { resolver } from "@blitzjs/rpc"
import db from "db"
import { Role } from "types"

export default async function submitArticleSocial(_: any, ctx: any) {
  if (_.id) {
    const data = await db.kolRecord.update({
      data: {
        commerce: _.commerce,
        date: _.date,
        amount: _.amount,
        unit: _.unit,
        imgUrl: _.imgUrl,
        sortNum: _.sortNum,
      },
      where: {
        id: parseInt(_.id),
      },
    })
    return {
      status: true,
      entry: data,
    }
  } else {
    const data = await db.kolRecord.create({
      data: {
        commerce: _.commerce,
        date: _.date,
        amount: _.amount,
        unit: _.unit,
        imgUrl: _.imgUrl,
        sortNum: _.sortNum,
      },
    })
    return {
      status: true,
      entry: data,
    }
  }
}
