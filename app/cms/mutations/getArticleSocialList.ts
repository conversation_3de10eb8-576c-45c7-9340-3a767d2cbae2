import { SecurePassword } from "@blitzjs/auth"
import { resolver } from "@blitzjs/rpc"
import db from "db"
import { Role } from "types"
import { groupBy, sortBy } from "lodash"
import dayjs from "dayjs"
import en from "dayjs/locale/en"
import { e, i } from "@blitzjs/auth/dist/index-57d74361"
import { Console } from "console"

const formatSocial = (data, type) => {
  if (!data) {
    return []
  }
  let arr = [] as any
  if (type == "zh") {
    data.forEach((item: any) => {
      arr.push({
        id: item.id,
        thumb: item.thumb,
        isIgnoreDay: item.isIgnoreDay,
        time: item.isIgnoreDay
          ? dayjs(item.timeStr).format("YYYY年MM月")
          : dayjs(item.timeStr).format("YYYY年MM月DD日"),
        content: item.content,
        sortNum: parseInt(item.sortNum || 999),
      })
    })
  } else {
    data.forEach((item: any) => {
      arr.push({
        id: item.id,
        thumb: item.thumb,
        time: dayjs(item.timeStr).locale("es").format("D MMMM, YYYY"),
        content: !item.content_en || item.content_en == "<p></p>" ? item.content : item.content_en,
        sortNum: parseInt(item.sortNum || 999),
      })
    })
  }
  arr = sortBy(arr, ["sortNum"])
  return arr
}

const formatArticleNews = (data, type, env?) => {
  console.log("formatArticleNews_devv", env)
  if (!data) {
    return []
  }
  let arr = [] as any
  if (type == "zh") {
    data.forEach((item: any, idx) => {
      // if (idx >= 19) {
      //   // console.log(item)
      //   return null
      // }
      if (!item.hide) {
        arr.push({
          id: item.id,
          pic: item.thumb,
          title: item.title,
          info: item.info,
          date: dayjs(item.timeStr).format("YYYY.MM.DD"),
          from: item.from,
          link: item.link,
          content: `<div class="${env}">${item.content}</div>`,
        })
      }
    })
  } else {
    data.forEach((item: any) => {
      if (!item.hide_en) {
        arr.push({
          id: item.id,
          pic: item.thumb,
          title: item.title_en || item.title,
          date: dayjs(item.timeStr).locale("es").format("D MMMM YYYY"),
          info: item.info_en || item.info,
          from: item.from_en || item.from,
          link: item.link,
          content:
            !item.content_en || item.content_en == "<p></p>"
              ? `<div class="${env}">${item.content}</div>`
              : item.content_en,
        })
      }
    })
  }
  return arr
}

const formatKolsDate = (str, type) => {
  if (str.indexOf("20") == -1) {
    return str
  } else {
    if (str.indexOf("年") !== -1) {
      if (type == "en") {
        return str.substr(0, 4)
      } else {
        return str
      }
    } else {
      if (type == "zh") {
        return dayjs(str).format("YYYY.MM.DD")
      } else {
        return dayjs(str).format("D MMMM YYYY")
      }
    }
  }
}

const formatKols = (data, type) => {
  if (!data) {
    return []
  }
  let arr = [] as any
  if (type == "zh") {
    data.forEach((item: any) => {
      let _list = item.list
      if (_list && _list.length > 0) {
        _list.forEach((o) => {
          o.date = o.dateType ? o.year + "年" : formatKolsDate(o.date, "zh")
          o.date = o.customStr || o.date
          console.log("o.date", o.date)
          delete o.content_en
        })
      }
      if (item.sortNum != 999) {
        arr.push({
          id: item.id,
          imgUrl: item.imgUrl,
          name: item.name,
          time: item.timeStr,
          info: item.info,
          intro: item.intro,
          idNum: item.idNum,
          followNum: item.countNum - 0,
          category: item.category,
          recommendAvatar: item.recommendAvatar,
          sellRecommend: item.sellRecommend || false,
          _sortNum: parseInt(item.sortNum), //自己内部排序, 主播介绍的模块
          sortNum: parseInt(item.sortNum2), //给客户端案例排序的
          list: _list,
        })
      }
    })
  } else {
    data.forEach((item: any) => {
      let _list = item.list
      if (_list && _list.length > 0) {
        _list.forEach((j) => {
          j.content == j.content_en || j.content
          j.date = j.dateType ? j.year : formatKolsDate(j.date, "en")
          j.date = j.customStr || j.date
          delete j.content_en
        })
      }
      if (item.sortNum != 999) {
        arr.push({
          id: item.id,
          imgUrl: item.imgUrl,
          name: item.name_en || item.name,
          time: item.timeStr,
          info: item.info_en || item.info,
          intro: item.intro_en || item.intro,
          idNum: item.idNum,
          followNum: item.countNum - 0,
          category: item.category_en || item.category,
          recommendAvatar: item.recommendAvatar,
          sellRecommend: item.sellRecommend || false,
          _sortNum: parseInt(item.sortNum),
          sortNum: parseInt(item.sortNum2),
          list: item.list,
        })
      }
    })
  }
  // console.log("en", arr)
  arr = sortBy(arr, ["_sortNum"])

  return arr
}

function formatFocusNews(dataArr, type) {
  let obj = {}
  let data = dataArr && dataArr.length > 0 ? dataArr[0] : []

  if (type == "zh") {
    obj = {
      id: data.id,
      coverImage: data.coverImage,
      title: data.title,
      date: dayjs(data.timeStr).format("YYYY.MM.DD"),
      from: data.from,
    }
  } else {
    obj = {
      id: data.id,
      coverImage: data.coverImage,
      title: data.title_en,
      date: dayjs(data.timeStr).format("MM.DD.YYYY"),
      from: data.from_en,
    }
  }
  return obj
}

function formatHomeNews(dataArr, type) {
  let arr = [] as any
  let data = dataArr || []

  if (type == "zh") {
    data.forEach((item: any) => {
      if (arr.length >= 3) {
        return
      }
      arr.push({
        id: item.id,
        pic: item.thumb,
        title: item.title,
        time: dayjs(item.timeStr).format("YYYY.MM.DD"),
        year: dayjs(item.timeStr).format("YYYY"),
        month: dayjs(item.timeStr).format("MM"),
        day: dayjs(item.timeStr).format("DD"),
        from: item.from,
        link: item.link,
        content: item.content,
      })
    })
  } else {
    data.forEach((item: any) => {
      if (arr.length >= 3) {
        return
      }
      arr.push({
        id: item.id,
        pic: item.thumb,
        title: item.title_en || item.title,
        time: dayjs(item.timeStr).format("YYYY.MM.DD"),
        year: dayjs(item.timeStr).format("YYYY"),
        month: dayjs(item.timeStr).format("MM"),
        day: dayjs(item.timeStr).format("DD"),
        from: item.from_en || item.from,
        link: item.link,
        content: !item.content_en || item.content_en == "<p></p>" ? item.content : item.content_en,
      })
    })
  }
  return arr
}

export default async function getArticleSocial(_: any, ctx: any) {
  // console.log(")))", ctx)
  // ctx.session.$isAuthorized(true)
  // ctx.session.$setPublicData()
  // ctx.session.$setPrivateData()

  //   const data = await db.articleSocial.findMany({
  //     orderBy: [{ time: "desc" }, { sortTime: "desc" }],
  //   })
  let result = await db.articleSocial.findMany({
    where: {
      isDeleted: false,
    },
    orderBy: [{ time: "desc" }, { sortNum: "desc" }],
  })
  let articleNews =
    (await db.articleNews.findMany({
      where: {
        isDeleted: false,
      },
      orderBy: [{ time: "desc" }, { sortNum: "desc" }],
    })) || []
  let kols = await db.kol.findMany({
    where: {
      isDeleted: false,
    },
    // orderBy: [{ time: "desc" }, { sortNum: "desc" }],
  })

  let focusNews = articleNews.filter((item: any) => {
    return item.isfocusNews == true
  })

  let homeNews = articleNews.filter((item: any) => {
    return item.isHomeNews == true
  })

  let kols2 = await db.kol.findMany({})
  let groupTmp = groupBy(result, "group")
  // let homeNews = [
  //   {
  //     id: 3,
  //     pic: "https://s.xinc818.com/files/webcila7teqcgoptiwq/摘要图",
  //     title: "辛选集团助农卖出3300多吨山西滞销甜脆柿",
  //     time: "2022.11.02",
  //     year: "2022",
  //     month: "11",
  //     day: "02",
  //     from: "央视网",
  //     link: "https://finance.cctv.com/2022/11/02/ARTIzl6v2970pq3eXWLIsMaU221102.shtml",
  //     content:
  //       '<div> 双十一电商大促已经开幕，而在快手辛巴直播间，来自山西运城的阳丰甜脆柿子先火了。近日，在辛巴辛有志的直播间，上演了“爆卖3300多吨”的助农传奇。</div>\n                <div>霜降时节，山西运城临猗县甜脆柿子丰收了，10月24日晚，这款甜蜜的阳丰甜脆柿通过辛巴直播间与全国用户见面了。上架后，下单量迅速跳到超50万单，500多万斤阳丰甜柿一抢而光。截至10月27日，累计销量已超过67万单，重量超3300吨，销售额超1700万元，直播助农见证了真正的“好柿”发生。</div>\n                <div><img src="https://s.xinc818.com/files/webcila7tepqi3b46h5/1.jpeg" /></div>\n                <div>据辛选集团创始人辛有志（网名辛巴）讲述，这次助农直播源于来自山西临猗县的一封信，信中说，今年的阳丰甜柿又是一个丰收年，当地部分阳丰甜柿却面临销售问题，希望辛选助力销售。了解情况后，辛有志立即决定帮农户公益带货阳丰甜柿，他说，“农户一年忙到头，很辛苦，我们会把全部收益返还到农户，辛选一分不留，让农户多赚一些钱。”</div>\n                <div>辛选招商、品控团队立即奔赴山西运城临猗县溯源，“这里的柿子品质非常好，我们助农也坚持严格的质量标准，比如单果大小在70mm以上，柿子的着色超80%，含糖在16以上，全面为产品品质保驾护航，让消费者有更好的消费体验。”辛选相关负责人表示。</div>\n                <div><img src="https://s.xinc818.com/files/webcila7teq6k6kuzj6/2.jpeg" /></div>\n                <div>公开资料显示，辛选集团成立于2017年，是一家以供应链为核心的数字新零售龙头企业。在辛有志的带领下，辛选集团一直将助力乡村振兴作为业务的战略重点，探索出“顶流主播+地标产品+IP升级+直播培训”的直播助农新模式，为各地农产品打造长效上行渠道，已累计助农带货销售额达7.5亿元。</div>\n                <div>农产品是非标产品，品质把控难度较大。一直以来，辛选借助数字化运营优势将直播供应链深入田间地头。辛选打造了一支超1400人的专业选品和品控团队，根据农产品特点，对甜度、口感、新鲜度、农药残留安全标准、包装等数十项细节层层把关，打造更好的购物体验。</div>\n                <div><img src="https://s.xinc818.com/files/webcila7teqa7nzsijm/3.jpeg" /></div>\n                <div>辛有志表示，辛选2000多名客服也将免费帮农户为消费者提供售后服务，全力为柿子的消费体验负责，用户收到柿子，有任何质量问题，反馈后客服立即处理。辛有志还承诺，后续辛选团队将全力以赴，帮助更多农产品打开销路，为各地乡村振兴积极助力。</div>\n                ',
  //   },
  // ]
  // let focusNews = {
  //   id: 34,
  //   coverImage: "https://s.xinc818.com/files/webcil3scgylkzwzqol/new_20211015s3.png",
  //   pic: "https://s.xinc818.com/files/webcil28pipkfvrs0ff/2021-11-5.png",
  //   title: "总台主持人朱迅携手主播辛巴助力进博会，为全球好物“直播带货”",
  //   info: "2021年11月5日晚，中央广播电视总台央视频、CGTN联合举办的《Hi，Go! 博览世界 “进”享好物》专场直播活动在上海举行。总台主持人朱迅携主播辛巴化身“好物推介官”，邀请多国“环球推介官”，推介国家特色，纵览全球好物。",
  //   date: "2021.11.05",
  //   from: "CCTV-4",
  //   link: "https://tv.cctv.com/v/v1/VIDEvVjYRk9SdkuKOjHovoSm211108.html",
  //   content:
  //     '<div>2021年11月5日晚，中央广播电视总台央视频、CGTN联合举办的《Hi，Go! 博览世界 “进”享好物》专场直播活动在上海举行。总台主持人朱迅携主播辛巴化身“好物推介官”，邀请多国“环球推介官”，推介国家特色，纵览全球好物。</div><div>中国中央电视台中文国际频道(CCTV-4)对此进行了相关报道，视频如下：</div><div><video controls="controls" style="width:100%;" src="https://s.xinc818.com/files/webcil1yr5nalxh8tnd/news_video.mp4" poster="https://s.xinc818.com/files/webcil3tk4n18pefhnd/13966803-DFD8-40F6-A15A-AE35EE89E656.png"></video></div>',
  // }

  /**
   *   csjz: "慈善救灾",
          xhzx: "辛火助学",
          xhjh: "辛火计划",
          xczx: "乡村振兴",
          kyjz: "抗疫捐赠",
          qtgy: "其他公益",
   */

  const params = {
    status: true,
    entry: {
      env: _.env,
      zh: {
        social: {
          commitment: [
            {
              type: "csjz",
              typeDesc: "慈善救灾",
              // banner: "https://s.xinc818.com/files/xinxuan-portal-social-commitment-title-1.png",
              list: formatSocial(groupTmp["csjz"], "zh"),
            },
            {
              type: "xhzx",
              typeDesc: "辛火助学",
              // banner: "https://s.xinc818.com/files/xinxuan-portal-social-commitment-title-2.png",
              list: formatSocial(groupTmp["xhzx"], "zh"),
            },
            {
              type: "xhjh",
              typeDesc: "辛火计划",
              list: formatSocial(groupTmp["xhjh"], "zh"),
            },
            {
              type: "xczx",
              typeDesc: "乡村振兴",
              // banner: "https://s.xinc818.com/files/xinxuan-portal-social-commitment-title-4.png",
              list: formatSocial(groupTmp["xczx"], "zh"),
            },
            {
              type: "kyjz",
              typeDesc: "抗疫捐赠",
              // banner: "https://s.xinc818.com/files/xinxuan-portal-social-commitment-title-5.pn",
              list: formatSocial(groupTmp["kyjz"], "zh"),
            },
            {
              type: "qtgy",
              typeDesc: "其他公益",
              // banner: "https://s.xinc818.com/files/xinxuan-portal-social-commitment-title-6.png",
              list: formatSocial(groupTmp["qtgy"], "zh"),
            },
          ],
        },
        group: {
          news: formatArticleNews(articleNews, "zh", _.env),
        },
        kols: formatKols(kols, "zh"),
        home: {
          focusNews: formatFocusNews(focusNews, "zh"),
          news: formatHomeNews(homeNews, "zh"),
        },
      },
      en: {
        social: {
          commitment: [
            {
              type: "csjz",
              typeDesc: "慈善救灾",
              // banner: "https://s.xinc818.com/files/xinxuan-portal-social-commitment-title-1_en.png",
              list: formatSocial(groupTmp["csjz"], "en"),
            },
            {
              type: "xhzx",
              typeDesc: "辛火助学",
              // banner: "https://s.xinc818.com/files/xinxuan-portal-social-commitment-title-2_en.png",
              list: formatSocial(groupTmp["xhzx"], "en"),
            },
            {
              type: "xhjh",
              typeDesc: "辛火计划",
              list: formatSocial(groupTmp["xhjh"], "en"),
            },
            {
              type: "xczx",
              typeDesc: "乡村振兴",
              // banner: "https://s.xinc818.com/files/xinxuan-portal-social-commitment-title-3_en.png",
              list: formatSocial(groupTmp["xczx"], "en"),
            },
            {
              type: "kyjz",
              typeDesc: "抗疫捐赠",
              // banner: "https://s.xinc818.com/files/xinxuan-portal-social-commitment-title-4_en.png",
              list: formatSocial(groupTmp["kyjz"], "en"),
            },
            {
              type: "qtgy",
              typeDesc: "其他公益",
              // banner: "https://s.xinc818.com/files/xinxuan-portal-social-commitment-title-5_en.png",
              list: formatSocial(groupTmp["qtgy"], "en"),
            },
          ],
        },
        group: {
          news: formatArticleNews(articleNews, "en", _.env),
        },
        kols: formatKols(kols2, "en"),
        home: {
          focusNews: formatFocusNews(focusNews, "en"),
          news: formatHomeNews(homeNews, "en"),
        },
      },
    },
  }
  return params
}
