import { SecurePassword } from "@blitzjs/auth"
import { resolver } from "@blitzjs/rpc"
import db from "db"
import { Role } from "types"
import { groupBy } from "lodash"

export default async function getArticleSocial(_: any, ctx: any) {
  let result = await db.articleSocial.findMany({
    orderBy: [{ time: "desc" }, { sortNum: "desc" }],
  })
  let tmp = groupBy(result, "group")
  let group = [
    {
      title: "慈善救灾",
      banner: "https://s.xinc818.com/files/xinxuan-portal-social-commitment-title-1.png",
      list: tmp["csjz"],
    },
    {
      title: "辛火助学",
      banner: "https://s.xinc818.com/files/xinxuan-portal-social-commitment-title-1.png",
      list: tmp["xhzx"],
    },
  ]

  return {
    status: true,
    entry: {
      social: {
        commitment: group,
      },
    },
  }
}
