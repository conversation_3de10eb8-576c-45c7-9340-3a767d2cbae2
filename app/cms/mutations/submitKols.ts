import { SecurePassword } from "@blitzjs/auth"
import { resolver } from "@blitzjs/rpc"
import db from "db"
import { Role } from "types"

export default async function submitKols(_: any, ctx: any) {
  if (_.id) {
    const data = await db.kol.update({
      data: {
        imgUrl: _.imgUrl,
        recommendAvatar: _.recommendAvatar,
        name: _.name,
        name_en: _.name_en,
        info: _.info,
        info_en: _.info_en,
        intro: _.intro,
        intro_en: _.intro_en,
        idNum: _.idNum,
        countNum: _.countNum,
        category: _.category,
        category_en: _.category_en,
        list: _.list,
        sortNum: _.sortNum,
        sortNum2: _.sortNum2,
      },
      where: {
        id: parseInt(_.id),
      },
    })
    return {
      status: true,
      entry: data,
    }
  } else {
    const data = await db.kol.create({
      data: {
        imgUrl: _.imgUrl,
        recommendAvatar: _.recommendAvatar,
        name: _.name,
        name_en: _.name_en,
        info: _.info,
        info_en: _.info_en,
        intro: _.intro,
        intro_en: _.intro_en,
        idNum: _.idNum,
        countNum: _.countNum,
        category: _.category,
        category_en: _.category_en,
        list: _.list,
        sortNum: _.sortNum,
        sortNum2: _.sortNum2,
      },
    })
    return {
      status: true,
      entry: data,
    }
  }
}
