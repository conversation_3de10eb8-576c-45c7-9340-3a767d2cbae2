import { SecurePassword } from "@blitzjs/auth"
import { resolver } from "@blitzjs/rpc"
import db from "db"
import { Role } from "types"

export default async function submitArticleSocial(_: any, ctx: any) {
  if (_.id) {
    const data = await db.articleSocial.update({
      data: {
        content: _.content,
        time: new Date(_.time),
        timeStr: _.time,
        content_en: _.content_en,
        group: _.group,
        thumb: _.thumb,
        sortNum: _.sortNum,
        isIgnoreDay: _.isIgnoreDay,
      },
      where: {
        id: parseInt(_.id),
      },
    })
    return {
      status: true,
      entry: data,
    }
  } else {
    const data = await db.articleSocial.create({
      data: {
        content: _.content,
        time: new Date(_.time),
        timeStr: _.time,
        group: _.group,
        content_en: _.content_en,
        thumb: _.thumb,
        sortNum: _.sortNum,
        isIgnoreDay: _.isIgnoreDay,
      },
    })
    return {
      status: true,
      entry: data,
    }
  }
}
