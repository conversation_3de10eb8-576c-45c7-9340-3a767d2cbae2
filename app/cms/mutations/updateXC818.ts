import { SecurePassword } from "@blitzjs/auth"
import { resolver } from "@blitzjs/rpc"
import db from "db"
import { Role } from "types"
import { groupBy, sortBy } from "lodash"
import dayjs from "dayjs"
import en from "dayjs/locale/en"

export default async function updateXC818(_: any, ctx: any) {
  let kolMatrixResult = await db.kolMatrix.findMany({
    where: {
      isDeleted: false,
    },
    orderBy: [{ sortNum: "asc" }],
  })

  let kolRecordResult = await db.kolRecord.findMany({
    where: {
      isDeleted: false,
    },
    orderBy: [{ date: "desc" }],
  })

  let kolRecordArr = []
  kolRecordResult.forEach((item: any) => {
    item.date = dayjs(item.date).format("YYYY年MM月DD日")
  })

  const params = {
    status: true,
    entry: {
      liver: kolMatrixResult,
      recordList: kol<PERSON>ecordR<PERSON>ult,
    },
  }
  return params
}
