import { SecurePassword } from "@blitzjs/auth"
import { resolver } from "@blitzjs/rpc"
import db from "db"
import { Role } from "types"

export default async function getArticleSocial(_: any, ctx: any) {
  let whereObj = {} as any
  if (_.id) {
    whereObj.id = parseInt(_.id)
  }
  if (_.group) {
    whereObj.group = _.group
  }
  if (_.content) {
    whereObj.content = {
      contains: _.content,
    }
  }

  whereObj.isDeleted = false

  const data = await db.articleSocial.findMany({
    where: whereObj,
    orderBy: [{ time: "desc" }],
  })
  return {
    status: true,
    entry: data,
  }
}
