import { SecurePassword } from "@blitzjs/auth"
import { resolver } from "@blitzjs/rpc"
import db from "db"
import { Role } from "types"
import { groupBy, sortBy } from "lodash"
import dayjs from "dayjs"
import en from "dayjs/locale/en"
import { e, i } from "@blitzjs/auth/dist/index-57d74361"
import { Console } from "console"

const formatArticleNews = (data, type, env?) => {
  if (!data) {
    return []
  }
  let arr = [] as any
  if (type == "zh") {
    data.forEach((item: any, idx) => {
      if (!item.hide) {
        arr.push({
          id: item.id,
          pic: item.thumb,
          title: item.title,
          info: item.info,
          date: dayjs(item.timeStr).format("YYYY.MM.DD"),
          from: item.from,
          link: item.link,
          content: `<div class="${env}">${item.content}</div>`,
        })
      }
    })
  } else {
    data.forEach((item: any) => {
      if (!item.hide_en) {
        arr.push({
          id: item.id,
          pic: item.thumb,
          title: item.title_en || item.title,
          date: dayjs(item.timeStr).locale("es").format("D MMMM YYYY"),
          info: item.info_en || item.info,
          from: item.from_en || item.from,
          link: item.link,
          content:
            !item.content_en || item.content_en == "<p></p>"
              ? `<div class="${env}">${item.content}</div>`
              : item.content_en,
        })
      }
    })
  }
  return arr
}

export default async function getNewsUpdate(_: any, ctx: any) {
  let articleNews =
    (await db.articleNews.findMany({
      where: {
        isDeleted: false,
      },
      take: 10,
      orderBy: [{ time: "desc" }, { sortNum: "desc" }],
    })) || []

  const params = {
    status: true,
    entry: formatArticleNews(articleNews, "zh"),
  }
  return params
}
