import { SecurePassword } from "@blitzjs/auth"
import { resolver } from "@blitzjs/rpc"
import db from "db"
import { Role } from "types"

export default async function getKolRecord(_: any, ctx: any) {
  let hasAuth = ctx.session.role.indexOf("XINXUAN_CMS") != -1 ? true : false
  if (!hasAuth) {
    return {
      status: false,
    }
  }

  let whereObj = {} as any
  let data = {} as any
  if (_.id) {
    whereObj.id = parseInt(_.id)
  }

  if (_.commerce) {
    whereObj.commerce = {
      contains: _.commerce,
    }
  }

  whereObj.isDeleted = false

  if (_.type == "detail") {
    data = await db.kolRecord.findFirst({
      where: {
        id: parseInt(_.id),
      },
    })
  } else {
    data = await db.kolRecord.findMany({
      where: whereObj,
      orderBy: [{ date: "desc" }],
    })
  }

  return {
    status: true,
    entry: data,
  }
}
