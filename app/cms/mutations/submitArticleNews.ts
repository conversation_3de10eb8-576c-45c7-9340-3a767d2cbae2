import { SecurePassword } from "@blitzjs/auth"
import { resolver } from "@blitzjs/rpc"
import db from "db"
import { Role } from "types"

export default async function submitArticleSocial(_: any, ctx: any) {
  if (_.id) {
    const data = await db.articleNews.update({
      data: {
        content: _.content,
        time: new Date(_.time),
        timeStr: _.time,
        title: _.title,
        title_en: _.title_en,
        from: _.from,
        from_en: _.from_en,
        link: _.link,
        content_en: _.content_en,
        thumb: _.thumb,
        info: _.info,
        info_en: _.info_en,
        coverImage: _.coverImage || "",
        isfocusNews: _.isfocusNews,
        isHomeNews: _.isHomeNews,
      },
      where: {
        id: parseInt(_.id),
      },
    })
    console.log("update_data", data)
    return {
      status: true,
      entry: data,
    }
  } else {
    const data = await db.articleNews.create({
      data: {
        content: _.content,
        time: new Date(_.time),
        timeStr: _.time,
        title: _.title,
        title_en: _.title_en,
        from: _.from,
        from_en: _.from_en,
        info: _.info,
        info_en: _.info_en,
        link: _.link,
        content_en: _.content_en,
        thumb: _.thumb,
      },
    })
    return {
      status: true,
      entry: data,
    }
  }
}
