import { SecurePassword } from "@blitzjs/auth"
import { resolver } from "@blitzjs/rpc"
import db from "db"
import { Role } from "types"

export default async function getArticleNews(_: any, ctx: any) {
  let whereObj = {} as any
  if (_.id) {
    whereObj.id = parseInt(_.id)
  }
  if (_.title) {
    whereObj.title = {
      contains: _.title,
    }
  }
  if (_.title_en) {
    whereObj.title_en = {
      contains: _.title_en,
    }
  }

  whereObj.isDeleted = false

  const data = await db.articleNews.findMany({
    where: whereObj,
    orderBy: [{ time: "desc" }],
  })
  return {
    status: true,
    entry: data,
  }
}
