import { SecurePassword } from "@blitzjs/auth"
import { resolver } from "@blitzjs/rpc"
import db from "db"
import { Role } from "types"

export default async function submitKolMatrix(_: any, ctx: any) {
  if (_.id) {
    const data = await db.kolMatrix.update({
      data: {
        name: _.name,
        idNo: _.idNo,
        fans: _.fans,
        avatar: _.avatar,
        sortNum: parseFloat(_.sortNum || 999),
      },
      where: {
        id: parseInt(_.id),
      },
    })
    return {
      status: true,
      entry: data,
    }
  } else {
    const data = await db.kolMatrix.create({
      data: {
        name: _.name,
        idNo: _.idNo,
        fans: _.fans,
        avatar: _.avatar,
        sortNum: parseFloat(_.sortNum || 999),
      },
    })
    return {
      status: true,
      entry: data,
    }
  }
}
