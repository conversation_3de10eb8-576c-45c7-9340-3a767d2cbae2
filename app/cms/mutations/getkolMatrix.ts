import { SecurePassword } from "@blitzjs/auth"
import { resolver } from "@blitzjs/rpc"
import db from "db"
import { Role } from "types"

export default async function getKolMatrix(_: any, ctx: any) {
  let hasAuth = ctx.session.role.indexOf("XINXUAN_CMS") != -1 ? true : false
  if (!hasAuth) {
    return {
      status: false,
    }
  }

  console.log("hasAuth", hasAuth)
  let whereObj = {} as any
  let data = {} as any
  if (_.id) {
    whereObj.id = parseInt(_.id)
  }

  if (_.name) {
    whereObj.name = {
      contains: _.name,
    }
  }

  whereObj.isDeleted = false

  if (_.type == "detail") {
    data = await db.kolMatrix.findFirst({
      where: {
        id: parseInt(_.id),
      },
    })
  } else {
    data = await db.kolMatrix.findMany({
      where: whereObj,
      orderBy: [{ sortNum: "asc" }],
    })
  }

  return {
    status: true,
    entry: data,
  }
}
