//@ts-nocheck
import React, { Suspense, useEffect, useState } from "react"
import { Image, Timeline, Button, Modal, List } from "antd"
import dynamic from "next/dynamic"
import { showNotification } from "@mantine/notifications"

const Bar = dynamic(() => import("@ant-design/charts").then((mode) => mode.Bar), { ssr: false })

const BarChart = ({ info = {}, repoName = "" }) => {
  const config = {
    data: info,
    xField: "count",
    yField: "name",
    seriesField: "type",
    barWidthRatio: 0.8,
    meta: {
      name: {
        alias: "组件名",
      },
      count: {
        alias: "数量",
      },
    },
    scrollbar: {
      type: "vertical",
    },
    interactions: [{ type: "element-active" }],
    minBarWidth: 20,
    maxBarWidth: 20,
  }

  return (
    <div>
      <Bar
        autoFit={true}
        onReady={(plot) => {
          plot.on("plot:click", (evt) => {
            const { x, y } = evt
            const { xField } = plot.options
            const tooltipData = plot.chart.getTooltipItems({ x, y })
            console.log("tooltipData", tooltipData[0].data)
            Modal.info({
              title: tooltipData[0].data.name + " 组件使用详情",
              width: "700px",
              content: (
                <List
                  size="small"
                  bordered
                  dataSource={tooltipData[0].data.reverseDependencies}
                  renderItem={(item: any) => (
                    <List.Item>
                      <a
                        className="flow-shine-link text-gray-600"
                        href={`https://gitlab.xinc818.com/${repoName}/-/tree/master/${item}`}
                        target="_black"
                      >
                        {item}
                      </a>
                    </List.Item>
                  )}
                />
              ),
              maskClosable: true,
              onCancel() { },
              onOk() { }, //
            })
          })
        }}
        {...config}
      />
    </div>
  )
}

export default BarChart
