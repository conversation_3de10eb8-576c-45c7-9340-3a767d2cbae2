import { Suspense, useEffect } from "react"

const Divider = ({ name = "", show = true, classNames = "" }) => {
  return (
    <div className={`relative my-10 ${classNames}`}>
      <div className="absolute inset-0 flex items-center" aria-hidden="true">
        <div className="mx-auto w-full  border-t border-gray-300" />
      </div>
      {show && (
        <div className="relative flex justify-center">
          <span className="bg-white px-2 text-sm text-gray-500">{name}</span>
        </div>
      )}
    </div>
  )
}

export default Divider
