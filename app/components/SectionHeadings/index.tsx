import { Suspense, useEffect } from "react"

const SectionHeadings = ({ name = "", btnText = "", onClick = () => { }, children = null, buttons }: any) => {
  return (
    <div className="mb-5 border-b border-gray-200 pb-3 sm:flex sm:items-end sm:justify-between">
      <h3 className="text-lg font-medium leading-4 text-gray-900">{name}</h3>
      <div className="mt-1 sm:ml-4 sm:mt-0">
        {children}
        {(buttons && buttons?.length > 0) && buttons.map((btn, index) => (
          <button key={index} type="button" className="inline-flex mr-2 items-center rounded-md bg-indigo-600 px-3 py-2 text-sm font-semibold text-white shadow-sm hover:bg-indigo-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2" onClick={btn.onClick} >{btn.text}</button>
        ))}
        {btnText && (
          <button
            type="button"
            onClick={() => {
              void onClick()
            }}
            className="inline-flex items-center rounded-md bg-indigo-600 px-3 py-2 text-sm font-semibold text-white shadow-sm hover:bg-indigo-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2"
          >
            {btnText}
          </button>
        )}
      </div>
    </div>
  )
}

export default SectionHeadings
