import { ChevronRightIcon, HomeIcon } from "@heroicons/react/20/solid"
import { Router, useRouter } from "next/router"

// const pages = [
//   { name: "Projects", href: "#", current: false },
//   { name: "<PERSON> Nero", href: "#", current: true },
// ]

export default function Example({ pages }) {
  const router = useRouter()

  return (
    <nav className="flex" aria-label="Breadcrumb">
      <ol role="list" className="flex items-center space-x-4">
        <li>
          <div>
            <a
              href="javascript:void(0)"
              onClick={() => {
                void router.push({
                  pathname: "/home",
                })
              }}
              className="text-gray-400 hover:text-gray-500"
            >
              <HomeIcon className="h-5 w-5 flex-shrink-0" aria-hidden="true" />
              <span className="sr-only">Home</span>
            </a>
          </div>
        </li>
        {pages &&
          pages.length > 0 &&
          pages.map((page: any) => (
            <li key={page.name}>
              <div className="flex items-center">
                <ChevronRightIcon
                  className="h-5 w-5 flex-shrink-0 text-gray-400"
                  aria-hidden="true"
                />
                <a
                  href={"javascript:void(0)"}
                  onClick={() => {
                    if (page.href) {
                      void router.push({
                        pathname: page.href,
                      })
                    }
                  }}
                  className="ml-4 text-sm font-medium text-gray-500 hover:text-gray-700"
                  aria-current={page.current ? "page" : undefined}
                >
                  {page.name}
                </a>
              </div>
            </li>
          ))}
      </ol>
    </nav>
  )
}
