import { Suspense, useEffect } from "react"

const Bardge = ({ color = "", name = "", key, onClick }: any) => {
  return (
    <span
      key={key}
      onClick={() => {
        if (onClick && typeof onClick === 'function') {
          onClick()
        }
      }}
      className={`mr-2 mt-1 inline-flex items-center ${
        onClick ? "rounded" : "rounded-full"
      } bg-${color}-100 px-2.5 py-0.5 text-xs font-medium text-${color}-800  overflow-ellipsis whitespace-nowrap`}
    >
      <svg
        className={`-ml-0.5 mr-1.5 h-2 w-2 text-${color}-800`}
        fill="currentColor"
        viewBox="0 0 8 8"
      >
        <circle cx={4} cy={4} r={3} />
      </svg>
      {name}
    </span>
  )
}

export default Bardge
