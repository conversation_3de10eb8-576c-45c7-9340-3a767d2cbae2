import { SecurePassword } from "@blitzjs/auth"
import { resolver } from "@blitzjs/rpc"
import db from "db"

function sortData(data) {
  if (data && data.length > 0) {
    return data.sort((a, b) => b.reverseDependencies.length - a.reverseDependencies.length)
  } else {
    return data
  }
}

function removeDefault(str) {
  if (str.endsWith("#default")) {
    return str.slice(0, -8)
  } else {
    return str
  }
}

export default async function queryAppList(_: any, ctx: any) {
  let result
  let arr = [] as any
  if (_.pid) {
    if (_.pid !== "all") {
      result = await db.projects.findMany({
        where: {
          pid: _.pid,
          // isDeleted: false,
        },
        select: { id: true, title: true, repoName: true, pic: true, type: true, pid: true },
      })
    } else {
      console.log("db.projects.findMany({})")
      result = await db.projects.findMany({
        select: { id: true, title: true, repoName: true, pic: true, type: true, pid: true },
      })
    }
  } else {
    result = await db.projects.findFirst({
      where: {
        id: parseInt(_.id),
        // isDeleted: false,
      },
      include: {
        userProjectSettings: {
          where: {
            userId: ctx.session.userId,
          },
          select: {
            localPath: true,
          },
        },
      },
    })

    if (result && result.userProjectSettings && result.userProjectSettings.length > 0) {
      result.localPath = result.userProjectSettings[0].localPath
    }
    delete result.userProjectSettings
  }

  console.log("result", result)

  if (_.id && !_.pid) {
    console.log("sortedData")
    // let arr = [];
    let sortedData = sortData(result?.info || {})

    console.log("sortedData", result?.info)
    if (result && result.info) {
      sortedData &&
        sortedData.length &&
        sortedData.forEach((item: any) => {
          let type = "Untagged"
          if (item?.name.indexOf("@/components") != -1) {
            type = "custom"
          } else if (item?.id.indexOf("@tarojs/components") !== -1) {
            type = "core"
          } else {
            type = "external"
          }
          let _reverseDependencies = [] as any
          if (item.reverseDependencies.length > 0) {
            item.reverseDependencies.forEach((jsonString) => {
              // console.log('rr~~~~~~', r, JSON.parse(r));
              const obj = JSON.parse(jsonString)
              const path = obj.from.source.source.path
              _reverseDependencies.push(path)
              // _reverseDependencies.push({

              // })
            })
            arr.push({
              name: removeDefault(item.id),
              id: item.id,
              reverseDependencies: _reverseDependencies,
              count: item.reverseDependencies.length,
              type: type,
            })
          }
        })
    }

    // core external
    //custom
    //Untagged

    return {
      status: true,
      entry: result,
      hasAuth: ctx.session.userId == 3,
      info: arr || [],
    }
  } else {
    return {
      status: true,
      entry: result,
      // hasAuth: ctx.session.userId == 3,
      // info: arr || [],
    }
  }
}
