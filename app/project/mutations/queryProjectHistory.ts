import { SecurePassword } from "@blitzjs/auth"
import { resolver } from "@blitzjs/rpc"
import db from "db"

export default async function queryProjectHistory(_: any, ctx: any) {
  const result = await db.task.findMany({
    where: {
      isDeleted: false,
      AND: [
        //
        {
          data: {
            path: ["project"],
            array_contains: parseInt(_.id),
          },
        },
        {
          data: {
            path: ["progressType"],
            array_contains: 3,
          },
        },
      ],
    },
  })

  return {
    status: true,
    entry: result,
  }
}
