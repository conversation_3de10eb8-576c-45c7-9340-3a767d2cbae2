import { SecurePassword } from "@blitzjs/auth"
import { resolver } from "@blitzjs/rpc"
const { spawn } = require("child_process")
import db from "db"

import { promises as fs } from "fs" // 引入 fs 模块

export async function getJsonData() {
  const data = (await fs.readFile(`${process.cwd()}/data.json`)) as any // 读取文件
  return JSON.parse(data) // 将文件内容解析为 JSON 格式并返回
}

function getLastSegment(s) {
  // 查找最后一个斜杠的位置
  const index = s.lastIndexOf("/")
  // 如果没有找到斜杠，直接返回原字符串
  if (index < 0) {
    return s
  }
  // 使用substring()方法获取最后一个斜杠后的子串
  return s.substring(index + 1)
}

// 下载代码
const downloadCode = async (repoUrl, folderName) => {
  const downloadPath = `${process.cwd()}/${folderName}`

  const gitClone = spawn("git", ["clone", repoUrl, downloadPath])

  gitClone.on("close", (code) => {
    if (code === 0) {
      console.log("Code download finished.")
    } else {
      console.log("Code download failed.")
    }

    // 下载完成
    // console.log("cccc", `${process.cwd()}/magic/run.js`, downloadPath)
    // spawn("node", [`${process.cwd()}/magic/run.js`, downloadPath])
  })
}

export default async function updateProjects(_: any, ctx: any) {
  console.log("~~~~~~", ctx.session.role, ctx.session.userId)

  if (ctx.session.role.indexOf("FE_PROJECT_UPDATE") == -1) {
    return {
      status: false,
      message: "没有权限",
      entry: [],
    }
  }

  const runScript = (callback) => {
    const downloadPath = `${process.cwd()}/magic/code/${getLastSegment(_.repoName)}`
    const nodeProcess = spawn("node", [`${process.cwd()}/magic/run.js`, downloadPath])

    let result = ""

    nodeProcess.stdout.on("data", (data) => {
      result += data.toString()
    })

    nodeProcess.stderr.on("data", (data) => {
      console.error(`stderr: ${data}`)
    })

    nodeProcess.on("close", (code) => {
      if (code !== 0) {
        console.error(`Process exited with code ${code}.`)
      }
      callback(result)
    })

    nodeProcess.on("error", (error) => {
      console.error(`Failed to start child process: ${error}`)
    })
  }

  await downloadCode(
    "**********************:" + _.repoName,
    `/magic/code/${getLastSegment(_.repoName)}`
  )

  runScript((result) => {
    // console.log("99", result) // 子进程输出结果
  })

  await new Promise((resolve) => setTimeout(resolve, 3000)) // 等待 2s

  setTimeout(async () => {
    const _json = await getJsonData()
    console.log("_json,components", _.repoName, _json.components.length)

    let result = await db.projects.update({
      data: {
        info: _json.components,
      },
      where: {
        id: parseInt(_.id),
        // isDeleted: false,
      },
    })
  }, 3000)

  await new Promise((resolve) => setTimeout(resolve, 3000)) // 等待 2s

  // setTimeout(() => {
  //   const child = spawn("node", [`${process.cwd()}/magic/run.js`, downloadPath])
  // }, 1000)

  // let result = await db.projects.update({
  //   data: {
  //     content: _.content,
  //   },
  //   where: {
  //     id: parseInt(_.id),
  //     // isDeleted: false,
  //   },
  // })

  return {
    status: true,
    entry: [],
  }
}
