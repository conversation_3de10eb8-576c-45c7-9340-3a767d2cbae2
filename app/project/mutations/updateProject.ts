import { SecurePassword } from "@blitzjs/auth"
import { resolver } from "@blitzjs/rpc"
import db from "db"

export default async function updateProjects(_: any, ctx: any) {
  let result = await db.projects.update({
    data: {
      content: _.content,
      devUrl: _.devUrl || null,
      dailyUrl: _.dailyUrl || null,
      grayUrl: _.grayUrl || null,
      prodUrl: _.prodUrl || null,
      panguAppId: _.panguAppId || null,
      // gitUrl: _.gitUrl || null,
    },
    where: {
      id: parseInt(_.id),
      // isDeleted: false,
    },
  })

  if (_.localPath) {
    console.log("updateProjects", _.localPath, ctx.session.userId)
    await db.userProjectSettings.upsert({
      where: {
        userId_projectId: {
          userId: ctx.session.userId,
          projectId: parseInt(_.id),
        },
      },
      update: {
        localPath: _.localPath,
      },
      create: {
        userId: ctx.session.userId,
        projectId: parseInt(_.id),
        localPath: _.localPath,
      },
    })
  }

  return {
    status: true,
    entry: result,
  }
}
