import { Ctx } from "blitz"
import db from "db"

interface UpdateProfileInput {
  nickName?: string
  avatar?: string
}

export default async function updateUserProfile(
  { nickName, avatar }: UpdateProfileInput,
  { session }: Ctx
) {
  if (!session.userId) return null

  const updatedUser = await db.user.update({
    where: { id: session.userId },
    data: {
      nickName,
      avatar,
    },
    select: { nickName: true, avatar: true },
  })

  return {
    status: true,
  }
}
