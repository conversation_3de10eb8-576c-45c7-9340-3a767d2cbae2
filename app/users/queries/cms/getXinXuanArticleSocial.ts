import { Ctx } from "blitz"
import db from "db"

export default async function getXinXuanArticleSocial(_ = null, { session }: Ctx) {
  if (!session.userId) return null
  const user = await db.articleSocial.findMany()

  return {
    status: user,
    entry: user,
  }
}

// export default async function getXinXuanArticleSocial() {
//   const user = await db.articleSocial.findMany({
//     where: {},
//     select: {},
//   })
//   return user
// }
