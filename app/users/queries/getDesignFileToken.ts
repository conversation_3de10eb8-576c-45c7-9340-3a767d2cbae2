import { Ctx } from "blitz"
import db from "db"
const crypto = require("crypto")
const expAfter = 8 * 60 * 60 * 1000

const config = {
  dirPath: "xdesign/", //
  bucket: "public-xincheng-assets",
  region: "oss-cn-hangzhou", //
  accessKeyId: process.env.OSS_ACCESS_KEY_ID || "",
  accessKeySecret: process.env.OSS_ACCESS_KEY_SECRET || "",
  maxSize: 50 * 1024 * 1024, //
  expAfter,
}

const host = `https://${config.bucket}.${config.region}.aliyuncs.com`

// 生成token
function createToken() {
  const expireTime = new Date().getTime() + config.expAfter
  const expiration = new Date(expireTime).toISOString()
  const policyString = JSON.stringify({
    expiration,
    conditions: [
      ["content-length-range", 0, config.maxSize],
      ["starts-with", "$key", config.dirPath],
    ],
  })
  const policy = Buffer.from(policyString).toString("base64")
  const signature = crypto
    .createHmac("sha1", config.accessKeySecret)
    .update(policy)
    .digest("base64")
  return {
    signature,
    policy,
    host,
    OSSAccessKeyId: config.accessKeyId,
    success_action_status: 200,
    dirPath: config.dirPath,
  }
}

export default async function getCurrentUser(_ = null, { session }: Ctx) {
  if (!session.userId) return null

  //   const user = await db.user.findFirst({
  //     where: { id: session.userId as number },
  //     select: { id: true, name: true, email: true, role: true },
  //   })

  return createToken()
}
