import { SecurePassword } from "@blitzjs/auth"
import { resolver } from "@blitzjs/rpc"
import db from "db"
import { Role } from "types"
import dayjs from "dayjs"

export default async function submitFENews(_: any, ctx: any) {
  // ctx.session.$isAuthorized(true)
  // ctx.session.$setPublicData()
  // ctx.session.$setPrivateData()

  // console.log(_, ctx)

  // const _time = dayjs(new Date()).format("YYYY-MM-DD HH:mm:ss")
  // const data = await db.FENews.create({
  //   data: {
  //     timeStr: _time,
  //     authorId: ctx.session.userId,
  //     content: _.content,
  //   },
  // })
  // return {
  //   status: true,
  //   entry: data,
  // }
}
