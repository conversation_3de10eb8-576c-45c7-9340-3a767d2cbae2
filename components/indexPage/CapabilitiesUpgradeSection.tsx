
import React from "react"
import { FeatureGrid } from 'oocode'


const CapabilitiesUpgradeSection = () => {

  const features = [
    {
      title: "掌控全局，打造SSR页面盛宴",
      description: "我们的SSR页面内容管理功能，让您达到高效管理页面内容的目标;把用户体验做到极致，为用户带来独具特色的感官视觉。",
      iconName: "Zap"
    },
    {
      title: "掌握未来，引领新篇章",
      description: "平台中的人工智能辅助工具，为您提供无限可能性。我们将未来已经展现在您眼前，让您成为时代的引领者，赢得未来的竞争优势",
      iconName: "Grid"
    },
    {
      title: "聚智共享，星辰探索",
      description: "我们倡导知识共享，团队知识星球是我们为此而打造的平台。在智慧的星辰海洋中，开创属于自己的新旅程。",
      iconName: "Book"
    },
    {
      title: "汲取智慧，沉淀人生",
      description: "业务项目沉淀文档是您的精神资产，这里记录着您创造的价值和收获的成果。通过记录和相互学习，我们将共同成长，共创美好未来。",
      iconName: "Terminal"
    }
  ];

  return (
    <FeatureGrid
      title="全能升级"
      subtitle="新一代全面提效平台的典范之作，协同与效率跨界再突破。更出色的跨部门工具，为开发与运营团队带来协同的美好新时代；简洁的界面设计，让非开发人员也能轻松上手；智能的数据管理，助力运营决策更精准；还有丰富的学习资源，让团队成长蜕变不设限。"
      features={features}
    />
  );
}

export default CapabilitiesUpgradeSection

