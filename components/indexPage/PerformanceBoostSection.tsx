import React from "react"
import { FeatureSection } from 'oocode'

const PerformanceBoostSection = () => {
  const features = [
    {
      title: "Project Management",
      description: "一览无余的全景项目视图.任凭你的辛勤劳动，哪怕只是点滴，我们都将如实记录",
      iconName: "Zap"
    },
    {
      title: "Web-based JSON Configuration",
      description: "告别传统发布流程，轻松在平台上一键发布配置文件",
      iconName: "Grid"
    },
    {
      title: "Supports TinyPNG",
      description: "让您无需担心繁琐的图片压缩工作，同时也能保证上传速度和品质，让您的应用更加精美高效.",
      iconName: "Book"
    },
    {
      title: "Online Theme Customization",
      description: "释放你的创意，使用我们优雅而直观的在线主题构建器，轻松地打造你的网站，将你的愿景无缝呈现.",
      iconName: "Terminal"
    }
  ];

  return (
    <FeatureSection
      title="效能飞跃"
      subtitle="Flow 是新一代前端内部提效平台，协同和工程效率在旧有基础上重塑辉煌。更卓越的自动化工具，无需繁琐配置即可提升工作效率；智能的项目管理，让任务调度变得轻而易举；创新的模块化设计，带来无与伦比的开发体验；还有丰富的培训资源，助力团队成长迅速升级。"
      features={features}
    />
  );
}

export default PerformanceBoostSection

