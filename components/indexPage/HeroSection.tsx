import React, { useState, useRef, useEffect } from "react"
import { Router, useRouter } from "next/router"
import { ChevronRightIcon } from "@heroicons/react/20/solid"
import { ShineImageSection } from 'oocode';

const NewComponent = () => {

  const router = useRouter()

  const handleMouseMove = (e) => {
    const rect = e.target.getBoundingClientRect(),
      x = e.clientX - rect.left,
      y = e.clientY - rect.top

    e.target.style.setProperty("--mouse-x", `${x}px`)
    e.target.style.setProperty("--mouse-y", `${y}px`)
  }


  return (
    <div
      className="relative isolate overflow-hidden"
      id="cursor"
      onMouseMove={handleMouseMove}
    >
      <svg
        className="absolute inset-0 -z-10 h-full w-full stroke-white/10 [mask-image:radial-gradient(100%_100%_at_top_right,white,transparent)]"
        aria-hidden="true"
      >
        <defs>
          <pattern
            id="983e3e4c-de6d-4c3f-8d64-b9761d1534cc"
            width={200}
            height={200}
            x="50%"
            y={-1}
            patternUnits="userSpaceOnUse"
          >
            <path d="M.5 200V.5H200" fill="none" />
          </pattern>
        </defs>
        <svg x="50%" y={-1} className="overflow-visible fill-gray-800/20">
          <path
            d="M-200 0h201v201h-201Z M600 0h201v201h-201Z M-400 600h201v201h-201Z M200 800h201v201h-201Z"
            strokeWidth={0}
          />
        </svg>
        <rect
          width="100%"
          height="100%"
          strokeWidth={0}
          fill="url(#983e3e4c-de6d-4c3f-8d64-b9761d1534cc)"
        />
      </svg>
      <div
        className="absolute left-[calc(50%-4rem)] top-10 -z-10 transform-gpu blur-3xl sm:left-[calc(50%-18rem)] lg:left-48 lg:top-[calc(50%-30rem)] xl:left-[calc(50%-24rem)]"
        aria-hidden="true"
      >
        <div
          className="aspect-[1108/632] w-[69.25rem] bg-gradient-to-r from-[#80caff] to-[#4f46e5] opacity-20"
          style={{
            clipPath:
              "polygon(73.6% 51.7%, 91.7% 11.8%, 100% 46.4%, 97.4% 82.2%, 92.5% 84.9%, 75.7% 64%, 55.3% 47.5%, 46.5% 49.4%, 45% 62.9%, 50.3% 87.2%, 21.3% 64.1%, 0.1% 100%, 5.4% 51.1%, 21.4% 63.9%, 58.9% 0.2%, 73.6% 51.7%)",
          }}
        />
      </div>
      <div className="mx-auto max-w-7xl px-6 pb-24 pt-10 sm:pb-40 lg:flex lg:px-8 lg:pt-40">
        <div className="mx-auto max-w-2xl flex-shrink-0 lg:mx-0 lg:max-w-xl lg:pt-8">
          <img
            className="h-11"
            src="https://s.xinc818.com/files/webcim1ynyszfm6e33d/mark.svg"
            alt="Your Company"
          />
          <div className="mt-24 sm:mt-32 lg:mt-16">
            <a href="javascript:void(0)" className="inline-flex space-x-6">
              <span className="rounded-full bg-indigo-500/10 px-3 py-1 text-sm font-semibold leading-6 text-indigo-400 ring-1 ring-inset ring-indigo-500/20">
                最近更新
              </span>
              <span className="relative z-999 inline-flex items-center space-x-2 text-sm font-medium leading-6 text-gray-300">
                <span>
                  刚刚发布v2.0.0
                </span>
              </span>
            </a>
          </div>
          <h1 className="mt-10 text-4xl font-bold tracking-tight text-white sm:text-6xl">
            Front-end development made simple for businesses
            {/* Deploy to the cloud with confidence */}
          </h1>
          <p className="mt-6 text-lg leading-8 text-gray-300">
            我们的目标是提供优秀的技术解决方案，助力公司业务成长，同时打造开放的技术生态，推动集团前端技术的发展.
          </p>
          <div className="mt-10 flex items-center gap-x-6">
            <a
              href="/home"
              className="relative z-999 rounded-md bg-indigo-500 px-3.5 py-2.5 text-sm font-semibold text-white shadow-sm hover:bg-indigo-400 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-indigo-400"
            >
              开始使用
            </a>
            <a
              href="/auth/login"
              className="relative z-999 text-sm font-semibold leading-6 text-white"
            >
              Login In <span aria-hidden="true">→</span>
            </a>
          </div>
        </div>
        <div className="mx-auto mt-16 flex max-w-2xl sm:mt-24 lg:ml-10 lg:mr-0 lg:mt-0 lg:max-w-none lg:flex-none xl:ml-32">
          <div className="max-w-3xl flex-none sm:max-w-5xl lg:max-w-none">
            <ShineImageSection
              imageUrl="https://s.xinc818.com/files/webcilibeeigtdv3ry4tinty/<EMAIL>"
              imageWidth={2432}
              imageHeight={1442}
              altText="App screenshot"
            />
          </div>
        </div>
      </div>
    </div>
  )
}

export default NewComponent
