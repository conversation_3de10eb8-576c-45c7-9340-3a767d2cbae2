import React, { Fragment, useState, useRef, useEffect, useCallback } from "react";
import { useMutation } from "@blitzjs/rpc";
import dayjs from "dayjs";
import createNewsMutation from "app/news/mutations/createNews";
import queryNewsMutation from "app/news/mutations/queryNews";
import deleteNewsMutation from "app/news/mutations/deleteNews";
import { Popconfirm } from "antd";
import { Dialog, Transition } from "@headlessui/react";
import { XMarkIcon } from "@heroicons/react/24/outline";
import { showNotification } from "@mantine/notifications";

// 获取新闻数据的自定义 Hook
const useFetchNews = () => {
  const [newsList, setNewsList] = useState<any[]>([]);
  const [isLoaded, setIsLoaded] = useState(false);

  const [queryNews] = useMutation(queryNewsMutation, {});

  const fetchNews = useCallback(async () => {
    const response = await queryNews({});
    if (response.entry) {
      setNewsList(response.entry);
      setIsLoaded(true);
    }
  }, [queryNews]);

  useEffect(() => {
    void fetchNews();
  }, [fetchNews]);

  return { newsList, isLoaded, fetchNews };
};


// 格式化文本的函数，主要用于处理 URL 链接和换行符
const formatTextWithLinks = (text: string) => {
  const urlRegex = /(https?:\/\/[^\s]+)/g;
  return text
    .replace(urlRegex, (url) => `<a href="${url}" target="_blank" class="flow-shine-link" rel="noopener noreferrer">${url}</a>`)
    .replace(/\r?\n/g, '<br/>');
};

// 单个新闻项组件
const NewsItem = ({ newsItem, onClick }: { newsItem: any; onClick: (item: any) => void }) => {
  const author = newsItem.author;

  return (
    <li
      key={newsItem.id}
      className="cursor-pointer px-4 py-4 hover:bg-gray-950 sm:px-6 lg:px-8"
      onClick={(e) => {
        e.stopPropagation();
        onClick(newsItem);
      }}
    >
      <div className="flex items-center gap-x-3">
        <img
          src={author?.avatar || "https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?ixlib=rb-1.2.1&ixid=eyJhcHBfaWQiOjEyMDd9&auto=format&fit=facearea&facepad=2&w=256&h=256&q=80"}
          className="h-6 w-6 flex-none rounded-full bg-gray-800"
          alt="avatar"
        />
        <h3 className="relative top-1 flex-auto truncate text-sm font-semibold leading-6 text-white">
          {newsItem?.author?.name || newsItem?.fe}
        </h3>
        <time dateTime={newsItem.timeStr} className="flex-none text-xs text-gray-400">
          {dayjs(newsItem.createdAt).format("YYYY年MM月DD日")}
        </time>
      </div>
      <p className="mt-3 truncate text-sm text-gray-500">
        <span className="text-gray-300">{newsItem.content || `本周《${newsItem.title}》已更新`}</span>
      </p>
    </li>
  );
};

// 新闻列表组件
const NewsList = ({ newsList, onNewsItemClick }: { newsList: any[]; onNewsItemClick: (item: any) => void }) => {
  return (
    <ul role="list" className="ddd h-[calc(100vh-85px)] divide-y divide-white/5 overflow-auto">
      {newsList?.length > 0 && newsList.map((newsItem) => <NewsItem key={newsItem.id} newsItem={newsItem} onClick={onNewsItemClick} />)}
    </ul>
  );
};

// 新闻详情对话框组件
const NewsDialog = ({ selectedNews, isDialogOpen, setDialogOpen, deleteNews, refreshNews }: any) => {
  // const initialFocusRef = useRef(null);
  const handleDelete = async () => {
    const response = await deleteNews({ id: selectedNews.id });
    if (response && response.status) {
      showNotification({
        title: "Notification",
        message: "删除成功",
        color: "green",
      });
      await refreshNews();
      setDialogOpen(false);
    } else {
      showNotification({
        title: "Notification",
        message: "删除失败",
        color: "red",
      });
    }
  };

  return (
    <Transition.Root show={isDialogOpen} as={Fragment}>
      <Dialog as="div" className="relative z-999" onClose={() => setDialogOpen(false)}>
        <div className="fixed inset-0" />
        <div className="fixed inset-0 ">
          <div className="absolute inset-0 overflow-hidden">
            <div className="pointer-events-none fixed inset-y-0 right-0 flex max-w-full pl-10">
              <Transition.Child
                as={Fragment}
                enter="transform transition ease-in-out duration-300 sm:duration-300"
                enterFrom="translate-x-full"
                enterTo="translate-x-0"
                leave="transform transition ease-in-out duration-300 sm:duration-300"
                leaveFrom="translate-x-0"
                leaveTo="translate-x-full"
              >
                <Dialog.Panel className="pointer-events-auto w-screen max-w-md">
                  <div className="flex h-full flex-col overflow-y-scroll bg-white shadow-xl">
                    <div className="bg-indigo-700 px-4 py-6 sm:px-6">
                      <div className="flex items-center justify-between">
                        <Dialog.Title className="text-base font-semibold leading-6 text-white">
                          {selectedNews?.author?.name} ({selectedNews?.author?.email})
                        </Dialog.Title>
                        <div className="ml-3 flex h-7 items-center">
                          {selectedNews?.showDeleteBtn && (
                            <Popconfirm placement="topRight" title={"确认删除此条分享吗?"} onConfirm={handleDelete} okText="Yes" cancelText="No">
                              <span className="right-15 absolute top-16 cursor-pointer text-white opacity-60">
                                <svg
                                  xmlns="http://www.w3.org/2000/svg"
                                  fill="none"
                                  viewBox="0 0 24 24"
                                  strokeWidth={1.5}
                                  stroke="currentColor"
                                  className="h-6 w-6"
                                >
                                  <path
                                    strokeLinecap="round"
                                    strokeLinejoin="round"
                                    d="M14.74 9l-.346 9m-4.788 0L9.26 9m9.968-3.21c.342.052.682.107 1.022.166m-1.022-.165L18.16 19.673a2.25 2.25 0 01-2.244 2.077H8.084a2.25 2.25 0 01-2.244-2.077L4.772 5.79m14.456 0a48.108 48.108 0 00-3.478-.397m-12 .562c.34-.059.68-.114 1.022-.165m0 0a48.11 48.11 0 013.478-.397m7.5 0v-.916c0-1.18-.91-2.164-2.09-2.201a51.964 51.964 0 00-3.32 0c-1.18.037-2.09 1.022-2.09 2.201v.916m7.5 0a48.667 48.667 0 00-7.5 0"
                                  />
                                </svg>
                              </span>
                            </Popconfirm>
                          )}
                          <button type="button" className="rounded-md bg-indigo-700 text-indigo-200 hover:text-white focus:outline-none focus:ring-2 focus:ring-white" onClick={() => setDialogOpen(false)}>
                            <span className="sr-only">Close panel</span>
                            <XMarkIcon className="h-6 w-6" aria-hidden="true" />
                          </button>
                        </div>
                      </div>
                      <div className="mt-1">
                        <p className="text-sm text-indigo-300">{selectedNews?.timeStr || "-"}</p>
                      </div>
                    </div>
                    <div className="dark:bg-gray-850 relative flex-1 break-words bg-gray-800 px-4 py-6 text-white sm:px-6" dangerouslySetInnerHTML={{ __html: formatTextWithLinks(selectedNews?.content || '') }} />
                  </div>
                </Dialog.Panel>
              </Transition.Child>
            </div>
          </div>
        </div>
      </Dialog>
    </Transition.Root>
  );
};

// 主组件
const NewsComponent = () => {
  const [isEditorOpen, setIsEditorOpen] = useState(false);
  const [isDialogVisible, setIsDialogVisible] = useState(false);
  const [inputText, setInputText] = useState("");
  const [selectedNewsItem, setSelectedNewsItem] = useState<any>(null);

  const { newsList, isLoaded, fetchNews } = useFetchNews();

  const [createNews] = useMutation(createNewsMutation, {});
  const [deleteNews] = useMutation(deleteNewsMutation, {});

  const handleSendNews = async () => {
    if (inputText.length === 0) return;

    setIsEditorOpen(false);
    const response = await createNews({ content: inputText });
    if (response && response.status) {
      setInputText("");
      await fetchNews();
    } else {
      showNotification({ title: "notification", message: "发送失败", color: "red" });
    }
  };

  const handleNewsItemClick = (item: any) => {
    if (item.title) {
      window.open(`http://flow.xinc818.com/lanlan/sharing-session-editor?id=${item.id}&mode=view`);
    } else {
      setSelectedNewsItem(item);
      setIsDialogVisible(true)
    }
  };

  if (!isLoaded) {
    return null;
  }

  return (
    <>
      <aside className="bg-black/10 lg:fixed lg:bottom-0 lg:right-0 lg:top-0 lg:w-96 lg:overflow-y-hidden lg:border-l lg:border-white/5">
        <header className="flex items-center justify-between border-b border-white/5 px-4 py-4 sm:px-6 sm:py-6 lg:px-8">
          <h2 className="text-base font-semibold leading-7 text-white">近期消息</h2>
          <a href="#" className="text-sm font-semibold leading-6 text-indigo-400"></a>
        </header>
        <NewsList newsList={newsList} onNewsItemClick={handleNewsItemClick} />
      </aside>
      <NewsDialog selectedNews={selectedNewsItem} isDialogOpen={isDialogVisible} setDialogOpen={setIsDialogVisible} deleteNews={deleteNews} refreshNews={fetchNews} />
      <div className="fixed bottom-10 right-14">
        {!isEditorOpen ? (
          <button className="cursor-pointer rounded-full bg-indigo-500 p-4 text-white shadow-md focus:outline-none" onClick={() => setIsEditorOpen(!isEditorOpen)}>
            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" className="h-6 w-6">
              <path strokeLinecap="round" strokeLinejoin="round" d="M7.5 8.25h9m-9 3H12m-9.75 1.51c0 1.6 1.123 2.994 2.707 3.227 1.129.166 2.27.293 3.423.379.35.026.67.21.865.501L12 21l2.755-4.133a1.14 1.14 0 01.865-.501 48.172 48.172 0 003.423-.379c1.584-.233 2.707-1.626 2.707-3.228V6.741c0-1.602-1.123-2.995-2.707-3.228A48.394 48.394 0 0012 3c-2.392 0-4.744.175-7.043.513C3.373 3.746 2.25 5.14 2.25 6.741v6.018z" />
            </svg>
          </button>
        ) : (
          <div className="mr-4 mt-4 w-96 rounded-md bg-white p-4 shadow-lg">
            <div className="mb-4">
              <textarea
                className="w-full rounded-md border border-gray-300 p-2 focus:outline-none focus:ring-2 focus:ring-blue-400"
                rows={4}
                placeholder="输入您的分享内容(发错可删)"
                value={inputText}
                onChange={(e) => setInputText(e.target.value)}
              />
            </div>
            <button className={`mr-2 rounded-md bg-indigo-500 px-4 py-2 text-white shadow-md focus:outline-none ${inputText.length > 0 ? "ccc" : "cursor-default opacity-50"}`} onClick={handleSendNews}>
              发送 🎉
            </button>
            <button className="rounded-md bg-indigo-500 px-4 py-2 text-white shadow-md focus:outline-none" onClick={() => setIsEditorOpen(false)}>
              取消
            </button>
          </div>
        )}
      </div>
    </>
  );
};

export default React.memo(NewsComponent);
