import React, { useState, useCallback, useEffect } from "react";
import { useMutation } from "@blitzjs/rpc";
import { useRouter } from "next/router";
import dayjs from "dayjs";
import { ChevronRightIcon } from "@heroicons/react/24/outline";
import getHotTasks from "app/task/mutations/queryHotTask";

const taskEnum = {
  type: {
    1: "项目",
    2: "日常",
  },
  category: {
    1: "供应链中台",
    2: "设计师平台",
    3: "集团需求支撑",
    4: "产品研发中心",
    99: "其他",
  },
  classification: {
    1: "业务驱动",
    2: "技术驱动",
  },
  team: {
    "1": "后端开发部",
    "2": "质量保障组",
    "3": "前端组",
    "4": "UED",
    "5": "架构部",
  },
  progressType: {
    1: "未开始",
    2: "进行中",
    3: "已上线",
    4: "已暂停",
  },
  quality: {
    1: "暂无",
    2: "差",
    3: "一般",
    4: "较好",
    5: "好",
  },
};

interface Task {
  id: string;
  title: string;
  category: number;
  releaseTime: string;
  progressType: number;
  [key: string]: any;
}

const statuses = {
  1: "text-gray-500 bg-gray-100/10",
  2: "text-blue-400 bg-blue-400/10",
  3: "text-green-400 bg-green-400/10",
  4: "text-rose-400 bg-rose-400/10",
};

const environments = {
  1: "text-gray-400 bg-gray-600/30 ring-gray-700",
  2: "text-gray-400 bg-gray-600/30 ring-gray-700",
  3: "text-indigo-400 bg-indigo-400/10 ring-indigo-400/30",
  4: "text-gray-400 bg-gray-600/30 ring-gray-700",
};

const classNames = (...classes: string[]) => classes.filter(Boolean).join(" ");

const TaskListSection: React.FC = () => {
  const [data, setData] = useState<Task[]>([]);
  const router = useRouter();
  const [getHotTasksMutation] = useMutation(getHotTasks);

  const getTableData = useCallback(async () => {
    const res = await getHotTasksMutation({});
    if (res.entry) {
      const tasks = res.entry.map(({ id, data }: any) => ({ ...data, id }));
      setData(tasks);
    }
  }, [getHotTasksMutation]);

  useEffect(() => {
    void getTableData();
  }, [getTableData]);

  const handleClick = (id: string) => {
    void router.push({
      pathname: "/task/task_editor",
      query: { id, from: "EXTERNAL_SOURCE" },
    });
  };

  return (
    <main className="overflow-hidden lg:pr-96">
      <header className="flex items-center justify-between border-b border-white/5 px-4 py-4 sm:px-6 sm:py-6 lg:px-8">
        <h1 className="text-base font-semibold leading-7 text-white">近期项目</h1>
      </header>
      <ul role="list" className="h-[calc(100vh-85px)] divide-y divide-white/5 overflow-auto">
        {data.map(({ id, title, category, releaseTime, progressType, pd, ued, fe, be, tester }: Task) => (
          <li
            key={id}
            onClick={() => handleClick(id)}
            className="relative flex cursor-pointer items-center space-x-4 px-4 py-4 hover:bg-gray-950 sm:px-6 lg:px-8"
          >
            <div className="min-w-0 flex-auto">
              <div className="flex items-center gap-x-3">
                <div className={classNames(statuses[progressType], "flex-none rounded-full p-1")}>
                  <div className="h-2 w-2 rounded-full bg-current" />
                </div>
                <h2 className="relative top-1 min-w-0 text-sm font-semibold leading-6 text-white">
                  <a className="flex gap-x-2">
                    <span className="truncate">{taskEnum.category[category]}</span>
                    <span className="text-gray-400">/</span>
                    <span className="whitespace-nowrap">{title}</span>
                  </a>
                </h2>
              </div>
              <div className="mt-3 flex items-center gap-x-2.5 text-xs leading-5 text-gray-400">
                <p className="flex w-5/6 gap-x-3 whitespace-nowrap">
                  <span className="truncate">发布时间: {releaseTime ? dayjs(releaseTime).format("YYYY-MM-DD") : " - "}</span>
                  {pd?.length > 0 && <span>产品: {pd.join("、")}</span>}
                  {ued?.length > 0 && <span>UED: {ued.join("、")}</span>}
                  {fe?.length > 0 && <span>前端: {fe.join("、")}</span>}
                  {be?.length > 0 && <span>后端: {be.join("、")}</span>}
                  {tester?.length > 0 && <span>测试: {tester.join("、")}</span>}
                </p>
              </div>
            </div>
            <div className={classNames(environments[progressType], "flex-none rounded-full px-2 py-1 text-xs font-medium ring-1 ring-inset")}>
              {taskEnum.progressType[progressType]}
            </div>
            <ChevronRightIcon className="h-5 w-5 flex-none text-gray-400" aria-hidden="true" />
          </li>
        ))}
      </ul>
    </main>
  );
};

export default TaskListSection;
