@tailwind base;
@tailwind components;
@tailwind utilities;
@import "./typography.css";

body {
  margin: 0;
  padding: 0;
  overflow-x: hidden;
}

/* antd全局覆盖 */
:where(.css-dev-only-do-not-override-htwhyh).ant-btn-primary {
  color: #fff !important;
  background-color: #1677ff !important;
}
.ant-btn > span.anticon {
  display: inline-flex;
}
.ant-switch {
  background-color: rgba(0, 0, 0, 0.2) !important;
}
.ant-switch-checked {
  background-color: #1677ff !important;
}
.ant-btn-primary {
  background-color: #4f46e5;
}

/*.mantine-Button-root */
.mantine-10v2ems:hover,
.mantine-10v2ems {
  /* color: #4f46e5 !important; */
  color: rgb(79, 70, 229);
  cursor: pointer;
}
.mantine-czn2bj,
.mantine-8nr514 {
  background-color: #4f46e5 !important;
  color: #fff !important;
}
.mantine-10v2ems,
.mantine-10v2ems:hover {
  background-color: transparent;
}
.mantine-xe59jl {
  background-color: transparent;
  color: #4f46e5;
}
.mantine-j2x5jx {
  padding-top: 0px;
  padding-bottom: 0;
  padding-right: 0;
  padding-left: 288px;
}

/* 隐藏滚动条 */
#home ul::-webkit-scrollbar {
  display: none;
}

.dark {
  --pink-track: linear-gradient(
    0deg,
    rgb(255 255 255/0) 0%,
    hsla(243, 75%, 30%, 0.5) 15%,
    hsla(243, 75%, 30%, 0.5) 15%,
    rgb(255 255 255/0) 100%
  );
  --blue-track: linear-gradient(
    0deg,
    rgb(255 255 255/0) 0%,
    hsla(220, 62%, 25%, 0.5) 15%,
    hsla(220, 62%, 25%, 0.5) 85%,
    rgb(255 255 255/0) 100%
  );
  --green-track: linear-gradient(
    0deg,
    rgb(255 255 255/0) 0%,
    hsla(150, 60%, 13%, 0.5) 15%,
    hsla(150, 60%, 13%, 0.5) 85%,
    rgb(255 255 255/0) 100%
  );
  --blue-100: hsl(220, 55%, 13%);
  --blue-200: hsl(220, 62%, 25%);
  --blue-300: hsl(220, 68%, 35%);
  --blue-500: hsl(220, 80%, 55%);
  --blue-700: hsl(220, 80%, 75%);
  --background: hsl(250, 24%, 9%);
  --secondaryBg: hsl(250, 21%, 11%);
}

.border-blue-300 {
  border-color: var(--blue-300);
}

.dark .border-blue-200 {
  border-color: var(--blue-200);
}

.space-y-8 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(2rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(2rem * var(--tw-space-y-reverse));
}

.icon-md,
.icon-md svg {
  width: 20px;
  height: 20px;
}

.text-h3 {
  font-size: clamp(22px, 2.5vw, 24px);
  line-height: 1.375;
}

.text-blue-500 {
  color: var(--blue-500);
}

.border-m1 {
  --tw-text-opacity: 0.5;
  border-color: rgb(99 102 241 / var(--tw-text-opacity)) !important;
}

.border-m2 {
  --tw-text-opacity: 0.5;
  border-color: rgb(33 82 171 / var(--tw-text-opacity)) !important;
}

@font-face {
  font-family: "Inter";
  font-weight: 100 900;
  font-display: block;
  font-style: normal;
  font-named-instance: "Regular";
  src: url("/fonts/Inter-roman.var.woff2") format("woff2");
}
@font-face {
  font-family: "Inter";
  font-weight: 100 900;
  font-display: block;
  font-style: italic;
  font-named-instance: "Italic";
  src: url("/fonts/Inter-italic.var.woff2") format("woff2");
}
@font-face {
  font-family: "Mona Sans";
  font-weight: 200 900;
  font-display: block;
  font-style: normal;
  font-stretch: 75% 125%;
  src: url("/fonts/Mona-Sans.var.woff2") format("woff2");
}

#__next {
  flex: auto;
  display: flex;
  flex-direction: column;
}
.tdesign-source-content__iframe {
  border: 0;
  border-radius: var(--iframe-border-radius, 6px);
  display: block;
  height: var(--iframe-height, 576px);
  margin-top: -56px;
  /* max-height: 576px; */
  /* min-height: 376px; */
  height: 100%;
  padding: 0;
}

/* 动画版超链 */
.flow-link {
  color: #1677ff !important;
}
a.flow-shine-link {
  color: #1677ff !important;
  background: linear-gradient(0deg, #1677ff, #1677ff) no-repeat right bottom / 0 var(--bg-h);
  transition: background-size 350ms;
  --bg-h: 100%;
  padding-bottom: 2px;
  --bg-h: 2px;
}
a.flow-shine-link:where(:hover, :focus-visible) {
  background-size: 100% var(--bg-h);
  background-position-x: left;
}

*,
::before,
::after {
  box-sizing: border-box;
}

/* cursor */
#cursor {
  position: relative;
  cursor: default;
  opacity: 1;
}
#cursor:hover::before {
  opacity: 1;
}
#cursor::before,
#cursor::after {
  border-radius: inherit;
  content: "";
  height: 100%;
  left: 0px;
  opacity: 0;
  position: absolute;
  top: 0px;
  transition: opacity 500ms;
  width: 100%;
}
#cursor::before {
  background: radial-gradient(
    400px circle at var(--mouse-x) var(--mouse-y),
    rgba(255, 255, 255, 0.09),
    transparent 40%
  );
  z-index: 3;
}
#cursor::after {
  background: radial-gradient(
    400px circle at var(--mouse-x) var(--mouse-y),
    rgba(255, 255, 255, 0.4),
    transparent 40%
  );
  z-index: 1;
}

/* mobile-device */
.mobile-device {
  font-family: -apple-system, BlinkMacSystemFont, Segoe UI, PingFang SC, Hiragino Sans GB,
    Microsoft YaHei, Helvetica Neue, Helvetica, Arial, sans-serif, Apple Color Emoji, Segoe UI Emoji,
    Segoe UI Symbol;
  font-variant: tabular-nums;
  font-feature-settings: "tnum", "tnum";
  position: sticky;
  display: flex;
  flex-direction: column;
  margin-left: 58px;
  border-radius: 32px;
  overflow: hidden;
  width: 320px;
  min-width: 320px;
  height: 693.33333333px;
  box-shadow: 0 0 0 14px #090a0d, 0 0 0 16px #9fa3a8, 0 4px 20px 16px rgba(0, 0, 0, 0.1);
  top: 116px;
}
.mobile-device-status {
  height: 30px;
  color: #222;
  font-size: 12px;
  font-weight: 500;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}
.mobile-device-status span {
  display: inline-block;
  width: 60px;
}
.mobile-device-status span:nth-child(2) {
  text-align: center;
}
.mobile-device-status:after {
  content: "";
  display: inline-block;
  margin-left: 42px;
  width: 14px;
  height: 5px;
  border-radius: 1px;
  background: #50d664;
  box-shadow: 0 0 0 1px #fff, 0 0 0 2px #999;
}

.md-editor .md-editor-content h2 {
  font-size: 22px !important;
}

.progress-bar {
  position: fixed;
  top: 0;
  left: 288px;
  right: 0;
  height: 5px;
  z-index: 111;
  background: rgba(79, 70, 229, 0.8);
  transform-origin: 0%;
  display: none;
}

.mantine-ScrollArea-viewport > div > div > div::-webkit-scrollbar {
  display: none;
}

.documate-button {
  width: 60px;
  overflow: hidden;
  margin: 0 !important;
  padding: 0 !important;
  border: 0 none !important;
  outline: 0;
}
.documate-button svg {
  display: none;
}

.darklight-reference-promo {
  visibility: hidden;
  padding-bottom: 0px !important;
}

.expand-collapse-btn {
  display: none;
}

.hide-scrollbar {
  overflow-y: hidden;
}
.hide-behavior {
  overscroll-behavior-y: none;
}

.aone .ant-menu-submenu-selected:hover > .ant-menu-submenu-title,
.aone .ant-menu-submenu-selected > .ant-menu-submenu-title {
  background: #2a7ef3 !important;
  color: #fff !important;
}

.x-space {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 32 32' width='12' height='12' fill='none' stroke='rgb(0 0 0 / 0.1)'%3e%3cpath d='M0 .5H31.5V32'/%3e%3c/svg%3e");
}
