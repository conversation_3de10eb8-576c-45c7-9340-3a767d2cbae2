module.exports = require("@blitzjs/next/eslint")
module.exports = {
  ...require("@blitzjs/next/eslint"),
  rules: {
    ...require("@blitzjs/next/eslint").rules,
    "@typescript-eslint/no-floating-promises": "off", // 关闭 @typescript-eslint/no-floating-promises 规则
    "react/no-children-prop": "off", // 关闭react/no-children-prop规则
  },
  overrides: [
    {
      files: ["*.ts", "*.tsx"], // 适用于所有 TypeScript 文件
      rules: {
        "@typescript-eslint/no-floating-promises": "off", // 关闭 @typescript-eslint/no-floating-promises 规则
      },
    }
  ],
};


